# 小说人物档案管理系统 v3.0 Enhanced 使用说明

## 🌟 系统概述

小说人物档案管理系统 v3.0 Enhanced 是一个全功能的辅助创作工具，专为小说作者设计，帮助管理复杂的人物关系、事件时间线和角色成长轨迹。

### 核心特性

- **动态人物关系网络** - 支持关系强度可视化、历史追踪和动态更新
- **全局事件库系统** - 完整的事件管理，包括分类、搜索和时间线排序
- **角色里程碑追踪** - 记录角色成长节点，支持轨迹分析和可视化
- **交互式可视化界面** - 多种图表类型，支持实时交互和数据更新
- **性能优化** - 支持大数据量处理，包含缓存和索引优化
- **数据兼容性** - 支持从v2系统无缝迁移数据

## 🚀 快速开始

### 安装依赖

```bash
pip install networkx pandas plotly numpy
```

### 基础使用

```python
from 图v3 import NovelV3System

# 初始化系统
system = NovelV3System()

# 添加角色
char_id = system.add_character({
    'name': '李风',
    'prototype': '英雄',
    'narrative_role': '主角',
    'tags': ['正义', '勇敢']
})

# 添加关系
system.add_relationship('李风', '苏凝雪', {
    'relationship_type': '恋人',
    'strength': 8,
    'description': '青梅竹马'
})

# 添加事件
event_id = system.add_event({
    'name': '初入江湖',
    'category': '成长',
    'importance': 8,
    'location': '青云山'
})

# 添加参与者
system.add_participant(event_id, '李风', '主导者', 8)

# 生成可视化
fig = system.show_character_network()
fig.show()
```

## 📊 核心功能详解

### 1. 人物关系管理

#### 添加角色
```python
system.add_character({
    'name': '角色名',
    'prototype': '英雄/导师/反派/伙伴/智者',
    'narrative_role': '主角/配角/对手',
    'core_identity': {
        'aliases': ['别名1', '别名2'],
        'basic_info': {'gender': '男/女', 'age': 25}
    },
    'tags': ['标签1', '标签2']
})
```

#### 建立关系
```python
system.add_relationship('源角色', '目标角色', {
    'relationship_type': '师徒/朋友/敌人/恋人/亲人/同事',
    'strength': 1-10,  # 关系强度
    'description': '关系描述'
})
```

#### 更新关系
```python
system.characters.update_relationship('角色A', '角色B', {
    'strength': 9,
    'status': '紧张'
})
```

#### 查看关系历史
```python
history = system.characters.get_relationship_history('角色A', '角色B')
```

### 2. 事件库管理

#### 添加事件
```python
system.add_event({
    'name': '事件名称',
    'description': '详细描述',
    'timestamp': '2023-01-01T10:00:00',
    'location': '地点',
    'category': '冲突/成长/转折/相遇/分离/发现/决策',
    'tags': ['标签1', '标签2'],
    'importance': 1-10
})
```

#### 添加参与者
```python
system.add_participant(event_id, '角色名', '主导者/参与者/受影响者/旁观者', 影响程度, '备注')
```

#### 高级查询
```python
# 按多条件查询
events = system.query_events(
    persons=['李风', '苏凝雪'],
    categories=['冲突', '成长'],
    importance_range=(7, 10),
    time_range=('2023-01-01T00:00:00', '2023-12-31T23:59:59'),
    sort_by='importance',
    limit=10
)

# 快速搜索
results = system.quick_search('李风', search_type='events')
```

### 3. 里程碑系统

#### 添加里程碑
```python
system.add_milestone({
    'character_name': '角色名',
    'name': '里程碑名称',
    'description': '详细描述',
    'timestamp': '2023-01-01T10:00:00',
    'milestone_type': 'growth/achievement/setback/revelation/transformation/decision',
    'importance': 1-10,
    'psychological_change': {'confidence': '增强'},
    'values_change': {'justice': '坚定'},
    'goals_change': {'main_goal': '拯救世界'}
})
```

#### 绑定事件
```python
system.bind_milestone_to_event(milestone_id, event_id)
```

#### 成长分析
```python
analysis = system.milestones.analyze_character_growth('角色名')
print(f"总里程碑: {analysis['total_milestones']}")
print(f"平均重要性: {analysis['average_importance']}")
print(f"成长速度: {analysis['average_growth_rate']}")
```

### 4. 可视化功能

#### 人物关系网络图
```python
# 基础网络图
fig = system.show_character_network()

# 自定义布局和过滤
fig = system.show_character_network(
    layout='spring',  # spring/circular/kamada_kawai
    filter_strength=5  # 过滤低强度关系
)

# 保存图表
fig.write_html('关系网络.html')
```

#### 时间线视图
```python
# 全局时间线
fig = system.show_timeline()

# 角色时间线
fig = system.show_timeline(character_name='李风')

# 分类过滤
fig = system.show_timeline(category_filter=['冲突', '成长'])
```

#### 成长轨迹图
```python
fig = system.show_character_growth('角色名')
```

### 5. 数据分析

#### 系统统计
```python
# 打印详细统计
system.print_system_statistics()

# 获取统计数据
stats = system.get_system_statistics()
```

#### 关系模式分析
```python
patterns = system.characters.analyze_relationship_patterns()
print(f"关系类型分布: {patterns['type_distribution']}")
print(f"网络密度: {patterns['density']}")
```

#### 事件模式分析
```python
patterns = system.events.analyze_event_patterns()
print(f"分类分布: {patterns['category_distribution']}")
print(f"时间分布: {patterns['monthly_distribution']}")
```

### 6. 数据管理

#### 导出数据
```python
system.export_to_json('my_novel_data.json')
```

#### 导入数据
```python
system.import_from_json('my_novel_data.json')
```

#### 从v2迁移
```python
# 假设有v2系统实例
mapping = system.import_from_v2(v2_system)
```

### 7. 性能优化

#### 数据完整性检查
```python
issues = system.validate_data_integrity()
print(f"错误: {len(issues['errors'])}")
print(f"警告: {len(issues['warnings'])}")
```

#### 性能优化
```python
results = system.optimize_performance()
```

#### 批量操作
```python
# 批量添加角色
characters_data = [
    {'name': '角色1', 'prototype': '英雄'},
    {'name': '角色2', 'prototype': '导师'}
]
results = system.batch_add_characters(characters_data)

# 批量添加事件
events_data = [
    {'name': '事件1', 'category': '成长'},
    {'name': '事件2', 'category': '冲突'}
]
results = system.batch_add_events(events_data)
```

## 🎯 最佳实践

### 1. 数据组织建议

- **角色命名**: 使用唯一且易识别的名称
- **关系强度**: 1-3(弱), 4-6(中), 7-10(强)
- **事件分类**: 合理使用预定义分类，保持一致性
- **时间格式**: 统一使用ISO格式 (YYYY-MM-DDTHH:MM:SS)

### 2. 性能优化建议

- 定期运行 `system.optimize_performance()` 清理缓存
- 大量数据时使用筛选条件限制查询结果
- 使用批量操作接口提高效率

### 3. 可视化建议

- 关系网络图：角色数量超过20时建议使用强度过滤
- 时间线图：事件较多时使用分类或时间范围过滤
- 成长轨迹：确保里程碑有合理的时间间隔

## 🔧 故障排除

### 常见问题

1. **角色不存在错误**
   - 检查角色名称拼写
   - 使用 `system.characters.resolve_name()` 验证名称

2. **时间格式错误**
   - 确保使用ISO格式: `2023-01-01T10:00:00`
   - 使用 `datetime.now().isoformat()` 生成标准格式

3. **可视化显示问题**
   - 检查是否安装了plotly
   - 确保有足够的数据进行可视化

4. **性能问题**
   - 运行数据完整性检查
   - 执行性能优化
   - 考虑使用筛选条件

### 调试技巧

```python
# 检查数据完整性
issues = system.validate_data_integrity()
for error in issues['errors']:
    print(f"错误: {error}")

# 查看系统健康度
stats = system.get_system_statistics()
health = stats['system_health']
print(f"系统健康度: {health['health_status']}")

# 安全操作包装
result = system.safe_operation('添加角色', system.add_character, char_data)
if not result['success']:
    print(f"操作失败: {result['error']}")
```

## 📚 API参考

详细的API文档请参考代码中的docstring，每个方法都有详细的参数说明和使用示例。

## 🆕 版本更新

### v3.0 Enhanced 新特性

- 关系历史追踪和动态更新
- 事件分类和高级搜索
- 成长轨迹分析和比较
- 交互式可视化图表
- 性能优化和错误处理
- 批量操作接口
- 系统健康度评估

### 从v2升级

v3.0 Enhanced完全兼容v2数据，可以无缝迁移：

```python
# 创建v3系统
v3_system = NovelV3System()

# 从v2迁移数据
mapping = v3_system.import_from_v2(v2_system)

# 验证迁移结果
print(f"迁移角色: {len(mapping['char_id_to_name'])}")
print(f"迁移事件: {len(mapping['event_id_map'])}")
```

## 🤝 支持与反馈

如有问题或建议，请通过以下方式联系：
- 查看演示脚本: `图v3_演示.py`
- 运行完整性检查: `system.validate_data_integrity()`
- 查看系统统计: `system.print_system_statistics()`
