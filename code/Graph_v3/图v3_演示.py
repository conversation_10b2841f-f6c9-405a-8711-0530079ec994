# -*- coding: utf-8 -*-
"""
小说人物档案管理系统 v3.0 Enhanced 演示脚本
展示所有核心功能和可视化效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 图v3 import NovelV3System
import plotly.io as pio

def create_demo_data(system: NovelV3System):
    """创建演示数据"""
    print("\n🎬 创建演示数据...")
    
    # 添加主要角色
    characters = [
        {
            'name': '李风',
            'prototype': '英雄',
            'narrative_role': '主角',
            'core_identity': {
                'aliases': ['风之子', '剑心'],
                'basic_info': {'gender': '男', 'age': 25, 'occupation': '剑客'}
            },
            'tags': ['正义', '勇敢', '成长']
        },
        {
            'name': '苏凝雪',
            'prototype': '伙伴',
            'narrative_role': '女主',
            'core_identity': {
                'aliases': ['雪仙子'],
                'basic_info': {'gender': '女', 'age': 23, 'occupation': '医师'}
            },
            'tags': ['聪慧', '冷静', '治愈']
        },
        {
            'name': '赵无双',
            'prototype': '反派',
            'narrative_role': '对手',
            'core_identity': {
                'aliases': ['魔君'],
                'basic_info': {'gender': '男', 'age': 35, 'occupation': '魔教教主'}
            },
            'tags': ['野心', '阴谋', '强大']
        },
        {
            'name': '白大师',
            'prototype': '导师',
            'narrative_role': '配角',
            'core_identity': {
                'aliases': ['白云真人'],
                'basic_info': {'gender': '男', 'age': 60, 'occupation': '宗门长老'}
            },
            'tags': ['智慧', '慈祥', '指导']
        },
        {
            'name': '黑衣人',
            'prototype': '反派',
            'narrative_role': '爪牙',
            'tags': ['神秘', '危险']
        }
    ]
    
    for char_data in characters:
        system.add_character(char_data)
    
    # 添加人物关系
    relationships = [
        ('李风', '苏凝雪', {'relationship_type': '恋人', 'strength': 8, 'description': '青梅竹马，互相倾慕'}),
        ('苏凝雪', '李风', {'relationship_type': '朋友', 'strength': 7, 'description': '深厚友谊，暗生情愫'}),
        ('李风', '白大师', {'relationship_type': '师徒', 'strength': 9, 'description': '尊师重道'}),
        ('白大师', '李风', {'relationship_type': '师徒', 'strength': 9, 'description': '悉心栽培'}),
        ('李风', '赵无双', {'relationship_type': '敌人', 'strength': 8, 'description': '正邪对立'}),
        ('赵无双', '李风', {'relationship_type': '敌人', 'strength': 9, 'description': '视为眼中钉'}),
        ('赵无双', '黑衣人', {'relationship_type': '同事', 'strength': 6, 'description': '主仆关系'}),
        ('苏凝雪', '白大师', {'relationship_type': '朋友', 'strength': 6, 'description': '相互尊重'})
    ]
    
    for src, dst, rel_data in relationships:
        system.add_relationship(src, dst, rel_data)
    
    # 添加事件
    events = [
        {
            'name': '初入江湖',
            'description': '李风第一次离开师门，踏入江湖',
            'timestamp': '2023-01-15T08:00:00',
            'location': '青云山',
            'category': '成长',
            'tags': ['起点', '冒险'],
            'importance': 8
        },
        {
            'name': '邂逅佳人',
            'description': '李风在客栈中偶遇苏凝雪',
            'timestamp': '2023-02-01T19:30:00',
            'location': '悦来客栈',
            'category': '相遇',
            'tags': ['命运', '爱情'],
            'importance': 7
        },
        {
            'name': '山路遭遇',
            'description': '李风在山路上遭遇黑衣人袭击',
            'timestamp': '2023-02-15T14:20:00',
            'location': '天山古道',
            'category': '冲突',
            'tags': ['战斗', '危险'],
            'importance': 6
        },
        {
            'name': '师父传功',
            'description': '白大师传授李风绝世武功',
            'timestamp': '2023-03-01T10:00:00',
            'location': '青云山',
            'category': '成长',
            'tags': ['传承', '突破'],
            'importance': 9
        },
        {
            'name': '魔教来袭',
            'description': '赵无双率领魔教攻打正派',
            'timestamp': '2023-04-10T22:00:00',
            'location': '武林大会',
            'category': '冲突',
            'tags': ['大战', '转折'],
            'importance': 10
        },
        {
            'name': '生死诀别',
            'description': '李风与苏凝雪在大战中分离',
            'timestamp': '2023-04-11T02:30:00',
            'location': '武林大会',
            'category': '分离',
            'tags': ['离别', '痛苦'],
            'importance': 8
        }
    ]
    
    event_ids = []
    for event_data in events:
        event_id = system.add_event(event_data)
        event_ids.append(event_id)
    
    # 添加事件参与者
    participations = [
        (event_ids[0], '李风', '主导者', 8, '主角出场'),
        (event_ids[0], '白大师', '参与者', 6, '师父送别'),
        (event_ids[1], '李风', '主导者', 7, '一见钟情'),
        (event_ids[1], '苏凝雪', '主导者', 7, '命运邂逅'),
        (event_ids[2], '李风', '主导者', 8, '英勇应战'),
        (event_ids[2], '黑衣人', '对手', 6, '突然袭击'),
        (event_ids[3], '李风', '参与者', 9, '获得传承'),
        (event_ids[3], '白大师', '主导者', 8, '传授绝学'),
        (event_ids[4], '李风', '主导者', 10, '正派领袖'),
        (event_ids[4], '赵无双', '对手', 10, '魔教教主'),
        (event_ids[4], '苏凝雪', '参与者', 8, '医治伤员'),
        (event_ids[4], '白大师', '参与者', 7, '长老身份'),
        (event_ids[5], '李风', '主导者', 9, '痛苦离别'),
        (event_ids[5], '苏凝雪', '主导者', 9, '被迫分离')
    ]
    
    for event_id, char_name, role, impact, note in participations:
        system.add_participant(event_id, char_name, role, impact, note)
    
    # 添加里程碑
    milestones = [
        {
            'character_name': '李风',
            'name': '初窥剑道',
            'description': '第一次领悟剑法真谛',
            'timestamp': '2023-01-20T15:00:00',
            'milestone_type': 'growth',
            'importance': 7,
            'psychological_change': {'confidence': '增强'},
            'bound_events': [event_ids[0]]
        },
        {
            'character_name': '李风',
            'name': '情窦初开',
            'description': '对苏凝雪产生爱意',
            'timestamp': '2023-02-01T20:00:00',
            'milestone_type': 'revelation',
            'importance': 6,
            'psychological_change': {'emotional_maturity': '提升'},
            'bound_events': [event_ids[1]]
        },
        {
            'character_name': '李风',
            'name': '武功大成',
            'description': '掌握师父传授的绝世武功',
            'timestamp': '2023-03-01T12:00:00',
            'milestone_type': 'achievement',
            'importance': 9,
            'psychological_change': {'power': '大幅提升'},
            'bound_events': [event_ids[3]]
        },
        {
            'character_name': '李风',
            'name': '承担责任',
            'description': '成为正派领袖，承担拯救武林的责任',
            'timestamp': '2023-04-10T20:00:00',
            'milestone_type': 'transformation',
            'importance': 10,
            'psychological_change': {'responsibility': '觉醒'},
            'bound_events': [event_ids[4]]
        },
        {
            'character_name': '苏凝雪',
            'name': '医术精进',
            'description': '医术达到新的境界',
            'timestamp': '2023-02-20T14:00:00',
            'milestone_type': 'achievement',
            'importance': 7,
            'psychological_change': {'skill': '提升'}
        },
        {
            'character_name': '苏凝雪',
            'name': '情感觉醒',
            'description': '意识到对李风的感情',
            'timestamp': '2023-03-15T18:00:00',
            'milestone_type': 'revelation',
            'importance': 8,
            'psychological_change': {'love': '觉醒'}
        }
    ]
    
    for milestone_data in milestones:
        milestone_id = system.add_milestone(milestone_data)
        # 绑定事件
        if 'bound_events' in milestone_data:
            for event_id in milestone_data['bound_events']:
                system.bind_milestone_to_event(milestone_id, event_id)
    
    print("✅ 演示数据创建完成")
    return system

def demonstrate_features(system: NovelV3System):
    """演示系统功能"""
    print("\n🚀 开始功能演示...")
    
    # 1. 系统统计
    print("\n1️⃣ 系统统计信息:")
    system.print_system_statistics()
    
    # 2. 角色完整信息
    print("\n2️⃣ 角色完整信息查询:")
    li_feng_info = system.get_character_complete_info('李风')
    print(f"角色: {li_feng_info['basic_info']['name']}")
    print(f"影响力排名: {li_feng_info['analysis']['influence_rank']}")
    print(f"活跃度: {li_feng_info['analysis']['activity_level']}")
    print(f"成长状况: {li_feng_info['analysis']['growth_summary']}")
    print(f"关系数量: 出度 {len(li_feng_info['relationships']['outgoing'])}, 入度 {len(li_feng_info['relationships']['incoming'])}")
    print(f"参与事件: {len(li_feng_info['events']['participated'])} 个")
    print(f"里程碑: {len(li_feng_info['milestones']['list'])} 个")
    
    # 3. 事件查询
    print("\n3️⃣ 事件查询演示:")
    conflict_events = system.events.query_events(categories=['冲突'], sort_by='importance', limit=3)
    print(f"冲突类事件 (按重要性排序):")
    for event in conflict_events:
        print(f"  - {event['name']} (重要性: {event['importance']})")
    
    # 4. 人物成长分析
    print("\n4️⃣ 人物成长分析:")
    growth_analysis = system.milestones.analyze_character_growth('李风')
    print(f"李风成长分析:")
    print(f"  总里程碑: {growth_analysis['total_milestones']}")
    print(f"  平均重要性: {growth_analysis['average_importance']:.2f}")
    print(f"  成长速度: {growth_analysis['average_growth_rate']:.2f}")
    print(f"  里程碑类型分布: {growth_analysis['milestone_type_distribution']}")
    
    # 5. 关系模式分析
    print("\n5️⃣ 关系模式分析:")
    rel_patterns = system.characters.analyze_relationship_patterns()
    print(f"关系类型分布: {rel_patterns['type_distribution']}")
    print(f"互相关系比例: {rel_patterns['mutual_relationship_ratio']:.2%}")
    print(f"网络密度: {rel_patterns['density']:.3f}")
    
    # 6. 数据完整性检查
    print("\n6️⃣ 数据完整性检查:")
    integrity_check = system.validate_data_integrity()
    print(f"错误: {len(integrity_check['errors'])} 个")
    print(f"警告: {len(integrity_check['warnings'])} 个")
    print(f"信息: {len(integrity_check['info'])} 个")
    
    # 7. 快速搜索
    print("\n7️⃣ 快速搜索演示:")
    search_results = system.quick_search('李风')
    print(f"搜索 '李风' 结果:")
    print(f"  角色: {len(search_results['characters'])} 个")
    print(f"  事件: {len(search_results['events'])} 个")
    print(f"  里程碑: {len(search_results['milestones'])} 个")

def demonstrate_visualizations(system: NovelV3System):
    """演示可视化功能"""
    print("\n📊 开始可视化演示...")
    
    # 设置plotly默认渲染器
    pio.renderers.default = "browser"
    
    # 1. 人物关系网络图
    print("\n1️⃣ 生成人物关系网络图...")
    network_fig = system.show_character_network(layout='spring', filter_strength=0)
    network_fig.write_html('code/Graph_v3/人物关系网络图_v3.html')
    print("✅ 人物关系网络图已保存: 人物关系网络图_v3.html")
    
    # 2. 全局时间线
    print("\n2️⃣ 生成全局时间线...")
    timeline_fig = system.show_timeline()
    timeline_fig.write_html('code/Graph_v3/全局时间线_v3.html')
    print("✅ 全局时间线已保存: 全局时间线_v3.html")
    
    # 3. 角色时间线
    print("\n3️⃣ 生成角色时间线...")
    char_timeline_fig = system.show_timeline(character_name='李风')
    char_timeline_fig.write_html('code/Graph_v3/李风时间线_v3.html')
    print("✅ 角色时间线已保存: 李风时间线_v3.html")
    
    # 4. 角色成长轨迹
    print("\n4️⃣ 生成角色成长轨迹...")
    growth_fig = system.show_character_growth('李风')
    growth_fig.write_html('code/Graph_v3/李风成长轨迹_v3.html')
    print("✅ 角色成长轨迹已保存: 李风成长轨迹_v3.html")
    
    print("\n🎉 所有可视化文件已生成完成！")
    print("请在浏览器中打开HTML文件查看交互式图表。")

def main():
    """主演示函数"""
    print("="*70)
    print("🎭 小说人物档案管理系统 v3.0 Enhanced 功能演示")
    print("="*70)
    
    # 初始化系统
    system = NovelV3System()
    
    # 创建演示数据
    create_demo_data(system)
    
    # 演示核心功能
    demonstrate_features(system)
    
    # 演示可视化功能
    demonstrate_visualizations(system)
    
    # 性能优化演示
    print("\n🔧 性能优化演示:")
    optimization_results = system.optimize_performance()
    print(f"优化结果: {optimization_results}")
    
    # 导出数据
    print("\n💾 数据导出演示:")
    export_success = system.export_to_json('code/Graph_v3/demo_data_v3.json')
    if export_success:
        print("✅ 演示数据已导出: demo_data_v3.json")
    
    print("\n" + "="*70)
    print("🎊 演示完成！系统功能全面展示结束。")
    print("="*70)

if __name__ == '__main__':
    main()
