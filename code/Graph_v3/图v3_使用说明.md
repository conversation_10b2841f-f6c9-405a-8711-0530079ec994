# 小说人物档案管理系统 V3 使用说明

V3 定位为“辅助创作工具”，专注于人物塑造、剧情连接、事件记录与检索，避免替代作者创作本身。核心提升：
- 人物关系使用“名字”为节点 ID，图为有向（支持非对称关系）
- 人物节点具备多维属性（核心身份、内在维度、外在表现、角色类型、标签等）
- 全局事件库支持多人物参与，提供“人/事/地/时”快速查询索引
- 独立的里程碑系统，支持与事件绑定；不混入关系图/事件图
- 兼容 v2 数据结构，提供迁移入口与 JSON 导入/导出

---

## 1. 安装与依赖
- Python 3.8+
- 必需：networkx
- 可选：pandas（仅当你希望沿用 v2 的 Timeline 特性时）

安装示例：
```bash
pip install networkx
```

> 注：V3 默认不强依赖可视化库；如需可视化可参考 v2 的用法扩展。

---

## 2. 快速开始
```python
from code.Graph_v3.图v3 import NovelV3System

sys = NovelV3System()
# 添加人物（名字即节点ID，自动唯一化并保留稳定uid）
sys.add_character({'name': '李风', 'prototype': '英雄', 'narrative_role': '主角'})
sys.add_character({'name': '白大师', 'prototype': '导师', 'narrative_role': '配角'})

# 有向人物关系（支持非对称）
sys.add_relationship('李风', '白大师', {'relationship_type': '师徒', 'strength': 9})

# 事件与多人物参与
eid = sys.add_event({'name': '初见', 'location': '天山', 'tags': ['邂逅']})
sys.add_participant(eid, '李风', '参与者', 7)
sys.add_participant(eid, '白大师', '参与者', 6)

# 里程碑与事件绑定
mid = sys.add_milestone({'character_name': '李风', 'name': '立志行侠仗义', 'milestone_type': 'growth'})
sys.bind_milestone_to_event(mid, eid)

# “人事地/时”查询
results = sys.query_three_elements(person='李风', matter_keywords=['邂逅'], place='天山')
print(len(results))
```

---

## 3. 核心概念与数据结构

### 3.1 人物关系图（CharacterGraphV3）
- 使用 `nx.DiGraph` 表达“有向关系”，例如：A → B 为“深爱”，B → A 可为“冷淡”。
- 节点 ID = `name_key`（人物名称），同名自动追加 `#2/#3` 唯一化；同时保留稳定 `uid` 便于迁移与追踪。
- 人物多维属性：
  - prototype（原型/核心身份）
  - narrative_role（叙事角色类型）
  - core_identity/internal_dimension/external_dimension（多维信息容器）
  - tags（标签）
- 支持别名 alias：`add_alias(name_key, alias)`，查询时 `resolve_name()` 自动解析。

常用方法：
- `add_character(character_data) -> name_key`
- `add_relationship(src_name, dst_name, relationship_data) -> id`
- `get_outgoing_relationships(name_or_alias) / get_incoming_relationships(name_or_alias)`
- `analyze_centrality()`（入度中心性近似影响力）

### 3.2 全局事件库（EventLibraryV3）
- 事件结构：`{id, name, description, timestamp, location, tags, importance, participants[]}`
- 参与记录：`participants[]` 中存 `{name, role, emotional_impact, note}`
- 内建多索引：
  - `index_by_person`（人→事件）
  - `index_by_location`（地→事件）
  - `index_by_tag`（标签→事件）

常用方法：
- `add_event(event_data) -> event_id`
- `add_participant(event_id, character_name, role='参与者', impact=5, note='')`
- `query_events(persons=None, keywords=None, location=None, time_range=None)`

> V3 的 `NovelV3System.add_participant()` 会先解析别名为标准 `name_key`，确保参与者与人物节点一致。

### 3.3 里程碑系统（MilestoneManagerV3）
- 独立于人物关系图和事件库，使用 `nx.DiGraph` 表达里程碑间的依赖关系。
- 里程碑字段包含：`character_name, name, timestamp, milestone_type, *change, proof, importance, bound_events[]`。
- 支持 `bind_milestone_to_event(milestone_id, event_id)` 显式绑定。

常用方法：
- `add_milestone(milestone_data) -> milestone_id`
- `bind_milestone_to_event(milestone_id, event_id)`
- `add_dependency(pre_id, dep_id, data=None)`
- `get_character_milestones(name_key)`（按时间排序）

---

## 4. 一致性检查（防止角色崩塌与剧情衔接错误）
使用 `run_consistency_checks(rules=None)` 快速生成报告：
- 参与者建档检查：事件参与名单中若出现未建档或未声明别名的人物，给出警告
- 里程碑时间顺序检查：里程碑时间倒序/格式异常，给出提醒或错误
- 关系冲突检查：同一对人物在双向上若同时存在强度较高（默认 ≥7）的“恋人/朋友”与“敌人”关系，对应冲突警告

示例：
```python
report = sys.run_consistency_checks({'conflict_strength': 7})
print(report['summary'], report['warnings'][:3])
```

> 可按你的世界观扩展冲突规则，例如：在你设定中“师徒”与“敌人”在同一时期也视为冲突，可在规则层自定义。

---

## 5. 查询指南（人/事/地/时）
- 人（单/多）：`query_three_elements(person='李风', ...)` 或 `query_by_persons(['李风','苏凝雪'], ...)`
- 事：以 `keywords` 同时匹配事件名/描述/标签（交集匹配）
- 地：`location='天山'`
- 时：`time_range=('2023-05-01T00:00:00','2023-05-31T23:59:59')`

组合示例：
```python
sys.events.query_events(persons=['李风','苏凝雪'], keywords=['遭遇','冲突'], location='天山脚下',
                        time_range=('2023-05-01T00:00:00','2023-05-31T23:59:59'))
```

---

## 6. 数据导入/导出与 v2 迁移
- V3 JSON 导出/导入：
  - `export_to_json('novel_v3_data.json')`
  - `import_from_json('novel_v3_data.json')`
- 从 v2 迁移：
```python
from code.Graph_v2.图v2 import NovelMultiGraphSystem
v2 = NovelMultiGraphSystem()
# v2 加载/构建数据...

v3 = NovelV3System()
mapping = v3.import_from_v2(v2)
print(mapping['char_id_to_name'])  # 旧id -> 新name_key 映射
```
> 注：v2 无向人物关系导入为 v3 的“双向有向边”，以保留原始语义；重名人物会自动唯一化为 `张三#2` 等。

---

## 7. 扩展剧情与人物示例
V3 内置 `create_sample_data_v3()` 快速扩展一个小规模世界观（不自动执行）。它包含：
- 新人物：苏凝雪（女主）、赵无双（对手）、黑衣人（爪牙）
- 非对称关系：李风→苏凝雪（恋人）、苏凝雪→李风（朋友）；李风↔赵无双（敌人）
- 事件：山路遭遇、夜探古堡、宗门大比（多人物参与）
- 里程碑：李风“领悟剑心”、苏凝雪“破除心魔”，并与事件绑定

使用：
```python
sys = NovelV3System()
sys.add_character({'name': '李风', 'prototype': '英雄', 'narrative_role': '主角'})
sys.add_character({'name': '白大师', 'prototype': '导师', 'narrative_role': '配角'})
sys.create_sample_data_v3()  # 扩展世界观
```

---

## 8. 建议的创作工作流
1. 先建“核心人物”与“基础关系”（可先粗粒度）
2. 用事件库按“人事地时”记录关键节点，确保参与者和地点明确
3. 为角色添加里程碑，绑定关键事件，逐步形成成长弧光
4. 定期运行 `run_consistency_checks()`，检查人物设定与剧情衔接
5. 随着创作推进，补全多维属性（core_identity / internal / external），防止人物崩塌

---

## 9. 兼容与注意事项
- 名字即节点ID：请避免频繁改名；若必须改名，建议保留别名映射 `add_alias`
- 时间字段请使用 ISO-8601 格式（如 `2023-05-01T10:00:00`）以便时间相关查询与校验
- 冲突检查规则为“启发式”，不强制；请根据你的世界观扩展或调整
- 导入/导出建议放在里程碑阶段，以免频繁保存破坏创作节奏

---

## 10. 变更日志（相对 v2）
- [新增] 有向人物关系（非对称关系表达）
- [新增] 名称为节点ID + 别名解析；保留稳定 uid
- [新增] 全局事件库的人/地/标签索引与“人事地时”查询
- [新增] 独立里程碑系统与事件绑定
- [新增] 一致性检查报告（参与建档、时间顺序、关系冲突）
- [兼容] v2 → v3 数据迁移；JSON 导入/导出

如需更多功能（可视化、批量导入、细粒度规则校验等），可在此基础上增量扩展模块。

