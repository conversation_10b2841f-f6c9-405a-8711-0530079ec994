# -*- coding: utf-8 -*-
"""
小说人物档案管理系统 v3.0 Enhanced - 全功能辅助创作工具
核心特性：
1) 人物关系使用“名字”为节点ID；关系图为有向图（支持非对称关系）
2) 人物节点具备多维属性（核心身份/内在维度/外在表现/角色类型等）
3) 全局事件库：事件可包含多人物参与，提供人/事/地快速查询索引
4) 角色成长轨迹：独立的里程碑系统，支持与事件绑定；不混入关系图/事件图
5) 兼容v2数据，提供迁移入口
6) 交互式可视化界面：支持动态关系图、时间线视图、成长轨迹等
7) 性能优化：支持大数据量处理和实时更新
"""

from __future__ import annotations
import json
import uuid
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Set, Union
from collections import defaultdict, Counter
import networkx as nx
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# ============== 通用基础 ==============

def now_iso() -> str:
    return datetime.now().isoformat()

class BaseManager:
    def __init__(self):
        self.created_time = datetime.now()
        self.updated_time = datetime.now()

    def _generate_id(self, prefix: str = "") -> str:
        return f"{prefix}_{uuid.uuid4().hex[:8]}" if prefix else uuid.uuid4().hex[:8]

    def _update_timestamp(self):
        self.updated_time = datetime.now()

# ============== 人物关系（有向，名字为节点ID） ==============

class CharacterGraphV3(BaseManager):
    """名字即节点ID；关系为有向图。支持非对称关系。
    - 去重：若同名出现，自动添加 #2/#3 后缀保证唯一
    - 保留一个稳定的 uid（迁移/兼容用），但图节点ID使用 name_key
    - 增强功能：关系历史追踪、动态更新、强度可视化
    """
    RELATIONSHIP_TYPES = {
        '师徒': {'color': '#FF6B6B', 'strength_range': (7, 10), 'weight': 8},
        '朋友': {'color': '#4ECDC4', 'strength_range': (5, 9), 'weight': 6},
        '敌人': {'color': '#FF4757', 'strength_range': (3, 8), 'weight': 7},
        '恋人': {'color': '#FF3838', 'strength_range': (8, 10), 'weight': 9},
        '亲人': {'color': '#FFA502', 'strength_range': (6, 10), 'weight': 8},
        '同事': {'color': '#70A1FF', 'strength_range': (3, 7), 'weight': 5},
        '陌生人': {'color': '#A4B0BE', 'strength_range': (1, 3), 'weight': 2}
    }

    def __init__(self):
        super().__init__()
        self.graph = nx.DiGraph()
        self.name_to_uid: Dict[str, str] = {}   # name_key -> uid
        self.uid_to_name: Dict[str, str] = {}   # uid -> name_key
        self.alias_to_name: Dict[str, str] = {} # 别名 -> name_key
        # 新增：关系历史追踪
        self.relationship_history: Dict[str, List[Dict[str, Any]]] = defaultdict(list)  # edge_key -> history
        self.relationship_stats: Dict[str, Dict[str, Any]] = {}  # 关系统计信息

    @staticmethod
    def _normalize_name(name: str) -> str:
        return (name or "未命名").strip()

    def _ensure_unique_name(self, name: str) -> str:
        base = self._normalize_name(name)
        if base not in self.name_to_uid:
            return base
        # 追加序号避免冲突
        i = 2
        while f"{base}#{i}" in self.name_to_uid:
            i += 1
        return f"{base}#{i}"

    def add_character(self, character_data: Dict[str, Any]) -> str:
        """返回 name_key（作为节点ID）。"""
        raw_name = character_data.get('name', '未命名')
        name_key = self._ensure_unique_name(raw_name)
        uid = character_data.get('uid') or self._generate_id('char')

        node_attrs = {
            'uid': uid,
            'name': name_key,
            'prototype': character_data.get('prototype', ''),            # 核心身份-原型
            'narrative_role': character_data.get('narrative_role', ''),  # 角色类型/叙事位置
            'core_identity': character_data.get('core_identity', {}),
            'internal_dimension': character_data.get('internal_dimension', {}),
            'external_dimension': character_data.get('external_dimension', {}),
            'tags': character_data.get('tags', []),
            'created_time': now_iso(),
            'updated_time': now_iso(),
        }

        self.name_to_uid[name_key] = uid
        self.uid_to_name[uid] = name_key
        self.graph.add_node(name_key, **node_attrs)
        self._update_timestamp()
        print(f"✅ 添加人物: {name_key} (uid: {uid})")
        return name_key

    def add_alias(self, name_key: str, alias: str) -> bool:
        alias = alias.strip()
        if not self.graph.has_node(name_key):
            return False
        self.alias_to_name[alias] = name_key
        return True

    def resolve_name(self, name_or_alias: str) -> Optional[str]:
        name_or_alias = (name_or_alias or '').strip()
        if name_or_alias in self.graph.nodes:
            return name_or_alias
        return self.alias_to_name.get(name_or_alias)

    def add_relationship(self, src_name: str, dst_name: str, relationship_data: Dict[str, Any]) -> str:
        src = self.resolve_name(src_name)
        dst = self.resolve_name(dst_name)
        if not (src and dst and self.graph.has_node(src) and self.graph.has_node(dst)):
            print(f"❌ 人物不存在: {src_name} 或 {dst_name}")
            return ""

        rel_id = relationship_data.get('id', self._generate_id('rel'))
        rel_type = relationship_data.get('relationship_type', '朋友')
        cfg = self.RELATIONSHIP_TYPES.get(rel_type, {'color': '#97C2FC', 'weight': 5})
        strength = relationship_data.get('strength', 5)

        edge_attrs = {
            'id': rel_id,
            'relationship_type': rel_type,
            'strength': strength,
            'status': relationship_data.get('status', '正常'),
            'description': relationship_data.get('description', ''),
            'color': cfg['color'],
            'weight': cfg.get('weight', 5),
            'created_time': now_iso(),
            'updated_time': now_iso(),
        }

        # 记录关系历史
        edge_key = f"{src}->{dst}"
        history_entry = {
            'timestamp': now_iso(),
            'action': 'created',
            'relationship_type': rel_type,
            'strength': strength,
            'description': relationship_data.get('description', ''),
            'metadata': relationship_data.copy()
        }
        self.relationship_history[edge_key].append(history_entry)

        self.graph.add_edge(src, dst, **edge_attrs)
        self._update_relationship_stats(src, dst, rel_type, strength)
        self._update_timestamp()
        print(f"✅ 添加关系: {src} → {dst} ({rel_type}, 强度: {strength})")
        return rel_id

    def update_relationship(self, src_name: str, dst_name: str, updates: Dict[str, Any]) -> bool:
        """更新关系并记录历史"""
        src = self.resolve_name(src_name)
        dst = self.resolve_name(dst_name)
        if not (src and dst and self.graph.has_edge(src, dst)):
            print(f"❌ 关系不存在: {src_name} → {dst_name}")
            return False

        edge_data = self.graph[src][dst]
        old_strength = edge_data.get('strength', 5)
        old_type = edge_data.get('relationship_type', '朋友')

        # 更新边属性
        for key, value in updates.items():
            if key in edge_data:
                edge_data[key] = value
        edge_data['updated_time'] = now_iso()

        # 记录历史
        edge_key = f"{src}->{dst}"
        history_entry = {
            'timestamp': now_iso(),
            'action': 'updated',
            'old_values': {'strength': old_strength, 'relationship_type': old_type},
            'new_values': updates.copy(),
            'description': f"关系更新: {updates}"
        }
        self.relationship_history[edge_key].append(history_entry)

        # 更新统计
        new_strength = edge_data.get('strength', old_strength)
        new_type = edge_data.get('relationship_type', old_type)
        self._update_relationship_stats(src, dst, new_type, new_strength)

        self._update_timestamp()
        print(f"✅ 更新关系: {src} → {dst} ({updates})")
        return True

    def _update_relationship_stats(self, src: str, dst: str, rel_type: str, strength: int):
        """更新关系统计信息"""
        edge_key = f"{src}->{dst}"
        if edge_key not in self.relationship_stats:
            self.relationship_stats[edge_key] = {
                'creation_time': now_iso(),
                'update_count': 0,
                'strength_history': [],
                'type_changes': []
            }

        stats = self.relationship_stats[edge_key]
        stats['update_count'] += 1
        stats['last_update'] = now_iso()
        stats['current_strength'] = strength
        stats['current_type'] = rel_type
        stats['strength_history'].append({'timestamp': now_iso(), 'strength': strength})

        # 保持历史记录在合理范围内
        if len(stats['strength_history']) > 100:
            stats['strength_history'] = stats['strength_history'][-50:]

    def get_character(self, name_or_alias: str) -> Optional[Dict[str, Any]]:
        name_key = self.resolve_name(name_or_alias)
        if not name_key:
            return None
        return self.graph.nodes[name_key]

    def get_outgoing_relationships(self, name_or_alias: str) -> List[Dict[str, Any]]:
        name_key = self.resolve_name(name_or_alias)
        if not name_key or not self.graph.has_node(name_key):
            return []
        res = []
        for _, dst, data in self.graph.out_edges(name_key, data=True):
            res.append({'source': name_key, 'target': dst, **data})
        return res

    def get_incoming_relationships(self, name_or_alias: str) -> List[Dict[str, Any]]:
        name_key = self.resolve_name(name_or_alias)
        if not name_key or not self.graph.has_node(name_key):
            return []
        res = []
        for src, _, data in self.graph.in_edges(name_key, data=True):
            res.append({'source': src, 'target': name_key, **data})
        return res

    def get_relationship_history(self, src_name: str, dst_name: str) -> List[Dict[str, Any]]:
        """获取关系历史记录"""
        src = self.resolve_name(src_name)
        dst = self.resolve_name(dst_name)
        if not (src and dst):
            return []

        edge_key = f"{src}->{dst}"
        return self.relationship_history.get(edge_key, [])

    def get_relationship_stats(self, src_name: str, dst_name: str) -> Dict[str, Any]:
        """获取关系统计信息"""
        src = self.resolve_name(src_name)
        dst = self.resolve_name(dst_name)
        if not (src and dst):
            return {}

        edge_key = f"{src}->{dst}"
        return self.relationship_stats.get(edge_key, {})

    def analyze_centrality(self) -> Dict[str, float]:
        """分析人物中心性（影响力）"""
        if self.graph.number_of_nodes() == 0:
            return {}

        # 多种中心性指标
        in_centrality = nx.in_degree_centrality(self.graph)
        out_centrality = nx.out_degree_centrality(self.graph)
        betweenness = nx.betweenness_centrality(self.graph)

        # 综合中心性评分
        combined_centrality = {}
        for node in self.graph.nodes():
            score = (
                in_centrality.get(node, 0) * 0.4 +  # 被关注度
                out_centrality.get(node, 0) * 0.3 +  # 主动关系度
                betweenness.get(node, 0) * 0.3       # 桥梁作用
            )
            combined_centrality[node] = score

        return combined_centrality

    def analyze_relationship_patterns(self) -> Dict[str, Any]:
        """分析关系模式"""
        if self.graph.number_of_edges() == 0:
            return {}

        # 关系类型分布
        type_counts = Counter()
        strength_by_type = defaultdict(list)

        for _, _, data in self.graph.edges(data=True):
            rel_type = data.get('relationship_type', '朋友')
            strength = data.get('strength', 5)
            type_counts[rel_type] += 1
            strength_by_type[rel_type].append(strength)

        # 计算平均强度
        avg_strength_by_type = {}
        for rel_type, strengths in strength_by_type.items():
            avg_strength_by_type[rel_type] = sum(strengths) / len(strengths)

        # 互相关系分析
        mutual_relationships = 0
        total_pairs = 0

        for node1 in self.graph.nodes():
            for node2 in self.graph.nodes():
                if node1 != node2:
                    total_pairs += 1
                    if self.graph.has_edge(node1, node2) and self.graph.has_edge(node2, node1):
                        mutual_relationships += 1

        mutual_ratio = mutual_relationships / total_pairs if total_pairs > 0 else 0

        return {
            'type_distribution': dict(type_counts),
            'average_strength_by_type': avg_strength_by_type,
            'mutual_relationship_ratio': mutual_ratio,
            'total_relationships': self.graph.number_of_edges(),
            'total_characters': self.graph.number_of_nodes(),
            'density': nx.density(self.graph)
        }

# ============== 全局事件库（含多人物参与与快速索引） ==============

class EventLibraryV3(BaseManager):
    """增强版全局事件库 - 支持分类、时间线排序、关联管理和高级搜索"""

    EVENT_CATEGORIES = {
        '冲突': {'color': '#FF4757', 'icon': '⚔️'},
        '成长': {'color': '#4ECDC4', 'icon': '🌱'},
        '转折': {'color': '#FFA502', 'icon': '🔄'},
        '相遇': {'color': '#70A1FF', 'icon': '🤝'},
        '分离': {'color': '#A4B0BE', 'icon': '💔'},
        '发现': {'color': '#7BE19C', 'icon': '🔍'},
        '决策': {'color': '#FF6B6B', 'icon': '⚖️'},
        '其他': {'color': '#97C2FC', 'icon': '📝'}
    }

    def __init__(self):
        super().__init__()
        self.events: Dict[str, Dict[str, Any]] = {}
        # 参与数据直接挂在事件上
        # 索引结构
        self.index_by_person: Dict[str, Set[str]] = defaultdict(set)  # name_key -> {event_id}
        self.index_by_location: Dict[str, Set[str]] = defaultdict(set)
        self.index_by_tag: Dict[str, Set[str]] = defaultdict(set)
        self.index_by_category: Dict[str, Set[str]] = defaultdict(set)  # 新增分类索引
        self.index_by_importance: Dict[int, Set[str]] = defaultdict(set)  # 重要性索引

        # 事件关系图（因果关系、时间关系等）
        self.event_graph = nx.DiGraph()

        # 时间线缓存
        self._timeline_cache: Optional[pd.DataFrame] = None
        self._cache_dirty = True

    def add_event(self, event_data: Dict[str, Any]) -> str:
        event_id = event_data.get('id', self._generate_id('event'))
        category = event_data.get('category', '其他')

        ev = {
            'id': event_id,
            'name': event_data.get('name', '未命名事件'),
            'description': event_data.get('description', ''),
            'timestamp': event_data.get('timestamp', now_iso()),
            'location': event_data.get('location', ''),
            'tags': list(event_data.get('tags', [])),
            'category': category,  # 新增分类
            'importance': event_data.get('importance', 5),
            'participants': [],  # list of {name, role, impact, note}
            'outcomes': event_data.get('outcomes', []),  # 事件结果
            'prerequisites': event_data.get('prerequisites', []),  # 前置条件
            'consequences': event_data.get('consequences', []),  # 后续影响
            'created_time': now_iso(),
            'updated_time': now_iso(),
        }
        self.events[event_id] = ev

        # 建立各种索引
        if ev['location']:
            self.index_by_location[ev['location']].add(event_id)
        for t in ev['tags']:
            self.index_by_tag[t].add(event_id)
        self.index_by_category[category].add(event_id)
        self.index_by_importance[ev['importance']].add(event_id)

        # 添加到事件图
        self.event_graph.add_node(event_id, **ev)

        self._cache_dirty = True
        self._update_timestamp()
        print(f"✅ 添加事件: {ev['name']} (分类: {category}, ID: {event_id})")
        return event_id

    def add_event_relationship(self, source_event_id: str, target_event_id: str,
                              relationship_type: str = 'causal', strength: int = 5,
                              description: str = '') -> bool:
        """添加事件间关系（因果、时间等）"""
        if not (source_event_id in self.events and target_event_id in self.events):
            print(f"❌ 事件不存在: {source_event_id} 或 {target_event_id}")
            return False

        edge_data = {
            'relationship_type': relationship_type,  # causal, temporal, thematic
            'strength': strength,
            'description': description,
            'created_time': now_iso()
        }

        self.event_graph.add_edge(source_event_id, target_event_id, **edge_data)
        print(f"✅ 添加事件关系: {relationship_type} ({source_event_id} → {target_event_id})")
        return True

    def add_participant(self, event_id: str, character_name: str, role: str = '参与者',
                        impact: int = 5, note: str = '') -> bool:
        ev = self.events.get(event_id)
        if not ev:
            return False
        entry = {
            'name': character_name,
            'role': role,
            'emotional_impact': impact,
            'note': note,
        }
        ev['participants'].append(entry)
        self.index_by_person[character_name].add(event_id)
        self._update_timestamp()
        return True

    def get_event(self, event_id: str) -> Optional[Dict[str, Any]]:
        return self.events.get(event_id)

    def query_events(self, persons: Optional[List[str]] = None,
                     keywords: Optional[List[str]] = None,
                     location: Optional[str] = None,
                     time_range: Optional[Tuple[str, str]] = None,
                     categories: Optional[List[str]] = None,
                     importance_range: Optional[Tuple[int, int]] = None,
                     sort_by: str = 'timestamp',
                     limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """增强版事件查询"""
        candidates: Set[str] = set(self.events.keys())

        # 人物筛选
        if persons:
            sets = [self.index_by_person.get(p, set()) for p in persons]
            if not sets:
                return []
            inter = sets[0].copy()
            for s in sets[1:]:
                inter &= s
            candidates &= inter

        # 地点筛选
        if location:
            candidates &= self.index_by_location.get(location, set())

        # 分类筛选
        if categories:
            category_sets = [self.index_by_category.get(c, set()) for c in categories]
            if category_sets:
                category_union = set()
                for s in category_sets:
                    category_union |= s
                candidates &= category_union

        # 重要性筛选
        if importance_range:
            min_imp, max_imp = importance_range
            importance_sets = [self.index_by_importance.get(i, set())
                             for i in range(min_imp, max_imp + 1)]
            if importance_sets:
                importance_union = set()
                for s in importance_sets:
                    importance_union |= s
                candidates &= importance_union

        # 关键词筛选
        if keywords:
            kw = [k.lower() for k in keywords]
            def match(ev: Dict[str, Any]) -> bool:
                text = (ev['name'] + ' ' + ev['description'] + ' ' +
                       ' '.join(ev['tags']) + ' ' + ev.get('category', '')).lower()
                return all(k in text for k in kw)
            candidates = {eid for eid in candidates if match(self.events[eid])}

        # 时间范围筛选
        if time_range:
            start = datetime.fromisoformat(time_range[0])
            end = datetime.fromisoformat(time_range[1])
            def in_range(ev: Dict[str, Any]) -> bool:
                try:
                    t = datetime.fromisoformat(ev['timestamp'])
                except Exception:
                    return False
                return start <= t <= end
            candidates = {eid for eid in candidates if in_range(self.events[eid])}

        # 获取结果并排序
        results = [self.events[eid] for eid in candidates]

        # 排序
        if sort_by == 'timestamp':
            results.sort(key=lambda x: x.get('timestamp', ''))
        elif sort_by == 'importance':
            results.sort(key=lambda x: x.get('importance', 0), reverse=True)
        elif sort_by == 'name':
            results.sort(key=lambda x: x.get('name', ''))

        # 限制结果数量
        if limit:
            results = results[:limit]

        return results

    def get_timeline_dataframe(self, force_refresh: bool = False) -> pd.DataFrame:
        """获取时间线DataFrame（带缓存）"""
        if self._timeline_cache is None or self._cache_dirty or force_refresh:
            data = []
            for event_id, event in self.events.items():
                try:
                    timestamp = pd.to_datetime(event['timestamp'])
                    data.append({
                        'event_id': event_id,
                        'name': event['name'],
                        'timestamp': timestamp,
                        'category': event.get('category', '其他'),
                        'importance': event.get('importance', 5),
                        'location': event.get('location', ''),
                        'participant_count': len(event.get('participants', [])),
                        'tags': ', '.join(event.get('tags', []))
                    })
                except Exception:
                    continue

            self._timeline_cache = pd.DataFrame(data)
            if not self._timeline_cache.empty:
                self._timeline_cache = self._timeline_cache.sort_values('timestamp')
            self._cache_dirty = False

        return self._timeline_cache.copy() if self._timeline_cache is not None else pd.DataFrame()

    def analyze_event_patterns(self) -> Dict[str, Any]:
        """分析事件模式"""
        if not self.events:
            return {}

        df = self.get_timeline_dataframe()
        if df.empty:
            return {}

        # 分类分布
        category_counts = df['category'].value_counts().to_dict()

        # 重要性分布
        importance_stats = df['importance'].describe().to_dict()

        # 时间模式
        df['month'] = df['timestamp'].dt.to_period('M')
        monthly_counts = df.groupby('month').size().to_dict()

        # 地点分布
        location_counts = df[df['location'] != '']['location'].value_counts().head(10).to_dict()

        # 参与度分析
        participation_stats = df['participant_count'].describe().to_dict()

        return {
            'total_events': len(self.events),
            'category_distribution': category_counts,
            'importance_statistics': importance_stats,
            'monthly_distribution': {str(k): v for k, v in monthly_counts.items()},
            'top_locations': location_counts,
            'participation_statistics': participation_stats,
            'date_range': {
                'start': df['timestamp'].min().isoformat() if not df.empty else None,
                'end': df['timestamp'].max().isoformat() if not df.empty else None
            }
        }

# ============== 里程碑系统（独立，支持事件绑定） ==============

class MilestoneManagerV3(BaseManager):
    """增强版里程碑管理器 - 支持成长轨迹分析和可视化"""

    MILESTONE_TYPES = {
        'growth': {'color': '#4ECDC4', 'icon': '🌱', 'name': '成长'},
        'achievement': {'color': '#7BE19C', 'icon': '🏆', 'name': '成就'},
        'setback': {'color': '#FF4757', 'icon': '💔', 'name': '挫折'},
        'revelation': {'color': '#FFA502', 'icon': '💡', 'name': '顿悟'},
        'transformation': {'color': '#FF6B6B', 'icon': '🦋', 'name': '蜕变'},
        'decision': {'color': '#70A1FF', 'icon': '⚖️', 'name': '决策'}
    }

    def __init__(self):
        super().__init__()
        self.graph = nx.DiGraph()  # 里程碑依赖
        self.milestones: Dict[str, Dict[str, Any]] = {}
        self.by_character: Dict[str, List[str]] = defaultdict(list)  # name_key -> [milestone_id]

        # 成长轨迹缓存
        self._growth_cache: Dict[str, pd.DataFrame] = {}
        self._cache_dirty: Set[str] = set()

    def add_milestone(self, milestone_data: Dict[str, Any]) -> str:
        mid = milestone_data.get('id', self._generate_id('milestone'))
        name_key = milestone_data.get('character_name', '')
        m = {
            'id': mid,
            'character_name': name_key,
            'name': milestone_data.get('name', '未命名里程碑'),
            'description': milestone_data.get('description', ''),
            'timestamp': milestone_data.get('timestamp', now_iso()),
            'milestone_type': milestone_data.get('milestone_type', 'growth'),
            'psychological_change': milestone_data.get('psychological_change', {}),
            'values_change': milestone_data.get('values_change', {}),
            'goals_change': milestone_data.get('goals_change', {}),
            'proof': milestone_data.get('proof', ''),
            'importance': milestone_data.get('importance', 5),
            'bound_events': list(milestone_data.get('bound_events', [])),  # 新增绑定
            'created_time': now_iso(),
            'updated_time': now_iso(),
        }
        self.milestones[mid] = m
        self.graph.add_node(mid, **m)
        if name_key:
            self.by_character[name_key].append(mid)
        self._update_timestamp()
        print(f"✅ 添加里程碑: {m['name']} (人物: {name_key})")
        return mid

    def bind_milestone_to_event(self, milestone_id: str, event_id: str) -> bool:
        m = self.milestones.get(milestone_id)
        if not m:
            return False
        if event_id not in m['bound_events']:
            m['bound_events'].append(event_id)
            self.graph.nodes[milestone_id]['bound_events'] = m['bound_events']
            self._update_timestamp()
        return True

    def add_dependency(self, pre_id: str, dep_id: str, data: Optional[Dict[str, Any]] = None) -> bool:
        if not (pre_id in self.milestones and dep_id in self.milestones):
            return False
        d = data or {}
        self.graph.add_edge(pre_id, dep_id, **{
            'id': d.get('id', self._generate_id('dep')),
            'dependency_type': d.get('dependency_type', 'prerequisite'),
            'strength': d.get('strength', 5),
            'description': d.get('description', ''),
            'created_time': now_iso(),
        })
        self._update_timestamp()
        return True

    def get_character_milestones(self, name_key: str, sorted_by_time: bool = True) -> List[Dict[str, Any]]:
        """获取角色里程碑列表"""
        ids = self.by_character.get(name_key, [])
        arr = [self.milestones[i] for i in ids if i in self.milestones]
        if sorted_by_time:
            arr.sort(key=lambda x: x['timestamp'])
        return arr

    def get_character_growth_dataframe(self, name_key: str, force_refresh: bool = False) -> pd.DataFrame:
        """获取角色成长轨迹DataFrame"""
        if name_key not in self._growth_cache or name_key in self._cache_dirty or force_refresh:
            milestones = self.get_character_milestones(name_key)
            data = []

            for milestone in milestones:
                try:
                    timestamp = pd.to_datetime(milestone['timestamp'])
                    data.append({
                        'milestone_id': milestone['id'],
                        'name': milestone['name'],
                        'timestamp': timestamp,
                        'milestone_type': milestone.get('milestone_type', 'growth'),
                        'importance': milestone.get('importance', 5),
                        'description': milestone.get('description', ''),
                        'bound_events': len(milestone.get('bound_events', [])),
                        'psychological_change': bool(milestone.get('psychological_change')),
                        'values_change': bool(milestone.get('values_change')),
                        'goals_change': bool(milestone.get('goals_change'))
                    })
                except Exception:
                    continue

            df = pd.DataFrame(data)
            if not df.empty:
                df = df.sort_values('timestamp')
                # 计算成长趋势
                df['cumulative_importance'] = df['importance'].cumsum()
                df['growth_velocity'] = df['importance'].rolling(window=3, min_periods=1).mean()

            self._growth_cache[name_key] = df
            self._cache_dirty.discard(name_key)

        return self._growth_cache[name_key].copy()

    def analyze_character_growth(self, name_key: str) -> Dict[str, Any]:
        """分析角色成长轨迹"""
        df = self.get_character_growth_dataframe(name_key)
        if df.empty:
            return {}

        # 基础统计
        total_milestones = len(df)
        type_counts = df['milestone_type'].value_counts().to_dict()
        avg_importance = df['importance'].mean()

        # 成长趋势分析
        if len(df) > 1:
            # 计算成长速度（重要性变化率）
            df_sorted = df.sort_values('timestamp')
            time_diffs = df_sorted['timestamp'].diff().dt.total_seconds() / (24 * 3600)  # 天数
            importance_diffs = df_sorted['importance'].diff()
            growth_rates = importance_diffs / time_diffs.replace(0, 1)  # 避免除零
            avg_growth_rate = growth_rates.mean()

            # 成长阶段识别
            growth_phases = []
            current_phase = {'start_idx': 0, 'type': df_sorted.iloc[0]['milestone_type'], 'milestones': 1}

            for i in range(1, len(df_sorted)):
                if df_sorted.iloc[i]['milestone_type'] == current_phase['type']:
                    current_phase['milestones'] += 1
                else:
                    current_phase['end_idx'] = i - 1
                    growth_phases.append(current_phase.copy())
                    current_phase = {'start_idx': i, 'type': df_sorted.iloc[i]['milestone_type'], 'milestones': 1}

            current_phase['end_idx'] = len(df_sorted) - 1
            growth_phases.append(current_phase)
        else:
            avg_growth_rate = 0
            growth_phases = []

        # 变化分析
        change_analysis = {
            'psychological_changes': df['psychological_change'].sum(),
            'values_changes': df['values_change'].sum(),
            'goals_changes': df['goals_change'].sum(),
            'event_bound_milestones': df[df['bound_events'] > 0].shape[0]
        }

        return {
            'character_name': name_key,
            'total_milestones': total_milestones,
            'milestone_type_distribution': type_counts,
            'average_importance': avg_importance,
            'average_growth_rate': avg_growth_rate,
            'growth_phases': growth_phases,
            'change_analysis': change_analysis,
            'first_milestone': df.iloc[0].to_dict() if not df.empty else None,
            'latest_milestone': df.iloc[-1].to_dict() if not df.empty else None,
            'peak_importance': df['importance'].max() if not df.empty else 0,
            'growth_trajectory': df['cumulative_importance'].tolist() if not df.empty else []
        }

    def compare_character_growth(self, character_names: List[str]) -> Dict[str, Any]:
        """比较多个角色的成长轨迹"""
        comparison = {}

        for name in character_names:
            analysis = self.analyze_character_growth(name)
            if analysis:
                comparison[name] = {
                    'total_milestones': analysis['total_milestones'],
                    'average_importance': analysis['average_importance'],
                    'average_growth_rate': analysis['average_growth_rate'],
                    'dominant_milestone_type': max(analysis['milestone_type_distribution'].items(),
                                                 key=lambda x: x[1])[0] if analysis['milestone_type_distribution'] else 'unknown'
                }

        # 排名
        if comparison:
            # 按总里程碑数排名
            milestone_ranking = sorted(comparison.items(), key=lambda x: x[1]['total_milestones'], reverse=True)
            # 按平均重要性排名
            importance_ranking = sorted(comparison.items(), key=lambda x: x[1]['average_importance'], reverse=True)
            # 按成长速度排名
            growth_rate_ranking = sorted(comparison.items(), key=lambda x: x[1]['average_growth_rate'], reverse=True)

            return {
                'character_data': comparison,
                'rankings': {
                    'by_milestone_count': [name for name, _ in milestone_ranking],
                    'by_importance': [name for name, _ in importance_ranking],
                    'by_growth_rate': [name for name, _ in growth_rate_ranking]
                }
            }

        return {'character_data': {}, 'rankings': {}}

# ============== 可视化模块 ==============

class VisualizationEngine:
    """交互式可视化引擎"""

    def __init__(self, system: 'NovelV3System'):
        self.system = system

    def create_character_relationship_network(self, layout: str = 'spring',
                                            show_strength: bool = True,
                                            filter_strength: int = 0) -> go.Figure:
        """创建人物关系网络图"""
        graph = self.system.characters.graph

        if graph.number_of_nodes() == 0:
            return go.Figure().add_annotation(text="暂无人物关系数据",
                                            xref="paper", yref="paper",
                                            x=0.5, y=0.5, showarrow=False)

        # 过滤低强度关系
        if filter_strength > 0:
            edges_to_remove = [(u, v) for u, v, d in graph.edges(data=True)
                             if d.get('strength', 0) < filter_strength]
            graph = graph.copy()
            graph.remove_edges_from(edges_to_remove)

        # 计算布局
        if layout == 'spring':
            pos = nx.spring_layout(graph, k=2, iterations=50)
        elif layout == 'circular':
            pos = nx.circular_layout(graph)
        elif layout == 'kamada_kawai':
            pos = nx.kamada_kawai_layout(graph)
        else:
            pos = nx.spring_layout(graph)

        # 准备节点数据
        node_x = []
        node_y = []
        node_text = []
        node_color = []
        node_size = []

        centrality = self.system.characters.analyze_centrality()

        for node in graph.nodes():
            x, y = pos[node]
            node_x.append(x)
            node_y.append(y)

            char_data = self.system.characters.get_character(node)
            node_text.append(f"{node}<br>原型: {char_data.get('prototype', '未知')}<br>"
                           f"中心性: {centrality.get(node, 0):.3f}")

            # 根据原型设置颜色
            prototype_colors = {
                '英雄': '#FF6B6B',
                '导师': '#4ECDC4',
                '伙伴': '#FFA502',
                '反派': '#FF4757',
                '智者': '#70A1FF'
            }
            node_color.append(prototype_colors.get(char_data.get('prototype', ''), '#97C2FC'))

            # 根据中心性设置大小
            size = 20 + centrality.get(node, 0) * 50
            node_size.append(size)

        # 准备边数据
        edge_x = []
        edge_y = []
        edge_info = []

        for edge in graph.edges(data=True):
            x0, y0 = pos[edge[0]]
            x1, y1 = pos[edge[1]]
            edge_x.extend([x0, x1, None])
            edge_y.extend([y0, y1, None])

            edge_data = edge[2]
            rel_type = edge_data.get('relationship_type', '未知')
            strength = edge_data.get('strength', 0)
            edge_info.append(f"{edge[0]} → {edge[1]}<br>关系: {rel_type}<br>强度: {strength}")

        # 创建图形
        fig = go.Figure()

        # 添加边
        fig.add_trace(go.Scatter(x=edge_x, y=edge_y,
                                line=dict(width=2, color='#888'),
                                hoverinfo='none',
                                mode='lines',
                                name='关系'))

        # 添加节点
        fig.add_trace(go.Scatter(x=node_x, y=node_y,
                                mode='markers+text',
                                marker=dict(size=node_size,
                                          color=node_color,
                                          line=dict(width=2, color='white')),
                                text=[node for node in graph.nodes()],
                                textposition="middle center",
                                hovertext=node_text,
                                hoverinfo='text',
                                name='人物'))

        fig.update_layout(
            title="人物关系网络图",
            showlegend=False,
            hovermode='closest',
            margin=dict(b=20,l=5,r=5,t=40),
            annotations=[ dict(
                text="拖拽节点可调整位置，悬停查看详情",
                showarrow=False,
                xref="paper", yref="paper",
                x=0.005, y=-0.002,
                xanchor='left', yanchor='bottom',
                font=dict(color='gray', size=12)
            )],
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            plot_bgcolor='white'
        )

        return fig

    def create_timeline_view(self, character_name: Optional[str] = None,
                           category_filter: Optional[List[str]] = None) -> go.Figure:
        """创建时间线视图"""
        if character_name:
            # 角色时间线
            events = [ev for ev in self.system.events.events.values()
                     if any(p.get('name') == character_name for p in ev.get('participants', []))]
            milestones = self.system.milestones.get_character_milestones(character_name)
            title = f"时间线视图 - {character_name}"
        else:
            # 全局时间线
            events = list(self.system.events.events.values())
            milestones = []
            for char_name in self.system.characters.graph.nodes():
                milestones.extend(self.system.milestones.get_character_milestones(char_name))
            title = "全局时间线视图"

        # 分类过滤
        if category_filter:
            events = [ev for ev in events if ev.get('category') in category_filter]

        fig = go.Figure()

        # 添加事件
        if events:
            event_times = []
            event_names = []
            event_importances = []
            event_categories = []
            event_hover = []

            for event in events:
                try:
                    timestamp = pd.to_datetime(event['timestamp'])
                    event_times.append(timestamp)
                    event_names.append(event['name'])
                    event_importances.append(event.get('importance', 5))
                    event_categories.append(event.get('category', '其他'))

                    participants = ', '.join([p.get('name', '') for p in event.get('participants', [])])
                    hover_text = (f"事件: {event['name']}<br>"
                                f"时间: {timestamp.strftime('%Y-%m-%d %H:%M')}<br>"
                                f"分类: {event.get('category', '其他')}<br>"
                                f"重要性: {event.get('importance', 5)}<br>"
                                f"参与者: {participants}<br>"
                                f"地点: {event.get('location', '未知')}")
                    event_hover.append(hover_text)
                except Exception:
                    continue

            # 按分类着色
            category_colors = {cat: config['color'] for cat, config in self.system.events.EVENT_CATEGORIES.items()}
            colors = [category_colors.get(cat, '#97C2FC') for cat in event_categories]

            fig.add_trace(go.Scatter(
                x=event_times,
                y=event_importances,
                mode='markers',
                marker=dict(
                    size=[imp * 3 for imp in event_importances],
                    color=colors,
                    opacity=0.7,
                    line=dict(width=1, color='white')
                ),
                text=event_names,
                hovertext=event_hover,
                hoverinfo='text',
                name='事件'
            ))

        # 添加里程碑
        if milestones:
            milestone_times = []
            milestone_names = []
            milestone_importances = []
            milestone_hover = []

            for milestone in milestones:
                try:
                    timestamp = pd.to_datetime(milestone['timestamp'])
                    milestone_times.append(timestamp)
                    milestone_names.append(milestone['name'])
                    milestone_importances.append(milestone.get('importance', 5))

                    hover_text = (f"里程碑: {milestone['name']}<br>"
                                f"角色: {milestone.get('character_name', '未知')}<br>"
                                f"时间: {timestamp.strftime('%Y-%m-%d %H:%M')}<br>"
                                f"类型: {milestone.get('milestone_type', 'growth')}<br>"
                                f"重要性: {milestone.get('importance', 5)}")
                    milestone_hover.append(hover_text)
                except Exception:
                    continue

            fig.add_trace(go.Scatter(
                x=milestone_times,
                y=milestone_importances,
                mode='markers',
                marker=dict(
                    size=[imp * 2 for imp in milestone_importances],
                    color='gold',
                    symbol='star',
                    opacity=0.8,
                    line=dict(width=1, color='orange')
                ),
                text=milestone_names,
                hovertext=milestone_hover,
                hoverinfo='text',
                name='里程碑'
            ))

        fig.update_layout(
            title=title,
            xaxis_title='时间',
            yaxis_title='重要性',
            hovermode='closest',
            showlegend=True,
            plot_bgcolor='white',
            height=600
        )

        return fig

    def create_character_growth_chart(self, character_name: str) -> go.Figure:
        """创建角色成长轨迹图"""
        df = self.system.milestones.get_character_growth_dataframe(character_name)

        if df.empty:
            return go.Figure().add_annotation(
                text=f"角色 {character_name} 暂无里程碑数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5, showarrow=False
            )

        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('成长重要性趋势', '里程碑类型分布'),
            vertical_spacing=0.15,
            specs=[[{"secondary_y": True}], [{}]]
        )

        # 成长趋势线
        fig.add_trace(
            go.Scatter(
                x=df['timestamp'],
                y=df['importance'],
                mode='lines+markers',
                name='重要性',
                line=dict(color='#4ECDC4', width=3),
                marker=dict(size=8, color='#FF6B6B'),
                hovertemplate='<b>%{text}</b><br>时间: %{x}<br>重要性: %{y}<extra></extra>',
                text=df['name']
            ),
            row=1, col=1
        )

        # 累积重要性
        fig.add_trace(
            go.Scatter(
                x=df['timestamp'],
                y=df['cumulative_importance'],
                mode='lines',
                name='累积重要性',
                line=dict(color='#FFA502', width=2, dash='dash'),
                yaxis='y2'
            ),
            row=1, col=1, secondary_y=True
        )

        # 里程碑类型分布
        type_counts = df['milestone_type'].value_counts()
        colors = [self.system.milestones.MILESTONE_TYPES.get(t, {}).get('color', '#97C2FC')
                 for t in type_counts.index]

        fig.add_trace(
            go.Bar(
                x=type_counts.index,
                y=type_counts.values,
                name='类型分布',
                marker_color=colors,
                hovertemplate='类型: %{x}<br>数量: %{y}<extra></extra>'
            ),
            row=2, col=1
        )

        fig.update_layout(
            title=f"角色成长轨迹 - {character_name}",
            height=800,
            showlegend=True
        )

        fig.update_xaxes(title_text="时间", row=1, col=1)
        fig.update_yaxes(title_text="重要性", row=1, col=1)
        fig.update_yaxes(title_text="累积重要性", secondary_y=True, row=1, col=1)
        fig.update_xaxes(title_text="里程碑类型", row=2, col=1)
        fig.update_yaxes(title_text="数量", row=2, col=1)

        return fig

# ============== 系统聚合与统一接口 ==============

class NovelV3System:
    """小说人物档案管理系统 v3.0 Enhanced - 全功能版本"""

    def __init__(self):
        print("🚀 初始化小说人物档案管理系统 v3.0 Enhanced ...")
        self.characters = CharacterGraphV3()
        self.events = EventLibraryV3()
        self.milestones = MilestoneManagerV3()
        self.visualizer = VisualizationEngine(self)

        self.system_info = {
            'version': '3.0 Enhanced',
            'created_time': now_iso(),
            'updated_time': now_iso(),
            'description': '全功能辅助创作工具：人物塑造/剧情连接/事件记录/可视化分析',
            'features': [
                '动态人物关系网络',
                '全局事件库系统',
                '角色里程碑追踪',
                '交互式可视化界面',
                '数据分析与统计',
                'v2数据兼容导入'
            ]
        }
        print("✅ v3 Enhanced 系统初始化完成")
        print("   📊 人物关系管理 - 支持历史追踪和动态更新")
        print("   📅 全局事件库 - 支持分类、搜索和时间线")
        print("   🎯 里程碑系统 - 支持成长轨迹分析")
        print("   📈 可视化引擎 - 支持交互式图表")
        print("   🔄 数据兼容 - 支持v2系统迁移")

    def _touch(self):
        self.system_info['updated_time'] = now_iso()

    # ---- 统一新增接口 ----
    def add_character(self, character_data: Dict[str, Any]) -> str:
        name_key = self.characters.add_character(character_data)
        self._touch()
        return name_key

    def add_relationship(self, src_name: str, dst_name: str, relationship_data: Dict[str, Any]) -> str:
        rid = self.characters.add_relationship(src_name, dst_name, relationship_data)
        self._touch()
        return rid

    def add_event(self, event_data: Dict[str, Any]) -> str:
        eid = self.events.add_event(event_data)
        self._touch()
        return eid

    def add_participant(self, event_id: str, character_name: str, role: str = '参与者',
                        impact: int = 5, note: str = '') -> bool:
        # 允许传入别名；解析为标准 name_key
        resolved = self.characters.resolve_name(character_name)
        final_name = resolved or (character_name or '').strip()
        ok = self.events.add_participant(event_id, final_name, role, impact, note)
        self._touch()
        return ok

    def add_milestone(self, milestone_data: Dict[str, Any]) -> str:
        mid = self.milestones.add_milestone(milestone_data)
        self._touch()
        return mid

    def bind_milestone_to_event(self, milestone_id: str, event_id: str) -> bool:
        ok = self.milestones.bind_milestone_to_event(milestone_id, event_id)
        self._touch()
        return ok

    # ---- 查询接口 ----
    def get_character_complete_info(self, name_or_alias: str) -> Dict[str, Any]:
        """获取角色完整信息（增强版）"""
        base = self.characters.get_character(name_or_alias)
        if not base:
            return {}

        name_key = base['name']
        centrality_analysis = self.characters.analyze_centrality()
        growth_analysis = self.milestones.analyze_character_growth(name_key)

        info = {
            'basic_info': base,
            'relationships': {
                'outgoing': self.characters.get_outgoing_relationships(name_key),
                'incoming': self.characters.get_incoming_relationships(name_key),
                'statistics': self._get_character_relationship_stats(name_key)
            },
            'milestones': {
                'list': self.milestones.get_character_milestones(name_key),
                'analysis': growth_analysis
            },
            'events': {
                'participated': [
                    self.events.events[eid] for eid in sorted(self.events.index_by_person.get(name_key, set()))
                ],
                'statistics': self._get_character_event_stats(name_key)
            },
            'analysis': {
                'centrality': centrality_analysis.get(name_key, 0.0),
                'influence_rank': self._get_character_influence_rank(name_key, centrality_analysis),
                'activity_level': self._calculate_character_activity(name_key),
                'growth_summary': self._summarize_character_growth(growth_analysis)
            }
        }
        return info

    def _get_character_relationship_stats(self, name_key: str) -> Dict[str, Any]:
        """获取角色关系统计"""
        outgoing = self.characters.get_outgoing_relationships(name_key)
        incoming = self.characters.get_incoming_relationships(name_key)

        # 关系类型分布
        out_types = Counter([r['relationship_type'] for r in outgoing])
        in_types = Counter([r['relationship_type'] for r in incoming])

        # 平均关系强度
        out_strengths = [r.get('strength', 0) for r in outgoing]
        in_strengths = [r.get('strength', 0) for r in incoming]

        return {
            'total_outgoing': len(outgoing),
            'total_incoming': len(incoming),
            'outgoing_types': dict(out_types),
            'incoming_types': dict(in_types),
            'avg_outgoing_strength': sum(out_strengths) / len(out_strengths) if out_strengths else 0,
            'avg_incoming_strength': sum(in_strengths) / len(in_strengths) if in_strengths else 0,
            'mutual_relationships': len([r for r in outgoing
                                       if any(ir['source'] == r['target'] and ir['target'] == name_key
                                             for ir in incoming)])
        }

    def _get_character_event_stats(self, name_key: str) -> Dict[str, Any]:
        """获取角色事件统计"""
        event_ids = self.events.index_by_person.get(name_key, set())
        events = [self.events.events[eid] for eid in event_ids]

        if not events:
            return {}

        # 参与角色分布
        role_counts = Counter()
        impact_scores = []

        for event in events:
            for participant in event.get('participants', []):
                if participant.get('name') == name_key:
                    role_counts[participant.get('role', '参与者')] += 1
                    impact_scores.append(participant.get('emotional_impact', 5))

        # 事件分类参与
        category_counts = Counter([ev.get('category', '其他') for ev in events])

        return {
            'total_events': len(events),
            'role_distribution': dict(role_counts),
            'category_participation': dict(category_counts),
            'average_impact': sum(impact_scores) / len(impact_scores) if impact_scores else 0,
            'most_common_role': role_counts.most_common(1)[0][0] if role_counts else '无',
            'most_active_category': category_counts.most_common(1)[0][0] if category_counts else '无'
        }

    def _get_character_influence_rank(self, name_key: str, centrality_analysis: Dict[str, float]) -> int:
        """获取角色影响力排名"""
        sorted_chars = sorted(centrality_analysis.items(), key=lambda x: x[1], reverse=True)
        for i, (char, _) in enumerate(sorted_chars):
            if char == name_key:
                return i + 1
        return len(sorted_chars)

    def _calculate_character_activity(self, name_key: str) -> str:
        """计算角色活跃度"""
        event_count = len(self.events.index_by_person.get(name_key, set()))
        milestone_count = len(self.milestones.get_character_milestones(name_key))
        relationship_count = (len(self.characters.get_outgoing_relationships(name_key)) +
                            len(self.characters.get_incoming_relationships(name_key)))

        total_activity = event_count + milestone_count + relationship_count

        if total_activity >= 20:
            return "极高"
        elif total_activity >= 15:
            return "高"
        elif total_activity >= 10:
            return "中等"
        elif total_activity >= 5:
            return "低"
        else:
            return "极低"

    def _summarize_character_growth(self, growth_analysis: Dict[str, Any]) -> str:
        """总结角色成长情况"""
        if not growth_analysis:
            return "暂无成长数据"

        total = growth_analysis.get('total_milestones', 0)
        avg_importance = growth_analysis.get('average_importance', 0)
        growth_rate = growth_analysis.get('average_growth_rate', 0)

        if total == 0:
            return "暂无里程碑"
        elif total >= 10 and avg_importance >= 7:
            return "成长显著"
        elif total >= 5 and avg_importance >= 5:
            return "稳步成长"
        elif growth_rate > 0:
            return "缓慢成长"
        else:
            return "成长停滞"

    # ---- 可视化接口 ----
    def show_character_network(self, layout: str = 'spring', filter_strength: int = 0,
                              save_path: Optional[str] = None) -> go.Figure:
        """显示人物关系网络图"""
        fig = self.visualizer.create_character_relationship_network(layout, True, filter_strength)
        if save_path:
            fig.write_html(save_path)
            print(f"✅ 人物关系网络图已保存: {save_path}")
        return fig

    def show_timeline(self, character_name: Optional[str] = None,
                     category_filter: Optional[List[str]] = None,
                     save_path: Optional[str] = None) -> go.Figure:
        """显示时间线视图"""
        fig = self.visualizer.create_timeline_view(character_name, category_filter)
        if save_path:
            fig.write_html(save_path)
            print(f"✅ 时间线视图已保存: {save_path}")
        return fig

    def show_character_growth(self, character_name: str,
                             save_path: Optional[str] = None) -> go.Figure:
        """显示角色成长轨迹"""
        fig = self.visualizer.create_character_growth_chart(character_name)
        if save_path:
            fig.write_html(save_path)
            print(f"✅ 角色成长轨迹图已保存: {save_path}")
        return fig

    # ---- 系统统计和分析 ----
    def get_system_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息（增强版）"""
        char_analysis = self.characters.analyze_relationship_patterns()
        event_analysis = self.events.analyze_event_patterns()

        # 角色统计
        character_count = len(self.characters.graph.nodes())
        relationship_count = len(self.characters.graph.edges())

        # 事件统计
        event_count = len(self.events.events)

        # 里程碑统计
        milestone_count = len(self.milestones.milestones)

        # 活跃度分析
        most_central_chars = sorted(self.characters.analyze_centrality().items(),
                                  key=lambda x: x[1], reverse=True)[:5]

        # 最活跃的事件分类
        category_counts = Counter()
        for event in self.events.events.values():
            category_counts[event.get('category', '其他')] += 1

        return {
            'system_info': self.system_info,
            'data_counts': {
                'characters': character_count,
                'relationships': relationship_count,
                'events': event_count,
                'milestones': milestone_count,
                'total_data_points': character_count + relationship_count + event_count + milestone_count
            },
            'character_analysis': char_analysis,
            'event_analysis': event_analysis,
            'top_characters': [{'name': name, 'centrality': score} for name, score in most_central_chars],
            'event_categories': dict(category_counts.most_common()),
            'system_health': self._assess_system_health()
        }

    def _assess_system_health(self) -> Dict[str, Any]:
        """评估系统数据健康度"""
        char_count = len(self.characters.graph.nodes())
        rel_count = len(self.characters.graph.edges())
        event_count = len(self.events.events)
        milestone_count = len(self.milestones.milestones)

        # 数据完整性评分
        completeness_score = 0
        if char_count > 0:
            completeness_score += 25
        if rel_count > 0:
            completeness_score += 25
        if event_count > 0:
            completeness_score += 25
        if milestone_count > 0:
            completeness_score += 25

        # 数据丰富度评分
        richness_score = min(100, (char_count * 5 + rel_count * 3 + event_count * 2 + milestone_count))

        # 数据平衡性
        balance_score = 100
        if char_count > 0:
            rel_ratio = rel_count / char_count
            event_ratio = event_count / char_count
            milestone_ratio = milestone_count / char_count

            # 理想比例：每个角色应该有2-3个关系，1-2个事件，1-2个里程碑
            if rel_ratio < 1 or rel_ratio > 5:
                balance_score -= 20
            if event_ratio < 0.5 or event_ratio > 3:
                balance_score -= 20
            if milestone_ratio < 0.5 or milestone_ratio > 3:
                balance_score -= 20

        overall_score = (completeness_score + richness_score + balance_score) / 3

        if overall_score >= 80:
            health_status = "优秀"
        elif overall_score >= 60:
            health_status = "良好"
        elif overall_score >= 40:
            health_status = "一般"
        else:
            health_status = "需要改进"

        return {
            'overall_score': round(overall_score, 1),
            'health_status': health_status,
            'completeness_score': completeness_score,
            'richness_score': min(100, richness_score),
            'balance_score': max(0, balance_score),
            'recommendations': self._generate_recommendations(char_count, rel_count, event_count, milestone_count)
        }

    def _generate_recommendations(self, char_count: int, rel_count: int,
                                event_count: int, milestone_count: int) -> List[str]:
        """生成改进建议"""
        recommendations = []

        if char_count == 0:
            recommendations.append("建议添加主要角色信息")
        elif char_count < 3:
            recommendations.append("建议增加更多角色以丰富故事")

        if rel_count == 0 and char_count > 1:
            recommendations.append("建议建立角色间的关系")
        elif char_count > 0 and rel_count / char_count < 1:
            recommendations.append("建议增加角色关系以增强故事连接性")

        if event_count == 0:
            recommendations.append("建议添加关键事件")
        elif char_count > 0 and event_count / char_count < 0.5:
            recommendations.append("建议为角色添加更多相关事件")

        if milestone_count == 0 and char_count > 0:
            recommendations.append("建议为主要角色添加成长里程碑")
        elif char_count > 0 and milestone_count / char_count < 0.5:
            recommendations.append("建议增加角色发展里程碑")

        if not recommendations:
            recommendations.append("数据结构良好，可以考虑深化角色关系和事件细节")

        return recommendations

    def print_system_statistics(self):
        """打印系统统计信息"""
        stats = self.get_system_statistics()

        print("\n" + "="*70)
        print("📊 小说人物档案管理系统 v3.0 Enhanced 统计报告")
        print("="*70)

        # 系统信息
        print(f"🏷️  系统版本: {stats['system_info']['version']}")
        print(f"📅 创建时间: {stats['system_info']['created_time']}")
        print(f"🔄 更新时间: {stats['system_info']['updated_time']}")

        # 数据统计
        counts = stats['data_counts']
        print(f"\n📈 数据统计:")
        print(f"   角色数量: {counts['characters']}")
        print(f"   关系数量: {counts['relationships']}")
        print(f"   事件数量: {counts['events']}")
        print(f"   里程碑数量: {counts['milestones']}")
        print(f"   总数据点: {counts['total_data_points']}")

        # 系统健康度
        health = stats['system_health']
        print(f"\n🏥 系统健康度: {health['health_status']} ({health['overall_score']}/100)")
        print(f"   完整性: {health['completeness_score']}/100")
        print(f"   丰富度: {health['richness_score']}/100")
        print(f"   平衡性: {health['balance_score']}/100")

        # 推荐建议
        if health['recommendations']:
            print(f"\n💡 改进建议:")
            for i, rec in enumerate(health['recommendations'], 1):
                print(f"   {i}. {rec}")

        # 顶级角色
        if stats['top_characters']:
            print(f"\n🌟 影响力排行:")
            for i, char in enumerate(stats['top_characters'][:3], 1):
                print(f"   {i}. {char['name']} (中心性: {char['centrality']:.3f})")

        # 事件分类
        if stats['event_categories']:
            print(f"\n📊 事件分类分布:")
            for category, count in list(stats['event_categories'].items())[:5]:
                print(f"   {category}: {count}")

        print("="*70)

    # ---- 性能优化和错误处理 ----
    def validate_data_integrity(self) -> Dict[str, List[str]]:
        """验证数据完整性"""
        issues = {'errors': [], 'warnings': [], 'info': []}

        try:
            # 检查人物关系的一致性
            for src, dst, data in self.characters.graph.edges(data=True):
                if not self.characters.graph.has_node(src):
                    issues['errors'].append(f"关系源节点不存在: {src}")
                if not self.characters.graph.has_node(dst):
                    issues['errors'].append(f"关系目标节点不存在: {dst}")

                # 检查关系强度合理性
                strength = data.get('strength', 0)
                if not (1 <= strength <= 10):
                    issues['warnings'].append(f"关系强度异常: {src} → {dst} (强度: {strength})")

            # 检查事件参与者的存在性
            known_characters = set(self.characters.graph.nodes()) | set(self.characters.alias_to_name.keys())
            for event_id, event in self.events.events.items():
                for participant in event.get('participants', []):
                    name = participant.get('name', '').strip()
                    if name and name not in known_characters:
                        issues['warnings'].append(f"事件 [{event['name']}] 参与者未建档: {name}")

                # 检查时间格式
                try:
                    datetime.fromisoformat(event['timestamp'])
                except ValueError:
                    issues['errors'].append(f"事件时间格式错误: {event['name']} - {event['timestamp']}")

            # 检查里程碑时间顺序
            for char_name, milestone_ids in self.milestones.by_character.items():
                timestamps = []
                for mid in milestone_ids:
                    milestone = self.milestones.milestones.get(mid)
                    if milestone:
                        try:
                            ts = datetime.fromisoformat(milestone['timestamp'])
                            timestamps.append((ts, milestone['name']))
                        except ValueError:
                            issues['errors'].append(f"里程碑时间格式错误: {char_name} - {milestone['name']}")

                # 检查时间顺序
                timestamps.sort()
                for i in range(1, len(timestamps)):
                    if timestamps[i][0] < timestamps[i-1][0]:
                        issues['warnings'].append(f"里程碑时间顺序异常: {char_name}")
                        break

            # 性能警告
            if len(self.characters.graph.nodes()) > 100:
                issues['info'].append("角色数量较多，建议使用筛选功能提升可视化性能")
            if len(self.events.events) > 500:
                issues['info'].append("事件数量较多，建议使用分类和时间范围筛选")

        except Exception as e:
            issues['errors'].append(f"数据验证过程中发生错误: {str(e)}")

        return issues

    def optimize_performance(self) -> Dict[str, Any]:
        """性能优化"""
        optimization_results = {}

        try:
            # 清理缓存
            self.events._timeline_cache = None
            self.events._cache_dirty = True
            self.milestones._growth_cache.clear()
            self.milestones._cache_dirty.clear()

            # 优化关系历史记录
            cleaned_history = 0
            for edge_key, history in self.characters.relationship_history.items():
                if len(history) > 50:  # 保留最近50条记录
                    self.characters.relationship_history[edge_key] = history[-50:]
                    cleaned_history += len(history) - 50

            # 重建索引
            self.events.index_by_person.clear()
            self.events.index_by_location.clear()
            self.events.index_by_tag.clear()
            self.events.index_by_category.clear()
            self.events.index_by_importance.clear()

            for event_id, event in self.events.events.items():
                # 重建人物索引
                for participant in event.get('participants', []):
                    name = participant.get('name', '').strip()
                    if name:
                        self.events.index_by_person[name].add(event_id)

                # 重建其他索引
                if event.get('location'):
                    self.events.index_by_location[event['location']].add(event_id)
                for tag in event.get('tags', []):
                    self.events.index_by_tag[tag].add(event_id)
                self.events.index_by_category[event.get('category', '其他')].add(event_id)
                self.events.index_by_importance[event.get('importance', 5)].add(event_id)

            optimization_results = {
                'cleaned_history_records': cleaned_history,
                'rebuilt_indices': True,
                'cleared_caches': True,
                'optimization_time': now_iso()
            }

            print("✅ 性能优化完成")
            if cleaned_history > 0:
                print(f"   清理历史记录: {cleaned_history} 条")
            print("   重建索引: 完成")
            print("   清理缓存: 完成")

        except Exception as e:
            optimization_results['error'] = str(e)
            print(f"❌ 性能优化失败: {e}")

        return optimization_results

    def safe_operation(self, operation_name: str, operation_func, *args, **kwargs):
        """安全操作包装器"""
        try:
            result = operation_func(*args, **kwargs)
            return {'success': True, 'result': result}
        except Exception as e:
            error_msg = f"{operation_name} 操作失败: {str(e)}"
            print(f"❌ {error_msg}")
            return {'success': False, 'error': error_msg, 'result': None}

    # ---- 批量操作接口 ----
    def batch_add_characters(self, characters_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量添加角色"""
        results = {'success': [], 'failed': [], 'total': len(characters_data)}

        for char_data in characters_data:
            try:
                char_id = self.add_character(char_data)
                results['success'].append({'name': char_data.get('name'), 'id': char_id})
            except Exception as e:
                results['failed'].append({'name': char_data.get('name'), 'error': str(e)})

        print(f"✅ 批量添加角色完成: 成功 {len(results['success'])}, 失败 {len(results['failed'])}")
        return results

    def batch_add_events(self, events_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量添加事件"""
        results = {'success': [], 'failed': [], 'total': len(events_data)}

        for event_data in events_data:
            try:
                event_id = self.add_event(event_data)
                results['success'].append({'name': event_data.get('name'), 'id': event_id})
            except Exception as e:
                results['failed'].append({'name': event_data.get('name'), 'error': str(e)})

        print(f"✅ 批量添加事件完成: 成功 {len(results['success'])}, 失败 {len(results['failed'])}")
        return results

    # ---- 便捷查询接口 ----
    def quick_search(self, query: str, search_type: str = 'all') -> Dict[str, List[Dict[str, Any]]]:
        """快速搜索"""
        results = {'characters': [], 'events': [], 'milestones': []}
        query_lower = query.lower()

        try:
            if search_type in ['all', 'characters']:
                for char_name, char_data in self.characters.graph.nodes(data=True):
                    if (query_lower in char_name.lower() or
                        query_lower in char_data.get('description', '').lower() or
                        any(query_lower in tag.lower() for tag in char_data.get('tags', []))):
                        results['characters'].append(char_data)

            if search_type in ['all', 'events']:
                for event in self.events.events.values():
                    if (query_lower in event['name'].lower() or
                        query_lower in event.get('description', '').lower() or
                        any(query_lower in tag.lower() for tag in event.get('tags', []))):
                        results['events'].append(event)

            if search_type in ['all', 'milestones']:
                for milestone in self.milestones.milestones.values():
                    if (query_lower in milestone['name'].lower() or
                        query_lower in milestone.get('description', '').lower()):
                        results['milestones'].append(milestone)

        except Exception as e:
            print(f"❌ 搜索失败: {e}")

        return results

    def get_event_complete_info(self, event_id: str) -> Dict[str, Any]:
        ev = self.events.get_event(event_id)
        return ev or {}

    def query_three_elements(self, person: Optional[str] = None,
                             matter_keywords: Optional[List[str]] = None,
                             place: Optional[str] = None,
                             time_range: Optional[Tuple[str, str]] = None) -> List[Dict[str, Any]]:
        persons = [person] if person else None
        return self.events.query_events(persons=persons, keywords=matter_keywords,
                                        location=place, time_range=time_range)

    # ---- v2 -> v3 数据迁移 ----
    def import_from_v2(self, v2_system) -> Dict[str, Any]:
        """接受 v2 的 NovelMultiGraphSystem 或测试版，迁移到 v3。
        返回映射表：{'char_id_to_name': {...}, 'event_id_map': {...}}
        """
        mapping = {
            'char_id_to_name': {},
            'event_id_map': {},
        }
        # 人物
        for old_id, c in getattr(v2_system.character_manager, 'characters', {}).items():
            name_key = self.add_character(c)  # 会自动唯一化
            mapping['char_id_to_name'][old_id] = name_key
        # 关系（v2多为无向，导入为双向有向）
        g = v2_system.character_manager.graph
        for u, v, data in g.edges(data=True):
            src = mapping['char_id_to_name'].get(u)
            dst = mapping['char_id_to_name'].get(v)
            if not (src and dst):
                continue
            self.add_relationship(src, dst, data)
            self.add_relationship(dst, src, data)
        # 事件
        for eid, ev in getattr(v2_system.event_manager, 'events', {}).items():
            new_id = self.add_event(ev)
            mapping['event_id_map'][eid] = new_id
        # 参与（如果 v2 存在 ParticipationManager）
        part_mgr = getattr(v2_system, 'participation_manager', None)
        if part_mgr:
            for _, pdata in getattr(part_mgr, 'particiations', {}).items():  # 兼容性容错
                cid = pdata.get('character_id')
                old_eid = pdata.get('event_id')
                name_key = mapping['char_id_to_name'].get(cid)
                new_eid = mapping['event_id_map'].get(old_eid)
                if name_key and new_eid:
                    self.add_participant(new_eid, name_key, pdata.get('role', '参与者'),
                                         pdata.get('emotional_impact', 5), pdata.get('impact_description', ''))
        # 里程碑（如果 v2 有）
        ms_mgr = getattr(v2_system, 'milestone_manager', None)
        if ms_mgr:
            for mid, m in getattr(ms_mgr, 'milestones', {}).items():
                name_key = mapping['char_id_to_name'].get(m.get('character_id')) or m.get('character_name', '')
                new_mid = self.add_milestone({**m, 'character_name': name_key})
                # 绑定事件（若 v2 数据存在 event_id 字段）
                ev_id = m.get('event_id')
                if ev_id:
                    new_eid = mapping['event_id_map'].get(ev_id)
                    if new_eid:
                        self.bind_milestone_to_event(new_mid, new_eid)
        return mapping

    # ---- 导入/导出 ----
    def export_to_json(self, filename: str = 'novel_v3_data.json') -> bool:
        try:
            data = {
                'system_info': self.system_info,
                'characters': {
                    'nodes': list(self.characters.graph.nodes(data=True)),
                    'edges': list(self.characters.graph.edges(data=True)),
                    'alias': self.characters.alias_to_name,
                },
                'events': self.events.events,
                'indices': {
                    'by_person': {k: list(v) for k, v in self.events.index_by_person.items()},
                    'by_location': {k: list(v) for k, v in self.events.index_by_location.items()},
                    'by_tag': {k: list(v) for k, v in self.events.index_by_tag.items()},
                },
                'milestones': {
                    'nodes': self.milestones.milestones,
                    'edges': list(self.milestones.graph.edges(data=True)),
                },
                'export_time': now_iso(),
            }
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            print(f"✅ v3 数据导出完成: {filename}")
            return True
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return False

    def import_from_json(self, filename: str = 'novel_v3_data.json') -> bool:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            # characters
            self.characters.graph = nx.DiGraph()
            self.characters.alias_to_name = data['characters'].get('alias', {})
            self.characters.name_to_uid.clear(); self.characters.uid_to_name.clear()
            for name_key, attrs in data['characters']['nodes']:
                self.characters.graph.add_node(name_key, **attrs)
                uid = attrs.get('uid', '')
                if uid:
                    self.characters.name_to_uid[name_key] = uid
                    self.characters.uid_to_name[uid] = name_key
            for src, dst, ed in data['characters']['edges']:
                self.characters.graph.add_edge(src, dst, **ed)
            # events
            self.events.events = data['events']
            self.events.index_by_person = defaultdict(set, {k: set(v) for k, v in data['indices']['by_person'].items()})
            self.events.index_by_location = defaultdict(set, {k: set(v) for k, v in data['indices']['by_location'].items()})
            self.events.index_by_tag = defaultdict(set, {k: set(v) for k, v in data['indices']['by_tag'].items()})
            # milestones
            self.milestones.graph = nx.DiGraph()
            self.milestones.milestones = data['milestones']['nodes']
            self.milestones.by_character.clear()
            for mid, m in self.milestones.milestones.items():
                self.milestones.graph.add_node(mid, **m)
                nk = m.get('character_name', '')
                if nk:
                    self.milestones.by_character[nk].append(mid)
            for pre, dep, ed in data['milestones']['edges']:
                self.milestones.graph.add_edge(pre, dep, **ed)
            print(f"✅ v3 数据导入完成: {filename}")
            return True
        except Exception as e:
            print(f"❌ 导入失败: {e}")
            return False

    # ---- 一致性检查与便捷查询/示例 ----
    def run_consistency_checks(self, rules: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        rules = rules or {}
        report = {'errors': [], 'warnings': [], 'info': []}
        self._check_participant_names(report)
        self._check_milestone_time_order(report)
        self._check_relationship_conflicts(report, rules)
        report['summary'] = {
            'errors': len(report['errors']),
            'warnings': len(report['warnings']),
            'info': len(report['info'])
        }
        return report

    def _check_participant_names(self, report: Dict[str, List[str]]):
        known = set(self.characters.graph.nodes) | set(self.characters.alias_to_name.keys())
        for eid, ev in self.events.events.items():
            for p in ev.get('participants', []):
                name = (p.get('name') or '').strip()
                if name and name not in known:
                    report['warnings'].append(f"事件[{ev.get('name')}] 参与者未建档: {name}")

    def _check_milestone_time_order(self, report: Dict[str, List[str]]):
        for name_key, mids in self.milestones.by_character.items():
            prev = None
            for mid in mids:
                m = self.milestones.milestones.get(mid)
                if not m:
                    continue
                try:
                    ts = datetime.fromisoformat(m.get('timestamp'))
                except Exception:
                    report['errors'].append(f"里程碑时间格式错误: {name_key} - {m.get('name')} - {m.get('timestamp')}")
                    continue
                if prev and ts < prev:
                    report['warnings'].append(f"里程碑时间倒序: {name_key} - {m.get('name')}")
                prev = ts

    def _check_relationship_conflicts(self, report: Dict[str, List[str]], rules: Dict[str, Any]):
        conflict_pairs = {('恋人','敌人'), ('朋友','敌人')}
        threshold = int(rules.get('conflict_strength', 7))
        g = self.characters.graph
        for u, v in g.edges():
            d = g[u][v]
            t = d.get('relationship_type')
            rev = g[v][u] if g.has_edge(v, u) else None
            if not rev:
                continue
            t2 = rev.get('relationship_type')
            if (t, t2) in conflict_pairs or (t2, t) in conflict_pairs:
                s1 = int(d.get('strength', 0))
                s2 = int(rev.get('strength', 0))
                if s1 >= threshold and s2 >= threshold:
                    report['warnings'].append(f"关系冲突: {u}↔{v} ({t} vs {t2}, 强度 {s1}/{s2})")

    # 便捷查询封装
    def query_by_persons(self, persons: List[str], **kwargs) -> List[Dict[str, Any]]:
        return self.events.query_events(persons=persons, **kwargs)

    def query_by_tags(self, tags: List[str], **kwargs) -> List[Dict[str, Any]]:
        kw = list(tags or [])
        return self.events.query_events(keywords=kw, **kwargs)

    def query_by_location(self, location: str, **kwargs) -> List[Dict[str, Any]]:
        return self.events.query_events(location=location, **kwargs)

    # 示例数据扩展（不自动执行）
    def create_sample_data_v3(self) -> Dict[str, Any]:
        out = {}
        # 角色
        self.add_character({'name': '苏凝雪', 'prototype': '伙伴', 'narrative_role': '女主', 'tags': ['聪慧','冷静']})
        self.add_character({'name': '赵无双', 'prototype': '反派', 'narrative_role': '对手', 'tags': ['野心','阴谋']})
        self.add_character({'name': '黑衣人', 'prototype': '反派', 'narrative_role': '爪牙', 'tags': ['神秘']})
        # 关系（非对称）
        self.add_relationship('李风', '苏凝雪', {'relationship_type': '恋人', 'strength': 8, 'description': '李风倾慕'})
        self.add_relationship('苏凝雪', '李风', {'relationship_type': '朋友', 'strength': 6, 'description': '苏凝雪未明心意'})
        self.add_relationship('李风', '赵无双', {'relationship_type': '敌人', 'strength': 8})
        self.add_relationship('赵无双', '李风', {'relationship_type': '敌人', 'strength': 9})
        # 事件
        e1 = self.add_event({'name': '山路遭遇', 'timestamp': '2023-05-02T09:00:00', 'location': '天山脚下', 'tags': ['遭遇','冲突'], 'importance': 7})
        self.add_participant(e1, '李风', '主导者', 7)
        self.add_participant(e1, '黑衣人', '受害者', 5, '被击退')
        e2 = self.add_event({'name': '夜探古堡', 'timestamp': '2023-05-03T22:00:00', 'location': '古堡', 'tags': ['潜入','谜团'], 'importance': 8})
        self.add_participant(e2, '苏凝雪', '主导者', 8)
        self.add_participant(e2, '李风', '参与者', 7)
        e3 = self.add_event({'name': '宗门大比', 'timestamp': '2023-05-10T10:00:00', 'location': '宗门广场', 'tags': ['比试','转折'], 'importance': 9})
        self.add_participant(e3, '李风', '主导者', 9)
        self.add_participant(e3, '赵无双', '对手', 8)
        # 里程碑
        m1 = self.add_milestone({'character_name': '李风', 'name': '领悟剑心', 'timestamp': '2023-05-02T20:00:00', 'milestone_type': 'growth', 'importance': 8})
        self.bind_milestone_to_event(m1, e1)
        m2 = self.add_milestone({'character_name': '苏凝雪', 'name': '破除心魔', 'timestamp': '2023-05-04T08:00:00', 'milestone_type': 'achievement', 'importance': 7})
        self.bind_milestone_to_event(m2, e2)
        out['events'] = [e1, e2, e3]; out['milestones'] = [m1, m2]
        return out

# 轻量演示
if __name__ == '__main__':
    sys = NovelV3System()
    li = sys.add_character({'name': '李风', 'prototype': '英雄', 'narrative_role': '主角'})
    bai = sys.add_character({'name': '白大师', 'prototype': '导师', 'narrative_role': '配角'})
    sys.add_relationship('李风', '白大师', {'relationship_type': '师徒', 'strength': 9, 'description': '尊师重道'})
    eid = sys.add_event({'name': '初见', 'location': '天山', 'tags': ['邂逅']})
    sys.add_participant(eid, '李风', '参与者', 7)
    sys.add_participant(eid, '白大师', '参与者', 6)
    mid = sys.add_milestone({'character_name': '李风', 'name': '立志行侠仗义', 'milestone_type': 'growth'})
    sys.bind_milestone_to_event(mid, eid)
    info = sys.get_character_complete_info('李风')
    print(f"人物中央性: {info['analysis']['centrality']:.3f}")
    res = sys.query_three_elements(person='李风', matter_keywords=['邂逅'], place='天山')
    print(f"人事地查询结果数量: {len(res)}")

