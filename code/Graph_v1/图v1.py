# -*- coding: utf-8 -*-
"""
小说人物档案管理系统 - 基于NetworkX图数据结构
作者: AugCode
功能: 将SQLAlchemy数据库系统转换为NetworkX图结构，实现人物关系可视化
"""

import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from pyvis.network import Network
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class NovelGraphSystem:
    """小说人物档案管理图系统"""
    
    def __init__(self):
        """初始化图系统"""
        # 使用多重有向图支持多种关系类型
        self.graph = nx.MultiDiGraph()
        
        # 节点类型定义
        self.NODE_TYPES = {
            'character': '人物',
            'event': '事件', 
            'milestone': '里程碑'
        }
        
        # 边类型定义
        self.EDGE_TYPES = {
            'character_relationship': '人物关系',
            'character_event': '人物参与事件',
            'character_milestone': '人物里程碑',
            'event_milestone': '事件里程碑'
        }
        
        # 关系类型定义
        self.RELATIONSHIP_TYPES = {
            '师徒': {'color': '#FF6B6B', 'strength_range': (7, 10)},
            '朋友': {'color': '#4ECDC4', 'strength_range': (5, 9)},
            '敌人': {'color': '#FF4757', 'strength_range': (3, 8)},
            '恋人': {'color': '#FF3838', 'strength_range': (8, 10)},
            '亲人': {'color': '#FFA502', 'strength_range': (6, 10)},
            '同事': {'color': '#70A1FF', 'strength_range': (3, 7)},
            '陌生人': {'color': '#A4B0BE', 'strength_range': (1, 3)}
        }
        
        print("✅ 小说人物档案管理图系统初始化完成")
    
    def _generate_id(self, prefix: str = "") -> str:
        """生成唯一ID"""
        return f"{prefix}_{uuid.uuid4().hex[:8]}" if prefix else uuid.uuid4().hex[:8]
    
    def _validate_node_attributes(self, node_type: str, attributes: Dict[str, Any]) -> Dict[str, Any]:
        """验证并标准化节点属性"""
        base_attrs = {
            'node_type': node_type,
            'id': attributes.get('id', self._generate_id(node_type)),
            'name': attributes.get('name', '未命名'),
            'created_time': datetime.now().isoformat(),
            'updated_time': datetime.now().isoformat()
        }
        
        if node_type == 'character':
            # 人物节点统一属性结构
            character_attrs = {
                'prototype': attributes.get('prototype', ''),
                'narrative_role': attributes.get('narrative_role', ''),
                'core_identity': attributes.get('core_identity', {}),
                'internal_dimension': attributes.get('internal_dimension', {}),
                'external_dimension': attributes.get('external_dimension', {}),
                'tags': attributes.get('tags', []),
                'color': '#97C2FC',  # 默认蓝色
                'size': 30
            }
            base_attrs.update(character_attrs)
            
        elif node_type == 'event':
            # 事件节点统一属性结构
            event_attrs = {
                'description': attributes.get('description', ''),
                'timestamp': attributes.get('timestamp', datetime.now().isoformat()),
                'location': attributes.get('location', ''),
                'tags': attributes.get('tags', []),
                'color': '#FFAA00',  # 默认橙色
                'size': 25
            }
            base_attrs.update(event_attrs)
            
        elif node_type == 'milestone':
            # 里程碑节点统一属性结构
            milestone_attrs = {
                'character_id': attributes.get('character_id', ''),
                'event_id': attributes.get('event_id', ''),
                'description': attributes.get('description', ''),
                'psychological_change': attributes.get('psychological_change', {}),
                'values_change': attributes.get('values_change', {}),
                'goals_change': attributes.get('goals_change', {}),
                'proof': attributes.get('proof', ''),
                'color': '#7BE19C',  # 默认绿色
                'size': 20
            }
            base_attrs.update(milestone_attrs)
        
        return base_attrs
    
    def _validate_edge_attributes(self, edge_type: str, attributes: Dict[str, Any]) -> Dict[str, Any]:
        """验证并标准化边属性"""
        base_attrs = {
            'edge_type': edge_type,
            'id': attributes.get('id', self._generate_id('edge')),
            'created_time': datetime.now().isoformat(),
            'updated_time': datetime.now().isoformat()
        }
        
        if edge_type == 'character_relationship':
            # 人物关系边统一属性结构
            rel_type = attributes.get('relationship_type', '朋友')
            rel_config = self.RELATIONSHIP_TYPES.get(rel_type, self.RELATIONSHIP_TYPES['朋友'])
            
            relationship_attrs = {
                'relationship_type': rel_type,
                'strength': attributes.get('strength', 5),
                'status': attributes.get('status', '正常'),
                'description': attributes.get('description', ''),
                'color': rel_config['color'],
                'weight': attributes.get('strength', 5),
                'label': f"{rel_type}({attributes.get('strength', 5)})"
            }
            base_attrs.update(relationship_attrs)
            
        elif edge_type == 'character_event':
            # 人物事件边统一属性结构
            event_attrs = {
                'role': attributes.get('role', '参与者'),
                'impact_description': attributes.get('impact_description', ''),
                'color': '#848484',
                'weight': 3,
                'label': attributes.get('role', '参与者')
            }
            base_attrs.update(event_attrs)
            
        elif edge_type in ['character_milestone', 'event_milestone']:
            # 里程碑边统一属性结构
            milestone_attrs = {
                'description': attributes.get('description', ''),
                'color': '#A0A0A0',
                'weight': 2,
                'label': '里程碑'
            }
            base_attrs.update(milestone_attrs)
        
        return base_attrs
    
    # ==================== 节点管理方法 ====================
    
    def add_character_node(self, attributes: Dict[str, Any]) -> str:
        """添加人物节点"""
        validated_attrs = self._validate_node_attributes('character', attributes)
        node_id = validated_attrs['id']
        
        self.graph.add_node(node_id, **validated_attrs)
        print(f"✅ 添加人物节点: {validated_attrs['name']} (ID: {node_id})")
        return node_id
    
    def add_event_node(self, attributes: Dict[str, Any]) -> str:
        """添加事件节点"""
        validated_attrs = self._validate_node_attributes('event', attributes)
        node_id = validated_attrs['id']
        
        self.graph.add_node(node_id, **validated_attrs)
        print(f"✅ 添加事件节点: {validated_attrs['name']} (ID: {node_id})")
        return node_id
    
    def add_milestone_node(self, attributes: Dict[str, Any]) -> str:
        """添加里程碑节点"""
        validated_attrs = self._validate_node_attributes('milestone', attributes)
        node_id = validated_attrs['id']
        
        self.graph.add_node(node_id, **validated_attrs)
        print(f"✅ 添加里程碑节点: {validated_attrs['name']} (ID: {node_id})")
        return node_id
    
    def update_node(self, node_id: str, attributes: Dict[str, Any]) -> bool:
        """更新节点属性"""
        if not self.graph.has_node(node_id):
            print(f"❌ 节点不存在: {node_id}")
            return False
        
        # 获取现有属性并更新
        current_attrs = self.graph.nodes[node_id]
        current_attrs.update(attributes)
        current_attrs['updated_time'] = datetime.now().isoformat()
        
        print(f"✅ 更新节点: {node_id}")
        return True
    
    def delete_node(self, node_id: str) -> bool:
        """删除节点及其所有边"""
        if not self.graph.has_node(node_id):
            print(f"❌ 节点不存在: {node_id}")
            return False

        node_name = self.graph.nodes[node_id].get('name', node_id)
        self.graph.remove_node(node_id)
        print(f"✅ 删除节点: {node_name} (ID: {node_id})")
        return True

    # ==================== 边管理方法 ====================

    def add_character_relationship(self, source_id: str, target_id: str, attributes: Dict[str, Any]) -> str:
        """添加人物关系边"""
        if not (self.graph.has_node(source_id) and self.graph.has_node(target_id)):
            print(f"❌ 节点不存在: {source_id} 或 {target_id}")
            return ""

        validated_attrs = self._validate_edge_attributes('character_relationship', attributes)
        edge_id = validated_attrs['id']

        self.graph.add_edge(source_id, target_id, key=edge_id, **validated_attrs)

        source_name = self.graph.nodes[source_id]['name']
        target_name = self.graph.nodes[target_id]['name']
        rel_type = validated_attrs['relationship_type']
        print(f"✅ 添加人物关系: {source_name} -> {target_name} ({rel_type})")
        return edge_id

    def add_character_event_relation(self, character_id: str, event_id: str, attributes: Dict[str, Any]) -> str:
        """添加人物参与事件的关系"""
        if not (self.graph.has_node(character_id) and self.graph.has_node(event_id)):
            print(f"❌ 节点不存在: {character_id} 或 {event_id}")
            return ""

        validated_attrs = self._validate_edge_attributes('character_event', attributes)
        edge_id = validated_attrs['id']

        self.graph.add_edge(character_id, event_id, key=edge_id, **validated_attrs)

        char_name = self.graph.nodes[character_id]['name']
        event_name = self.graph.nodes[event_id]['name']
        role = validated_attrs['role']
        print(f"✅ 添加人物事件关系: {char_name} -> {event_name} ({role})")
        return edge_id

    def add_milestone_relation(self, character_id: str, event_id: str, milestone_id: str) -> Tuple[str, str]:
        """添加里程碑相关的边"""
        edge_ids = []

        # 人物-里程碑边
        if self.graph.has_node(character_id) and self.graph.has_node(milestone_id):
            char_milestone_attrs = self._validate_edge_attributes('character_milestone', {})
            char_edge_id = char_milestone_attrs['id']
            self.graph.add_edge(character_id, milestone_id, key=char_edge_id, **char_milestone_attrs)
            edge_ids.append(char_edge_id)

        # 事件-里程碑边
        if self.graph.has_node(event_id) and self.graph.has_node(milestone_id):
            event_milestone_attrs = self._validate_edge_attributes('event_milestone', {})
            event_edge_id = event_milestone_attrs['id']
            self.graph.add_edge(event_id, milestone_id, key=event_edge_id, **event_milestone_attrs)
            edge_ids.append(event_edge_id)

        print(f"✅ 添加里程碑关系: {len(edge_ids)} 条边")
        return tuple(edge_ids)

    def update_edge(self, source_id: str, target_id: str, edge_key: str, attributes: Dict[str, Any]) -> bool:
        """更新边属性"""
        if not self.graph.has_edge(source_id, target_id, edge_key):
            print(f"❌ 边不存在: {source_id} -> {target_id} (key: {edge_key})")
            return False

        # 获取现有属性并更新
        current_attrs = self.graph.edges[source_id, target_id, edge_key]
        current_attrs.update(attributes)
        current_attrs['updated_time'] = datetime.now().isoformat()

        print(f"✅ 更新边: {source_id} -> {target_id}")
        return True

    def delete_edge(self, source_id: str, target_id: str, edge_key: str = None) -> bool:
        """删除边"""
        if edge_key:
            if not self.graph.has_edge(source_id, target_id, edge_key):
                print(f"❌ 边不存在: {source_id} -> {target_id} (key: {edge_key})")
                return False
            self.graph.remove_edge(source_id, target_id, edge_key)
        else:
            if not self.graph.has_edge(source_id, target_id):
                print(f"❌ 边不存在: {source_id} -> {target_id}")
                return False
            self.graph.remove_edge(source_id, target_id)

        print(f"✅ 删除边: {source_id} -> {target_id}")
        return True

    # ==================== 查询方法 ====================

    def get_character(self, character_id: str) -> Optional[Dict[str, Any]]:
        """获取人物信息"""
        if not self.graph.has_node(character_id):
            return None
        return dict(self.graph.nodes[character_id])

    def get_characters_by_type(self, prototype: str = None, narrative_role: str = None) -> List[Dict[str, Any]]:
        """根据类型获取人物列表"""
        characters = []
        for node_id, attrs in self.graph.nodes(data=True):
            if attrs.get('node_type') == 'character':
                if prototype and attrs.get('prototype') != prototype:
                    continue
                if narrative_role and attrs.get('narrative_role') != narrative_role:
                    continue
                characters.append({'id': node_id, **attrs})
        return characters

    def get_relationships(self, character_id: str) -> List[Dict[str, Any]]:
        """获取人物的所有关系"""
        relationships = []

        # 出边（作为源节点的关系）
        for target_id in self.graph.successors(character_id):
            for edge_key, edge_attrs in self.graph[character_id][target_id].items():
                if edge_attrs.get('edge_type') == 'character_relationship':
                    relationships.append({
                        'direction': 'outgoing',
                        'target_id': target_id,
                        'target_name': self.graph.nodes[target_id]['name'],
                        'edge_key': edge_key,
                        **edge_attrs
                    })

        # 入边（作为目标节点的关系）
        for source_id in self.graph.predecessors(character_id):
            for edge_key, edge_attrs in self.graph[source_id][character_id].items():
                if edge_attrs.get('edge_type') == 'character_relationship':
                    relationships.append({
                        'direction': 'incoming',
                        'source_id': source_id,
                        'source_name': self.graph.nodes[source_id]['name'],
                        'edge_key': edge_key,
                        **edge_attrs
                    })

        return relationships

    def find_path_between_characters(self, source_id: str, target_id: str) -> Optional[List[str]]:
        """查找两个人物之间的最短路径"""
        try:
            # 只考虑人物节点之间的路径
            character_subgraph = self.graph.subgraph([
                node_id for node_id, attrs in self.graph.nodes(data=True)
                if attrs.get('node_type') == 'character'
            ])

            path = nx.shortest_path(character_subgraph, source_id, target_id)
            return path
        except nx.NetworkXNoPath:
            return None

    def get_character_events(self, character_id: str) -> List[Dict[str, Any]]:
        """获取人物参与的所有事件"""
        events = []
        for target_id in self.graph.successors(character_id):
            target_attrs = self.graph.nodes[target_id]
            if target_attrs.get('node_type') == 'event':
                # 获取边信息
                edge_info = None
                for edge_key, edge_attrs in self.graph[character_id][target_id].items():
                    if edge_attrs.get('edge_type') == 'character_event':
                        edge_info = edge_attrs
                        break

                events.append({
                    'event_id': target_id,
                    'event_name': target_attrs['name'],
                    'role': edge_info.get('role', '参与者') if edge_info else '参与者',
                    'impact': edge_info.get('impact_description', '') if edge_info else '',
                    **target_attrs
                })

        return events

    # ==================== 数据迁移方法 ====================

    def import_from_sqlalchemy(self, db_url: str = 'sqlite:///novel_characters.db'):
        """从SQLAlchemy数据库导入数据"""
        try:
            # 导入数据库模型（假设在同一目录下）
            from 数据库草稿 import NovelManagementSystem, Character, Event, Milestone, CharacterRelationship

            # 连接数据库
            engine = create_engine(db_url)
            SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
            db = SessionLocal()

            novel_system = NovelManagementSystem()

            print("🔄 开始从SQLAlchemy数据库导入数据...")

            # 导入人物
            characters = db.query(Character).all()
            character_mapping = {}
            for char in characters:
                char_attrs = {
                    'id': char.id,
                    'name': char.name,
                    'prototype': char.prototype or '',
                    'narrative_role': char.narrative_role or '',
                    'core_identity': char.core_identity or {},
                    'internal_dimension': char.internal_dimension or {},
                    'external_dimension': char.external_dimension or {},
                    'tags': char.tags or []
                }
                node_id = self.add_character_node(char_attrs)
                character_mapping[char.id] = node_id

            # 导入事件
            events = db.query(Event).all()
            event_mapping = {}
            for event in events:
                event_attrs = {
                    'id': event.id,
                    'name': event.name,
                    'description': event.description or '',
                    'timestamp': event.timestamp.isoformat() if event.timestamp else datetime.now().isoformat(),
                    'location': event.location or '',
                    'tags': event.tags or []
                }
                node_id = self.add_event_node(event_attrs)
                event_mapping[event.id] = node_id

            # 导入里程碑
            milestones = db.query(Milestone).all()
            milestone_mapping = {}
            for milestone in milestones:
                milestone_attrs = {
                    'id': milestone.id,
                    'name': f"里程碑_{milestone.id[:8]}",
                    'character_id': milestone.character_id,
                    'event_id': milestone.event_id,
                    'description': milestone.description or '',
                    'psychological_change': milestone.psychological_change or {},
                    'values_change': milestone.values_change or {},
                    'goals_change': milestone.goals_change or {},
                    'proof': milestone.proof or ''
                }
                node_id = self.add_milestone_node(milestone_attrs)
                milestone_mapping[milestone.id] = node_id

                # 添加里程碑相关的边
                if milestone.character_id in character_mapping and milestone.event_id in event_mapping:
                    self.add_milestone_relation(
                        character_mapping[milestone.character_id],
                        event_mapping[milestone.event_id],
                        node_id
                    )

            # 导入人物关系
            relationships = db.query(CharacterRelationship).all()
            for rel in relationships:
                if rel.source_character_id in character_mapping and rel.target_character_id in character_mapping:
                    rel_attrs = {
                        'id': rel.id,
                        'relationship_type': rel.type or '朋友',
                        'strength': rel.strength or 5,
                        'status': rel.status or '正常',
                        'description': rel.description or ''
                    }
                    self.add_character_relationship(
                        character_mapping[rel.source_character_id],
                        character_mapping[rel.target_character_id],
                        rel_attrs
                    )

            db.close()
            print(f"✅ 数据导入完成: {len(characters)}个人物, {len(events)}个事件, {len(milestones)}个里程碑, {len(relationships)}个关系")

        except Exception as e:
            print(f"❌ 数据导入失败: {e}")

    def export_to_json(self, filename: str = 'novel_graph_data.json'):
        """导出图数据到JSON文件"""
        try:
            # 准备导出数据
            export_data = {
                'metadata': {
                    'export_time': datetime.now().isoformat(),
                    'node_count': self.graph.number_of_nodes(),
                    'edge_count': self.graph.number_of_edges(),
                    'graph_type': 'MultiDiGraph'
                },
                'nodes': [],
                'edges': []
            }

            # 导出节点
            for node_id, attrs in self.graph.nodes(data=True):
                export_data['nodes'].append({
                    'id': node_id,
                    **attrs
                })

            # 导出边
            for source, target, key, attrs in self.graph.edges(data=True, keys=True):
                export_data['edges'].append({
                    'source': source,
                    'target': target,
                    'key': key,
                    **attrs
                })

            # 写入文件
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            print(f"✅ 数据导出完成: {filename}")

        except Exception as e:
            print(f"❌ 数据导出失败: {e}")

    def import_from_json(self, filename: str = 'novel_graph_data.json'):
        """从JSON文件导入图数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 清空现有图
            self.graph.clear()

            # 导入节点
            for node_data in data['nodes']:
                node_id = node_data.pop('id')
                self.graph.add_node(node_id, **node_data)

            # 导入边
            for edge_data in data['edges']:
                source = edge_data.pop('source')
                target = edge_data.pop('target')
                key = edge_data.pop('key')
                self.graph.add_edge(source, target, key=key, **edge_data)

            print(f"✅ 数据导入完成: {filename}")
            print(f"   节点数: {self.graph.number_of_nodes()}")
            print(f"   边数: {self.graph.number_of_edges()}")

        except Exception as e:
            print(f"❌ 数据导入失败: {e}")

    # ==================== 可视化方法 ====================

    def visualize_static(self, layout: str = 'spring', figsize: Tuple[int, int] = (15, 10),
                        save_path: str = None, show_labels: bool = True):
        """静态图可视化"""
        try:
            plt.figure(figsize=figsize)

            # 选择布局算法
            layout_functions = {
                'spring': nx.spring_layout,
                'circular': nx.circular_layout,
                'kamada_kawai': nx.kamada_kawai_layout,
                'shell': nx.shell_layout,
                'spectral': nx.spectral_layout
            }

            if layout not in layout_functions:
                layout = 'spring'
                print(f"⚠️  未知布局算法，使用默认的spring布局")

            pos = layout_functions[layout](self.graph, k=3, iterations=50)

            # 按节点类型分组绘制
            for node_type, type_name in self.NODE_TYPES.items():
                nodes = [node for node, attrs in self.graph.nodes(data=True)
                        if attrs.get('node_type') == node_type]

                if nodes:
                    node_colors = [self.graph.nodes[node].get('color', '#97C2FC') for node in nodes]
                    node_sizes = [self.graph.nodes[node].get('size', 30) * 20 for node in nodes]

                    nx.draw_networkx_nodes(
                        self.graph, pos, nodelist=nodes,
                        node_color=node_colors, node_size=node_sizes,
                        alpha=0.8, label=type_name
                    )

            # 按边类型分组绘制
            for edge_type, type_name in self.EDGE_TYPES.items():
                edges = [(u, v, k) for u, v, k, attrs in self.graph.edges(data=True, keys=True)
                        if attrs.get('edge_type') == edge_type]

                if edges:
                    edge_colors = [self.graph.edges[u, v, k].get('color', '#848484') for u, v, k in edges]
                    edge_widths = [self.graph.edges[u, v, k].get('weight', 1) * 0.5 for u, v, k in edges]

                    nx.draw_networkx_edges(
                        self.graph, pos, edgelist=[(u, v) for u, v, k in edges],
                        edge_color=edge_colors, width=edge_widths,
                        alpha=0.6, arrows=True, arrowsize=20
                    )

            # 添加标签
            if show_labels:
                labels = {node: attrs.get('name', node)[:8] + ('...' if len(attrs.get('name', node)) > 8 else '')
                         for node, attrs in self.graph.nodes(data=True)}
                nx.draw_networkx_labels(self.graph, pos, labels, font_size=8, font_weight='bold')

            plt.title("小说人物关系图", fontsize=16, fontweight='bold', pad=20)
            plt.legend(scatterpoints=1, loc='upper left', bbox_to_anchor=(1, 1))
            plt.axis('off')
            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 静态图保存至: {save_path}")

            plt.show()

        except Exception as e:
            print(f"❌ 静态可视化失败: {e}")

    def visualize_interactive(self, filename: str = 'novel_graph_interactive.html',
                            height: str = '750px', width: str = '100%'):
        """交互式图可视化"""
        try:
            # 创建pyvis网络
            net = Network(
                height=height,
                width=width,
                bgcolor="#222222",
                font_color="white",
                directed=True,
                select_menu=True,
                filter_menu=True
            )

            # 添加节点
            for node_id, attrs in self.graph.nodes(data=True):
                net.add_node(
                    node_id,
                    label=attrs.get('name', node_id),
                    color=attrs.get('color', '#97C2FC'),
                    size=attrs.get('size', 30),
                    title=self._create_node_tooltip(node_id, attrs),
                    group=attrs.get('node_type', 'unknown')
                )

            # 添加边
            for source, target, key, attrs in self.graph.edges(data=True, keys=True):
                net.add_edge(
                    source, target,
                    color=attrs.get('color', '#848484'),
                    width=attrs.get('weight', 1),
                    label=attrs.get('label', ''),
                    title=self._create_edge_tooltip(source, target, key, attrs)
                )

            # 设置物理引擎参数
            net.set_options("""
            var options = {
              "physics": {
                "enabled": true,
                "stabilization": {"iterations": 200},
                "barnesHut": {
                  "gravitationalConstant": -8000,
                  "centralGravity": 0.3,
                  "springLength": 150,
                  "springConstant": 0.04
                }
              },
              "edges": {
                "smooth": {
                  "type": "curvedCW",
                  "roundness": 0.2
                }
              },
              "interaction": {
                "dragNodes": true,
                "dragView": true,
                "zoomView": true
              }
            }
            """)

            # 生成HTML文件
            net.show(filename, notebook=False)
            print(f"✅ 交互式图生成完成: {filename}")

        except Exception as e:
            print(f"❌ 交互式可视化失败: {e}")

    def _create_node_tooltip(self, node_id: str, attrs: Dict[str, Any]) -> str:
        """创建节点提示信息"""
        tooltip = f"<b>{attrs.get('name', node_id)}</b><br>"
        tooltip += f"类型: {self.NODE_TYPES.get(attrs.get('node_type', ''), '未知')}<br>"
        tooltip += f"ID: {node_id}<br>"

        if attrs.get('node_type') == 'character':
            tooltip += f"原型: {attrs.get('prototype', '未知')}<br>"
            tooltip += f"角色: {attrs.get('narrative_role', '未知')}<br>"
            if attrs.get('internal_dimension', {}).get('personality'):
                tooltip += f"性格: {', '.join(attrs['internal_dimension']['personality'][:3])}<br>"

        elif attrs.get('node_type') == 'event':
            tooltip += f"地点: {attrs.get('location', '未知')}<br>"
            if attrs.get('description'):
                tooltip += f"描述: {attrs['description'][:50]}...<br>"

        elif attrs.get('node_type') == 'milestone':
            tooltip += f"描述: {attrs.get('description', '无')[:50]}...<br>"

        return tooltip

    def _create_edge_tooltip(self, source: str, target: str, key: str, attrs: Dict[str, Any]) -> str:
        """创建边提示信息"""
        source_name = self.graph.nodes[source].get('name', source)
        target_name = self.graph.nodes[target].get('name', target)

        tooltip = f"<b>{source_name} → {target_name}</b><br>"
        tooltip += f"类型: {self.EDGE_TYPES.get(attrs.get('edge_type', ''), '未知')}<br>"

        if attrs.get('edge_type') == 'character_relationship':
            tooltip += f"关系: {attrs.get('relationship_type', '未知')}<br>"
            tooltip += f"强度: {attrs.get('strength', 0)}/10<br>"
            tooltip += f"状态: {attrs.get('status', '未知')}<br>"

        elif attrs.get('edge_type') == 'character_event':
            tooltip += f"角色: {attrs.get('role', '参与者')}<br>"

        if attrs.get('description'):
            tooltip += f"描述: {attrs['description'][:50]}...<br>"

        return tooltip

    # ==================== 统计分析方法 ====================

    def get_graph_statistics(self) -> Dict[str, Any]:
        """获取图的统计信息"""
        stats = {
            'basic_info': {
                'total_nodes': self.graph.number_of_nodes(),
                'total_edges': self.graph.number_of_edges(),
                'is_directed': self.graph.is_directed(),
                'is_multigraph': self.graph.is_multigraph()
            },
            'node_types': {},
            'edge_types': {},
            'connectivity': {
                'is_connected': nx.is_weakly_connected(self.graph) if self.graph.is_directed() else nx.is_connected(self.graph),
                'number_of_components': nx.number_weakly_connected_components(self.graph) if self.graph.is_directed() else nx.number_connected_components(self.graph)
            }
        }

        # 统计节点类型
        for node, attrs in self.graph.nodes(data=True):
            node_type = attrs.get('node_type', 'unknown')
            stats['node_types'][node_type] = stats['node_types'].get(node_type, 0) + 1

        # 统计边类型
        for u, v, attrs in self.graph.edges(data=True):
            edge_type = attrs.get('edge_type', 'unknown')
            stats['edge_types'][edge_type] = stats['edge_types'].get(edge_type, 0) + 1

        # 计算度中心性（仅对人物节点）
        character_nodes = [node for node, attrs in self.graph.nodes(data=True)
                          if attrs.get('node_type') == 'character']

        if character_nodes:
            character_subgraph = self.graph.subgraph(character_nodes)
            degree_centrality = nx.degree_centrality(character_subgraph)
            stats['centrality'] = {
                'most_connected_character': max(degree_centrality, key=degree_centrality.get),
                'average_degree': sum(degree_centrality.values()) / len(degree_centrality)
            }

        return stats

    def print_statistics(self):
        """打印图的统计信息"""
        stats = self.get_graph_statistics()

        print("\n" + "="*50)
        print("📊 图数据统计信息")
        print("="*50)

        print(f"📈 基本信息:")
        print(f"   总节点数: {stats['basic_info']['total_nodes']}")
        print(f"   总边数: {stats['basic_info']['total_edges']}")
        print(f"   图类型: {'有向多重图' if stats['basic_info']['is_directed'] and stats['basic_info']['is_multigraph'] else '图'}")

        print(f"\n🏷️  节点类型分布:")
        for node_type, count in stats['node_types'].items():
            type_name = self.NODE_TYPES.get(node_type, node_type)
            print(f"   {type_name}: {count}")

        print(f"\n🔗 边类型分布:")
        for edge_type, count in stats['edge_types'].items():
            type_name = self.EDGE_TYPES.get(edge_type, edge_type)
            print(f"   {type_name}: {count}")

        print(f"\n🌐 连通性:")
        print(f"   是否连通: {'是' if stats['connectivity']['is_connected'] else '否'}")
        print(f"   连通分量数: {stats['connectivity']['number_of_components']}")

        if 'centrality' in stats:
            most_connected = stats['centrality']['most_connected_character']
            most_connected_name = self.graph.nodes[most_connected].get('name', most_connected)
            print(f"\n⭐ 中心性分析:")
            print(f"   最具连接性的人物: {most_connected_name}")
            print(f"   平均度中心性: {stats['centrality']['average_degree']:.3f}")

        print("="*50)


# ==================== 示例数据和测试函数 ====================

def create_sample_data(graph_system: NovelGraphSystem):
    """创建示例数据"""
    print("\n🔄 创建示例数据...")

    # 创建人物
    li_feng_id = graph_system.add_character_node({
        'name': '李风',
        'prototype': '英雄',
        'narrative_role': '主角',
        'core_identity': {
            'aliases': ['风之子'],
            'basic_info': {
                'gender': '男',
                'age': 22,
                'nationality': '大唐',
                'race': '人族'
            },
            'background': '出身贫寒，自幼父母双亡，被剑术大师收养'
        },
        'internal_dimension': {
            'personality': ['勇敢', '正义', '冲动'],
            'personality_description': '天性善良，但有时过于冲动',
            'core_values': ['正义', '自由'],
            'motivations': ['为养父报仇', '保护弱小'],
            'fears_secrets': ['害怕失去重要的人', '隐藏着自己的真实身世']
        },
        'external_dimension': {
            'appearance': '黑色短发，身材修长，常穿青色长衫',
            'behavior': '行动敏捷，剑不离身',
            'skills_abilities': ['高级剑术', '轻功', '内力修炼']
        },
        'tags': ['核心角色', '成长型主角']
    })

    bai_master_id = graph_system.add_character_node({
        'name': '白大师',
        'prototype': '导师',
        'narrative_role': '配角',
        'core_identity': {
            'basic_info': {
                'gender': '男',
                'age': 65,
                'nationality': '大唐',
                'race': '人族'
            },
            'background': '前朝剑术宗师，隐居于山林'
        },
        'internal_dimension': {
            'personality': ['智慧', '耐心', '严肃'],
            'core_values': ['知识', '平衡'],
            'motivations': ['传授剑术', '保护传统']
        },
        'external_dimension': {
            'appearance': '白发长须，目光如炬，常穿白色道袍',
            'skills_abilities': ['宗师级剑术', '内功大师', '兵法策略']
        },
        'tags': ['导师角色', '重要配角']
    })

    xiao_yu_id = graph_system.add_character_node({
        'name': '小雨',
        'prototype': '伙伴',
        'narrative_role': '配角',
        'core_identity': {
            'basic_info': {
                'gender': '女',
                'age': 19,
                'nationality': '大唐',
                'race': '人族'
            },
            'background': '江湖医师之女，精通医术'
        },
        'internal_dimension': {
            'personality': ['善良', '聪明', '坚强'],
            'core_values': ['救死扶伤', '友情'],
            'motivations': ['帮助他人', '寻找失踪的父亲']
        },
        'external_dimension': {
            'appearance': '清秀可人，常背药箱',
            'skills_abilities': ['医术', '毒术', '轻功']
        },
        'tags': ['重要配角', '医师']
    })

    hei_ying_id = graph_system.add_character_node({
        'name': '黑影',
        'prototype': '反派',
        'narrative_role': '反派',
        'core_identity': {
            'basic_info': {
                'gender': '男',
                'age': 40,
                'nationality': '未知',
                'race': '人族'
            },
            'background': '神秘刺客组织首领'
        },
        'internal_dimension': {
            'personality': ['冷酷', '狡猾', '野心勃勃'],
            'core_values': ['权力', '复仇'],
            'motivations': ['统治江湖', '消灭白大师一脉']
        },
        'external_dimension': {
            'appearance': '身穿黑衣，面戴面具',
            'skills_abilities': ['暗杀术', '毒功', '轻功']
        },
        'tags': ['主要反派', '刺客']
    })

    # 创建事件
    attack_event_id = graph_system.add_event_node({
        'name': '山林遇袭',
        'description': '李风在山林中遭遇神秘刺客袭击',
        'timestamp': '2023-05-15T14:30:00',
        'location': '青云山',
        'tags': ['战斗', '转折点', '第一幕']
    })

    rescue_event_id = graph_system.add_event_node({
        'name': '小雨救治',
        'description': '小雨发现受伤的李风并进行救治',
        'timestamp': '2023-05-15T16:00:00',
        'location': '山间小屋',
        'tags': ['救援', '相遇', '第一幕']
    })

    training_event_id = graph_system.add_event_node({
        'name': '特训开始',
        'description': '白大师决定对李风进行特殊训练',
        'timestamp': '2023-05-20T08:00:00',
        'location': '隐秘道场',
        'tags': ['训练', '成长', '第二幕']
    })

    # 创建里程碑
    awakening_milestone_id = graph_system.add_milestone_node({
        'name': '觉醒里程碑',
        'character_id': li_feng_id,
        'event_id': attack_event_id,
        'description': '首次遭遇生死危机，意识到自身不足',
        'psychological_change': {
            'before': '自信过度，有些轻敌',
            'after': '更加谨慎，意识到需要更刻苦训练'
        },
        'values_change': {
            'before': ['正义', '自由'],
            'after': ['正义', '自由', '责任']
        },
        'goals_change': {
            'before': ['为养父报仇'],
            'after': ['为养父报仇', '提升剑术实力', '查明刺客身份']
        },
        'proof': '在后续剧情中，李风每天增加练剑时间，并向白大师请教更多实战技巧'
    })

    # 创建人物关系
    graph_system.add_character_relationship(li_feng_id, bai_master_id, {
        'relationship_type': '师徒',
        'strength': 9,
        'status': '稳固',
        'description': '白大师是李风的剑术导师，对他寄予厚望'
    })

    graph_system.add_character_relationship(li_feng_id, xiao_yu_id, {
        'relationship_type': '朋友',
        'strength': 7,
        'status': '发展中',
        'description': '小雨救治了李风，两人成为朋友'
    })

    graph_system.add_character_relationship(li_feng_id, hei_ying_id, {
        'relationship_type': '敌人',
        'strength': 8,
        'status': '敌对',
        'description': '黑影派刺客袭击李风，两人结下仇怨'
    })

    graph_system.add_character_relationship(bai_master_id, hei_ying_id, {
        'relationship_type': '敌人',
        'strength': 10,
        'status': '世仇',
        'description': '黑影是白大师的宿敌，两人有深仇大恨'
    })

    # 创建人物事件关系
    graph_system.add_character_event_relation(li_feng_id, attack_event_id, {
        'role': '受害者',
        'impact_description': '身受重伤，但侥幸逃生'
    })

    graph_system.add_character_event_relation(hei_ying_id, attack_event_id, {
        'role': '策划者',
        'impact_description': '派遣刺客袭击李风'
    })

    graph_system.add_character_event_relation(li_feng_id, rescue_event_id, {
        'role': '被救者',
        'impact_description': '得到及时救治，恢复健康'
    })

    graph_system.add_character_event_relation(xiao_yu_id, rescue_event_id, {
        'role': '救治者',
        'impact_description': '发挥医术，救治李风'
    })

    graph_system.add_character_event_relation(li_feng_id, training_event_id, {
        'role': '学员',
        'impact_description': '接受特殊训练，实力大幅提升'
    })

    graph_system.add_character_event_relation(bai_master_id, training_event_id, {
        'role': '导师',
        'impact_description': '传授高级剑术和实战经验'
    })

    # 创建里程碑关系
    graph_system.add_milestone_relation(li_feng_id, attack_event_id, awakening_milestone_id)

    print("✅ 示例数据创建完成！")


def demonstrate_system():
    """演示系统功能"""
    print("🚀 启动小说人物档案管理图系统演示")
    print("="*60)

    # 创建系统实例
    graph_system = NovelGraphSystem()

    # 创建示例数据
    create_sample_data(graph_system)

    # 显示统计信息
    graph_system.print_statistics()

    # 演示查询功能
    print("\n🔍 查询功能演示:")
    print("-" * 30)

    # 查询主角信息
    characters = graph_system.get_characters_by_type(narrative_role='主角')
    if characters:
        protagonist = characters[0]
        print(f"📖 主角信息: {protagonist['name']}")
        print(f"   原型: {protagonist['prototype']}")
        print(f"   性格: {', '.join(protagonist['internal_dimension'].get('personality', []))}")

        # 查询主角的关系
        relationships = graph_system.get_relationships(protagonist['id'])
        print(f"\n🤝 {protagonist['name']}的人物关系:")
        for rel in relationships:
            if rel['direction'] == 'outgoing':
                print(f"   → {rel['target_name']}: {rel['relationship_type']} (强度: {rel['strength']}/10)")
            else:
                print(f"   ← {rel['source_name']}: {rel['relationship_type']} (强度: {rel['strength']}/10)")

        # 查询主角参与的事件
        events = graph_system.get_character_events(protagonist['id'])
        print(f"\n📅 {protagonist['name']}参与的事件:")
        for event in events:
            print(f"   • {event['event_name']}: {event['role']}")

    # 演示路径查找
    print(f"\n🛤️  路径查找演示:")
    characters = graph_system.get_characters_by_type()
    if len(characters) >= 2:
        char1, char2 = characters[0], characters[1]
        path = graph_system.find_path_between_characters(char1['id'], char2['id'])
        if path:
            path_names = [graph_system.graph.nodes[node_id]['name'] for node_id in path]
            print(f"   {char1['name']} → {char2['name']}: {' → '.join(path_names)}")
        else:
            print(f"   {char1['name']} 和 {char2['name']} 之间没有直接路径")

    # 数据导出演示
    print(f"\n💾 数据导出演示:")
    graph_system.export_to_json('novel_graph_sample.json')

    # 可视化演示
    print(f"\n🎨 可视化演示:")
    print("   正在生成静态图...")
    graph_system.visualize_static(layout='spring', save_path='novel_graph_static.png', show_labels=True)

    print("   正在生成交互式图...")
    graph_system.visualize_interactive('novel_graph_interactive.html')

    print("\n🎉 演示完成！")
    print("📁 生成的文件:")
    print("   • novel_graph_sample.json - 图数据JSON文件")
    print("   • novel_graph_static.png - 静态图片")
    print("   • novel_graph_interactive.html - 交互式图页面")
    print("\n💡 提示: 用浏览器打开 novel_graph_interactive.html 查看交互式图")


if __name__ == "__main__":
    demonstrate_system()
