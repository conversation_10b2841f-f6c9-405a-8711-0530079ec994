# 小说人物档案管理系统 v2.0 使用说明

## 📖 概述

小说人物档案管理系统 v2.0 是基于多图架构的增强版本，解决了 v1 单一图结构的局限性，提供了更强大的多维度数据管理和分析功能。

## 🆚 v1 vs v2 主要改进

| 功能特性 | v1系统 | v2系统 |
|---------|--------|--------|
| 数据结构 | 单一MultiDiGraph | 多图架构 + 混合数据结构 |
| 人物关系 | ✅ 支持 | ✅ 增强支持 + 专门管理器 |
| 事件管理 | ✅ 基础支持 | ✅ 增强支持 + 因果关系 |
| 时间线 | ❌ 分散在节点中 | ✅ 专门的时间线管理器 |
| 里程碑 | ✅ 基础支持 | ✅ 增强支持 + 依赖关系 |
| 参与关系 | ✅ 简单边连接 | ✅ 专门的参与管理器 |
| 查询功能 | ✅ 基础查询 | ✅ 多维度统一查询接口 |
| 数据分析 | ✅ 基础统计 | ✅ 高级分析 + 模式识别 |
| 可视化 | ✅ 静态 + 交互式 | ✅ 多种专门视图 |

## 🏗️ 系统架构

### 核心组件

1. **CharacterRelationshipManager** - 人物关系管理器
   - 管理人物基本信息和人物间关系
   - 使用无向图表示双向关系
   - 支持关系类型、强度、状态等属性

2. **EventManager** - 事件管理器
   - 管理事件信息和事件间因果关系
   - 使用有向图表示因果链
   - 支持事件重要性、时间戳等属性

3. **TimelineManager** - 时间线管理器
   - 基于pandas DataFrame的时间序列管理
   - 支持时间范围查询和模式分析
   - 统一管理事件和里程碑的时间信息

4. **MilestoneManager** - 里程碑管理器
   - 管理人物成长里程碑
   - 支持里程碑间的依赖关系
   - 追踪人物心理、价值观、目标的变化

5. **ParticipationManager** - 参与管理器
   - 管理人物与事件的参与关系
   - 使用二分图连接人物和事件
   - 支持参与角色、影响程度等属性

6. **NovelMultiGraphSystem** - 主系统类
   - 统一的操作接口
   - 跨组件的数据协调
   - 高级分析和可视化功能

## 🚀 快速开始

### 基本使用

```python
from code.Graph_v2.图v2 import NovelMultiGraphSystem

# 创建系统实例
system = NovelMultiGraphSystem()

# 添加人物
li_feng_id = system.add_character({
   'name': '李风',
   'prototype': '英雄',
   'narrative_role': '主角',
   'core_identity': {
      'basic_info': {'gender': '男', 'age': 22},
      'background': '出身贫寒，自幼父母双亡'
   },
   'internal_dimension': {
      'personality': ['勇敢', '正义', '冲动'],
      'core_values': ['正义', '自由'],
      'motivations': ['为养父报仇', '保护弱小']
   },
   'tags': ['主角', '剑客']
})

# 添加事件
event_id = system.add_event({
   'name': '山林遇袭',
   'description': '李风在修炼时遭遇刺客袭击',
   'timestamp': '2023-05-15T14:30:00',
   'location': '青云山',
   'importance': 8
})

# 添加人物关系
system.add_character_relationship(char1_id, char2_id, {
   'relationship_type': '师徒',
   'strength': 9,
   'description': '师父与弟子的深厚关系'
})

# 添加参与关系
system.add_character_event_participation(li_feng_id, event_id, {
   'role': '受害者',
   'impact_description': '身受重伤但侥幸逃生',
   'emotional_impact': 8
})

# 添加里程碑
system.add_milestone({
   'character_id': li_feng_id,
   'name': '危机觉醒',
   'description': '首次遭遇生死危机，意识到实力不足',
   'milestone_type': 'setback',
   'psychological_change': {
      'before': '自信过度，有些轻敌',
      'after': '更加谨慎，意识到需要刻苦训练'
   },
   'importance': 8
})
```

### 查询功能

```python
# 获取人物完整信息
complete_info = system.get_character_complete_info(li_feng_id)
print(f"人物: {complete_info['basic_info']['name']}")
print(f"关系数量: {len(complete_info['relationships'])}")
print(f"参与事件: {len(complete_info['participations'])}")
print(f"里程碑数量: {len(complete_info['milestones'])}")

# 时间范围查询
timeline_data = system.query_by_timerange('2023-05-01', '2023-06-30')
print(f"时间范围内事件: {len(timeline_data['events'])}")

# 搜索人物
protagonists = system.search_characters(narrative_role='主角')
heroes = system.search_characters(prototype='英雄')
tagged_chars = system.search_characters(tags=['剑客'])
```

### 数据分析

```python
# 系统统计
system.print_system_statistics()

# 人物影响力分析
centrality = system.character_manager.analyze_centrality()
most_influential = max(centrality, key=centrality.get)

# 人物成长分析
growth_analysis = system.milestone_manager.analyze_character_growth(li_feng_id)
print(f"总里程碑: {growth_analysis['total_milestones']}")
print(f"平均重要性: {growth_analysis['average_importance']}")

# 参与模式分析
participation_pattern = system.participation_manager.analyze_character_participation_pattern(li_feng_id)
print(f"最常见角色: {participation_pattern['most_common_role']}")
```

### 可视化功能

```python
# 人物关系网络图
system.visualize_character_relationships(
    layout='spring',
    save_path='character_relationships.png'
)

# 时间线图
system.visualize_timeline(
    character_id=li_feng_id,
    save_path='character_timeline.html'
)

# 人物成长轨迹
system.visualize_character_growth(
    character_id=li_feng_id,
    save_path='character_growth.html'
)

# 事件参与网络
system.visualize_event_participation_network(
    save_path='participation_network.html'
)
```

### 数据导入导出

```python
# 导出数据
system.export_to_json('my_novel_data.json')

# 导入数据
system.import_from_json('my_novel_data.json')

# 从v1系统迁移
from code.图v1 import NovelGraphSystem as V1System

v1_system = V1System()
# ... 在v1系统中添加数据 ...
system.import_from_v1_system(v1_system)
```

## 📊 高级功能

### 复杂故事事件创建

```python
# 创建包含多个参与者和里程碑的完整事件
result = system.create_complete_story_event(
    event_data={
        'name': '决战黑影',
        'description': '李风与黑影的最终对决',
        'timestamp': '2023-07-01T20:00:00',
        'importance': 10
    },
    participants=[
        {
            'character_id': li_feng_id,
            'role': '主导者',
            'emotional_impact': 9
        },
        {
            'character_id': hei_ying_id,
            'role': '主导者',
            'emotional_impact': 8
        }
    ],
    related_milestones=[
        {
            'character_id': li_feng_id,
            'name': '复仇完成',
            'milestone_type': 'achievement',
            'importance': 10
        }
    ]
)
```

### 多维度数据分析

```python
# 获取完整系统统计
stats = system.get_system_statistics()

# 时间线模式分析
timeline_patterns = system.timeline_manager.analyze_timeline_patterns()
print(f"月度事件分布: {timeline_patterns['monthly_distribution']}")

# 人物群体分析
clusters = system.character_manager.get_character_clusters()
print(f"发现 {len(clusters)} 个人物群体")
```

## 🔧 依赖要求

### 必需依赖
- Python 3.7+
- NetworkX (图数据结构)
- pandas (时间序列管理)

### 可选依赖（用于可视化）
- matplotlib (静态图)
- plotly (交互式图表)
- pyvis (网络图可视化)

### 安装命令

```bash
# 基础依赖
pip install networkx pandas

# 可视化依赖
pip install matplotlib plotly pyvis

# 或一次性安装所有依赖
pip install networkx pandas matplotlib plotly pyvis
```

## 🐛 故障排除

### 常见问题

1. **依赖缺失**
   - 问题：ImportError: No module named 'xxx'
   - 解决：使用pip安装缺失的依赖包

2. **可视化不显示**
   - 问题：图表生成但不显示
   - 解决：检查是否在Jupyter环境中，或使用save_path参数保存文件

3. **数据导入失败**
   - 问题：JSON文件格式错误
   - 解决：检查JSON文件格式，确保使用UTF-8编码

### 性能优化建议

1. **大数据集处理**
   - 使用批量操作而非逐个添加
   - 定期清理不需要的数据
   - 考虑使用数据库存储大量数据

2. **可视化优化**
   - 大图可视化时限制显示的节点数量
   - 使用交互式图表而非静态图片
   - 分层显示复杂关系网络

## 📝 最佳实践

1. **数据组织**
   - 使用有意义的ID和名称
   - 保持数据结构的一致性
   - 定期备份重要数据

2. **关系建模**
   - 明确定义关系类型和强度标准
   - 使用标签对数据进行分类
   - 记录关系变化的时间信息

3. **里程碑设计**
   - 选择真正重要的成长节点
   - 详细记录心理和价值观变化
   - 建立里程碑间的逻辑依赖

4. **可视化设计**
   - 根据分析目的选择合适的视图
   - 使用颜色和大小编码重要信息
   - 提供交互功能增强用户体验

## 🔮 未来发展

v2.0系统为未来扩展奠定了良好基础：

- **AI集成**：支持自然语言处理和智能分析
- **协作功能**：多用户协同编辑和版本控制
- **云端同步**：数据云端存储和跨设备同步
- **插件系统**：支持第三方扩展和自定义功能
- **移动端支持**：开发移动应用和响应式界面

## 📞 支持与反馈

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件反馈
- 参与社区讨论

---

*小说人物档案管理系统 v2.0 - 让创作更有条理，让故事更精彩！*
