# -*- coding: utf-8 -*-
"""
小说人物档案管理系统 v2.0 - 多图架构增强版
作者: AugCode
功能: 使用多图结构和混合数据格式，支持人物关系、事件时间线、成长里程碑等多维度管理
改进: 解决v1单一图结构的局限性，提供更强大的查询和分析功能
"""

import networkx as nx
import pandas as pd
import matplotlib.pyplot as plt
from pyvis.network import Network
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class BaseManager:
    """基础管理器类，提供通用功能"""
    
    def __init__(self):
        self.created_time = datetime.now()
        self.updated_time = datetime.now()
    
    def _generate_id(self, prefix: str = "") -> str:
        """生成唯一ID"""
        return f"{prefix}_{uuid.uuid4().hex[:8]}" if prefix else uuid.uuid4().hex[:8]
    
    def _update_timestamp(self):
        """更新时间戳"""
        self.updated_time = datetime.now()


class CharacterRelationshipManager(BaseManager):
    """人物关系管理器 - 专门管理人物之间的关系"""
    
    def __init__(self):
        super().__init__()
        # 使用无向图，因为人物关系通常是双向的
        self.graph = nx.Graph()
        self.characters = {}  # 人物数据存储
        
        # 关系类型配置
        self.RELATIONSHIP_TYPES = {
            '师徒': {'color': '#FF6B6B', 'strength_range': (7, 10)},
            '朋友': {'color': '#4ECDC4', 'strength_range': (5, 9)},
            '敌人': {'color': '#FF4757', 'strength_range': (3, 8)},
            '恋人': {'color': '#FF3838', 'strength_range': (8, 10)},
            '亲人': {'color': '#FFA502', 'strength_range': (6, 10)},
            '同事': {'color': '#70A1FF', 'strength_range': (3, 7)},
            '陌生人': {'color': '#A4B0BE', 'strength_range': (1, 3)}
        }
    
    def add_character(self, character_data: Dict[str, Any]) -> str:
        """添加人物"""
        char_id = character_data.get('id', self._generate_id('char'))
        
        # 标准化人物数据
        standardized_data = {
            'id': char_id,
            'name': character_data.get('name', '未命名'),
            'prototype': character_data.get('prototype', ''),
            'narrative_role': character_data.get('narrative_role', ''),
            'core_identity': character_data.get('core_identity', {}),
            'internal_dimension': character_data.get('internal_dimension', {}),
            'external_dimension': character_data.get('external_dimension', {}),
            'tags': character_data.get('tags', []),
            'created_time': datetime.now().isoformat(),
            'updated_time': datetime.now().isoformat()
        }
        
        self.characters[char_id] = standardized_data
        self.graph.add_node(char_id, **standardized_data)
        self._update_timestamp()
        
        print(f"✅ 添加人物: {standardized_data['name']} (ID: {char_id})")
        return char_id
    
    def add_relationship(self, char1_id: str, char2_id: str, relationship_data: Dict[str, Any]) -> str:
        """添加人物关系"""
        if not (self.graph.has_node(char1_id) and self.graph.has_node(char2_id)):
            print(f"❌ 人物不存在: {char1_id} 或 {char2_id}")
            return ""
        
        rel_id = relationship_data.get('id', self._generate_id('rel'))
        rel_type = relationship_data.get('relationship_type', '朋友')
        rel_config = self.RELATIONSHIP_TYPES.get(rel_type, self.RELATIONSHIP_TYPES['朋友'])
        
        # 标准化关系数据
        standardized_rel = {
            'id': rel_id,
            'relationship_type': rel_type,
            'strength': relationship_data.get('strength', 5),
            'status': relationship_data.get('status', '正常'),
            'description': relationship_data.get('description', ''),
            'color': rel_config['color'],
            'created_time': datetime.now().isoformat(),
            'updated_time': datetime.now().isoformat()
        }
        
        self.graph.add_edge(char1_id, char2_id, **standardized_rel)
        self._update_timestamp()
        
        char1_name = self.characters[char1_id]['name']
        char2_name = self.characters[char2_id]['name']
        print(f"✅ 添加关系: {char1_name} ↔ {char2_name} ({rel_type})")
        return rel_id
    
    def get_character(self, char_id: str) -> Optional[Dict[str, Any]]:
        """获取人物信息"""
        return self.characters.get(char_id)
    
    def get_relationships(self, char_id: str) -> List[Dict[str, Any]]:
        """获取人物的所有关系"""
        if not self.graph.has_node(char_id):
            return []
        
        relationships = []
        for neighbor in self.graph.neighbors(char_id):
            edge_data = self.graph[char_id][neighbor]
            relationships.append({
                'target_id': neighbor,
                'target_name': self.characters[neighbor]['name'],
                **edge_data
            })
        
        return relationships
    
    def find_shortest_path(self, char1_id: str, char2_id: str) -> Optional[List[str]]:
        """查找两个人物之间的最短路径"""
        try:
            return nx.shortest_path(self.graph, char1_id, char2_id)
        except nx.NetworkXNoPath:
            return None
    
    def analyze_centrality(self) -> Dict[str, float]:
        """分析人物的中心性（影响力）"""
        centrality = nx.degree_centrality(self.graph)
        return {char_id: centrality.get(char_id, 0) for char_id in self.characters.keys()}
    
    def get_character_clusters(self) -> List[List[str]]:
        """获取人物群体（连通分量）"""
        return list(nx.connected_components(self.graph))


class EventManager(BaseManager):
    """事件管理器 - 管理事件及其因果关系"""
    
    def __init__(self):
        super().__init__()
        # 使用有向图表示事件的因果关系
        self.graph = nx.DiGraph()
        self.events = {}  # 事件数据存储
    
    def add_event(self, event_data: Dict[str, Any]) -> str:
        """添加事件"""
        event_id = event_data.get('id', self._generate_id('event'))
        
        # 标准化事件数据
        standardized_data = {
            'id': event_id,
            'name': event_data.get('name', '未命名事件'),
            'description': event_data.get('description', ''),
            'timestamp': event_data.get('timestamp', datetime.now().isoformat()),
            'location': event_data.get('location', ''),
            'tags': event_data.get('tags', []),
            'importance': event_data.get('importance', 5),  # 1-10重要性评级
            'created_time': datetime.now().isoformat(),
            'updated_time': datetime.now().isoformat()
        }
        
        self.events[event_id] = standardized_data
        self.graph.add_node(event_id, **standardized_data)
        self._update_timestamp()
        
        print(f"✅ 添加事件: {standardized_data['name']} (ID: {event_id})")
        return event_id
    
    def add_causal_relationship(self, cause_event_id: str, effect_event_id: str, 
                              relationship_data: Dict[str, Any] = None) -> str:
        """添加事件因果关系"""
        if not (self.graph.has_node(cause_event_id) and self.graph.has_node(effect_event_id)):
            print(f"❌ 事件不存在: {cause_event_id} 或 {effect_event_id}")
            return ""
        
        rel_data = relationship_data or {}
        rel_id = rel_data.get('id', self._generate_id('causal'))
        
        # 标准化因果关系数据
        standardized_rel = {
            'id': rel_id,
            'relationship_type': 'causal',
            'strength': rel_data.get('strength', 5),  # 因果关系强度
            'description': rel_data.get('description', ''),
            'created_time': datetime.now().isoformat()
        }
        
        self.graph.add_edge(cause_event_id, effect_event_id, **standardized_rel)
        self._update_timestamp()
        
        cause_name = self.events[cause_event_id]['name']
        effect_name = self.events[effect_event_id]['name']
        print(f"✅ 添加因果关系: {cause_name} → {effect_name}")
        return rel_id
    
    def get_event(self, event_id: str) -> Optional[Dict[str, Any]]:
        """获取事件信息"""
        return self.events.get(event_id)
    
    def get_causal_chain(self, event_id: str, direction: str = 'both') -> Dict[str, List[str]]:
        """获取事件的因果链"""
        result = {'causes': [], 'effects': []}
        
        if not self.graph.has_node(event_id):
            return result
        
        if direction in ['both', 'backward']:
            # 获取原因链
            result['causes'] = list(self.graph.predecessors(event_id))
        
        if direction in ['both', 'forward']:
            # 获取结果链
            result['effects'] = list(self.graph.successors(event_id))
        
        return result
    
    def get_events_by_timerange(self, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        """根据时间范围获取事件"""
        start_dt = datetime.fromisoformat(start_time)
        end_dt = datetime.fromisoformat(end_time)
        
        filtered_events = []
        for event_id, event_data in self.events.items():
            event_dt = datetime.fromisoformat(event_data['timestamp'])
            if start_dt <= event_dt <= end_dt:
                filtered_events.append(event_data)
        
        return sorted(filtered_events, key=lambda x: x['timestamp'])


class TimelineManager(BaseManager):
    """时间线管理器 - 管理时间序列数据"""
    
    def __init__(self):
        super().__init__()
        # 使用pandas DataFrame管理时间线数据
        self.timeline_df = pd.DataFrame(columns=[
            'id', 'type', 'name', 'timestamp', 'description', 
            'related_characters', 'related_events', 'importance'
        ])
        self.timeline_df['timestamp'] = pd.to_datetime(self.timeline_df['timestamp'])
    
    def add_timeline_entry(self, entry_data: Dict[str, Any]) -> str:
        """添加时间线条目"""
        entry_id = entry_data.get('id', self._generate_id('timeline'))
        
        # 标准化时间线条目数据
        standardized_entry = {
            'id': entry_id,
            'type': entry_data.get('type', 'event'),  # event, milestone, other
            'name': entry_data.get('name', '未命名条目'),
            'timestamp': pd.to_datetime(entry_data.get('timestamp', datetime.now())),
            'description': entry_data.get('description', ''),
            'related_characters': entry_data.get('related_characters', []),
            'related_events': entry_data.get('related_events', []),
            'importance': entry_data.get('importance', 5)
        }
        
        # 添加到DataFrame
        new_row = pd.DataFrame([standardized_entry])
        self.timeline_df = pd.concat([self.timeline_df, new_row], ignore_index=True)
        self.timeline_df = self.timeline_df.sort_values('timestamp').reset_index(drop=True)
        self._update_timestamp()
        
        print(f"✅ 添加时间线条目: {standardized_entry['name']} ({standardized_entry['timestamp']})")
        return entry_id
    
    def get_timeline_range(self, start_time: str, end_time: str) -> pd.DataFrame:
        """获取指定时间范围的时间线"""
        start_dt = pd.to_datetime(start_time)
        end_dt = pd.to_datetime(end_time)
        
        mask = (self.timeline_df['timestamp'] >= start_dt) & (self.timeline_df['timestamp'] <= end_dt)
        return self.timeline_df.loc[mask].copy()
    
    def get_character_timeline(self, character_id: str) -> pd.DataFrame:
        """获取特定人物的时间线"""
        mask = self.timeline_df['related_characters'].apply(lambda x: character_id in x)
        return self.timeline_df.loc[mask].copy()
    
    def analyze_timeline_patterns(self) -> Dict[str, Any]:
        """分析时间线模式"""
        if self.timeline_df.empty:
            return {}
        
        # 按月份统计事件数量
        monthly_counts = self.timeline_df.groupby(
            self.timeline_df['timestamp'].dt.to_period('M')
        ).size()
        
        # 按类型统计
        type_counts = self.timeline_df['type'].value_counts()
        
        # 重要性分布
        importance_stats = self.timeline_df['importance'].describe()
        
        return {
            'total_entries': len(self.timeline_df),
            'date_range': {
                'start': self.timeline_df['timestamp'].min(),
                'end': self.timeline_df['timestamp'].max()
            },
            'monthly_distribution': monthly_counts.to_dict(),
            'type_distribution': type_counts.to_dict(),
            'importance_stats': importance_stats.to_dict()
        }


class MilestoneManager(BaseManager):
    """里程碑管理器 - 管理人物成长里程碑"""
    
    def __init__(self):
        super().__init__()
        # 使用有向图表示里程碑的层次和依赖关系
        self.graph = nx.DiGraph()
        self.milestones = {}  # 里程碑数据存储
        self.character_milestones = defaultdict(list)  # 按人物索引里程碑
    
    def add_milestone(self, milestone_data: Dict[str, Any]) -> str:
        """添加里程碑"""
        milestone_id = milestone_data.get('id', self._generate_id('milestone'))
        character_id = milestone_data.get('character_id', '')
        
        # 标准化里程碑数据
        standardized_data = {
            'id': milestone_id,
            'character_id': character_id,
            'name': milestone_data.get('name', '未命名里程碑'),
            'description': milestone_data.get('description', ''),
            'timestamp': milestone_data.get('timestamp', datetime.now().isoformat()),
            'milestone_type': milestone_data.get('milestone_type', 'growth'),  # growth, achievement, setback
            'psychological_change': milestone_data.get('psychological_change', {}),
            'values_change': milestone_data.get('values_change', {}),
            'goals_change': milestone_data.get('goals_change', {}),
            'proof': milestone_data.get('proof', ''),
            'importance': milestone_data.get('importance', 5),
            'created_time': datetime.now().isoformat(),
            'updated_time': datetime.now().isoformat()
        }
        
        self.milestones[milestone_id] = standardized_data
        self.graph.add_node(milestone_id, **standardized_data)
        
        if character_id:
            self.character_milestones[character_id].append(milestone_id)
        
        self._update_timestamp()
        
        print(f"✅ 添加里程碑: {standardized_data['name']} (人物: {character_id})")
        return milestone_id
    
    def add_milestone_dependency(self, prerequisite_id: str, dependent_id: str, 
                                dependency_data: Dict[str, Any] = None) -> str:
        """添加里程碑依赖关系"""
        if not (self.graph.has_node(prerequisite_id) and self.graph.has_node(dependent_id)):
            print(f"❌ 里程碑不存在: {prerequisite_id} 或 {dependent_id}")
            return ""
        
        dep_data = dependency_data or {}
        dep_id = dep_data.get('id', self._generate_id('dep'))
        
        # 标准化依赖关系数据
        standardized_dep = {
            'id': dep_id,
            'dependency_type': dep_data.get('dependency_type', 'prerequisite'),
            'strength': dep_data.get('strength', 5),
            'description': dep_data.get('description', ''),
            'created_time': datetime.now().isoformat()
        }
        
        self.graph.add_edge(prerequisite_id, dependent_id, **standardized_dep)
        self._update_timestamp()
        
        pre_name = self.milestones[prerequisite_id]['name']
        dep_name = self.milestones[dependent_id]['name']
        print(f"✅ 添加里程碑依赖: {pre_name} → {dep_name}")
        return dep_id
    
    def get_character_milestones(self, character_id: str, sorted_by_time: bool = True) -> List[Dict[str, Any]]:
        """获取人物的所有里程碑"""
        milestone_ids = self.character_milestones.get(character_id, [])
        milestones = [self.milestones[mid] for mid in milestone_ids if mid in self.milestones]
        
        if sorted_by_time:
            milestones.sort(key=lambda x: x['timestamp'])
        
        return milestones
    
    def get_milestone_path(self, character_id: str) -> List[str]:
        """获取人物的里程碑发展路径"""
        milestone_ids = self.character_milestones.get(character_id, [])
        
        if not milestone_ids:
            return []
        
        # 创建子图只包含该人物的里程碑
        subgraph = self.graph.subgraph(milestone_ids)
        
        try:
            # 尝试找到拓扑排序（发展顺序）
            return list(nx.topological_sort(subgraph))
        except nx.NetworkXError:
            # 如果有环，按时间排序
            milestones = self.get_character_milestones(character_id, sorted_by_time=True)
            return [m['id'] for m in milestones]
    
    def analyze_character_growth(self, character_id: str) -> Dict[str, Any]:
        """分析人物成长轨迹"""
        milestones = self.get_character_milestones(character_id, sorted_by_time=True)
        
        if not milestones:
            return {}
        
        # 分析成长趋势
        growth_milestones = [m for m in milestones if m['milestone_type'] == 'growth']
        achievement_milestones = [m for m in milestones if m['milestone_type'] == 'achievement']
        setback_milestones = [m for m in milestones if m['milestone_type'] == 'setback']
        
        # 计算平均重要性
        avg_importance = sum(m['importance'] for m in milestones) / len(milestones)
        
        return {
            'total_milestones': len(milestones),
            'growth_count': len(growth_milestones),
            'achievement_count': len(achievement_milestones),
            'setback_count': len(setback_milestones),
            'average_importance': avg_importance,
            'first_milestone': milestones[0] if milestones else None,
            'latest_milestone': milestones[-1] if milestones else None,
            'growth_trajectory': [m['importance'] for m in milestones]
        }


class ParticipationManager(BaseManager):
    """参与管理器 - 管理人物与事件的参与关系"""
    
    def __init__(self):
        super().__init__()
        # 使用二分图连接人物和事件
        self.graph = nx.Graph()
        self.participations = {}  # 参与关系数据存储
        
        # 参与角色类型
        self.PARTICIPATION_ROLES = {
            '主导者': {'color': '#FF6B6B', 'weight': 10},
            '参与者': {'color': '#4ECDC4', 'weight': 7},
            '受影响者': {'color': '#FFA502', 'weight': 5},
            '旁观者': {'color': '#70A1FF', 'weight': 3},
            '受害者': {'color': '#FF4757', 'weight': 6},
            '救助者': {'color': '#7BE19C', 'weight': 8}
        }
    
    def add_participation(self, character_id: str, event_id: str, participation_data: Dict[str, Any]) -> str:
        """添加参与关系"""
        participation_id = participation_data.get('id', self._generate_id('participation'))
        role = participation_data.get('role', '参与者')
        role_config = self.PARTICIPATION_ROLES.get(role, self.PARTICIPATION_ROLES['参与者'])
        
        # 标准化参与数据
        standardized_data = {
            'id': participation_id,
            'character_id': character_id,
            'event_id': event_id,
            'role': role,
            'impact_description': participation_data.get('impact_description', ''),
            'emotional_impact': participation_data.get('emotional_impact', 5),  # 1-10情感影响程度
            'behavioral_change': participation_data.get('behavioral_change', ''),
            'color': role_config['color'],
            'weight': role_config['weight'],
            'created_time': datetime.now().isoformat(),
            'updated_time': datetime.now().isoformat()
        }
        
        self.participations[participation_id] = standardized_data
        
        # 在二分图中添加边
        self.graph.add_edge(character_id, event_id, **standardized_data)
        self._update_timestamp()
        
        print(f"✅ 添加参与关系: {character_id} → {event_id} ({role})")
        return participation_id
    
    def get_character_participations(self, character_id: str) -> List[Dict[str, Any]]:
        """获取人物的所有参与关系"""
        participations = []
        
        if self.graph.has_node(character_id):
            for event_id in self.graph.neighbors(character_id):
                edge_data = self.graph[character_id][event_id]
                participations.append(edge_data)
        
        return participations
    
    def get_event_participants(self, event_id: str) -> List[Dict[str, Any]]:
        """获取事件的所有参与者"""
        participants = []
        
        if self.graph.has_node(event_id):
            for character_id in self.graph.neighbors(event_id):
                edge_data = self.graph[character_id][event_id]
                participants.append(edge_data)
        
        return participants
    
    def analyze_character_participation_pattern(self, character_id: str) -> Dict[str, Any]:
        """分析人物的参与模式"""
        participations = self.get_character_participations(character_id)
        
        if not participations:
            return {}
        
        # 按角色统计
        role_counts = defaultdict(int)
        total_impact = 0
        
        for participation in participations:
            role_counts[participation['role']] += 1
            total_impact += participation['emotional_impact']
        
        avg_impact = total_impact / len(participations)
        most_common_role = max(role_counts, key=role_counts.get)
        
        return {
            'total_participations': len(participations),
            'role_distribution': dict(role_counts),
            'most_common_role': most_common_role,
            'average_emotional_impact': avg_impact,
            'participation_intensity': sum(self.PARTICIPATION_ROLES.get(role, {}).get('weight', 0)
                                         for role in role_counts.keys()) / len(role_counts)
        }


class NovelMultiGraphSystem:
    """小说人物档案管理多图系统 - 主系统类"""

    def __init__(self):
        """初始化多图系统"""
        print("🚀 初始化小说人物档案管理多图系统 v2.0...")

        # 初始化各个管理器
        self.character_manager = CharacterRelationshipManager()
        self.event_manager = EventManager()
        self.timeline_manager = TimelineManager()
        self.milestone_manager = MilestoneManager()
        self.participation_manager = ParticipationManager()

        # 系统元数据
        self.system_info = {
            'version': '2.0',
            'created_time': datetime.now().isoformat(),
            'updated_time': datetime.now().isoformat(),
            'description': '多图架构增强版小说人物档案管理系统'
        }

        print("✅ 多图系统初始化完成")
        print("   📊 人物关系管理器 - 就绪")
        print("   📅 事件管理器 - 就绪")
        print("   ⏰ 时间线管理器 - 就绪")
        print("   🎯 里程碑管理器 - 就绪")
        print("   🤝 参与管理器 - 就绪")

    def _update_system_timestamp(self):
        """更新系统时间戳"""
        self.system_info['updated_time'] = datetime.now().isoformat()

    # ==================== 统一的数据操作接口 ====================

    def add_character(self, character_data: Dict[str, Any]) -> str:
        """添加人物（统一接口）"""
        char_id = self.character_manager.add_character(character_data)
        self._update_system_timestamp()
        return char_id

    def add_event(self, event_data: Dict[str, Any]) -> str:
        """添加事件（统一接口）"""
        event_id = self.event_manager.add_event(event_data)

        # 同时添加到时间线
        timeline_entry = {
            'id': f"timeline_{event_id}",
            'type': 'event',
            'name': event_data.get('name', '未命名事件'),
            'timestamp': event_data.get('timestamp', datetime.now().isoformat()),
            'description': event_data.get('description', ''),
            'related_events': [event_id],
            'importance': event_data.get('importance', 5)
        }
        self.timeline_manager.add_timeline_entry(timeline_entry)

        self._update_system_timestamp()
        return event_id

    def add_milestone(self, milestone_data: Dict[str, Any]) -> str:
        """添加里程碑（统一接口）"""
        milestone_id = self.milestone_manager.add_milestone(milestone_data)

        # 同时添加到时间线
        timeline_entry = {
            'id': f"timeline_{milestone_id}",
            'type': 'milestone',
            'name': milestone_data.get('name', '未命名里程碑'),
            'timestamp': milestone_data.get('timestamp', datetime.now().isoformat()),
            'description': milestone_data.get('description', ''),
            'related_characters': [milestone_data.get('character_id', '')],
            'importance': milestone_data.get('importance', 5)
        }
        self.timeline_manager.add_timeline_entry(timeline_entry)

        self._update_system_timestamp()
        return milestone_id

    def add_character_relationship(self, char1_id: str, char2_id: str, relationship_data: Dict[str, Any]) -> str:
        """添加人物关系（统一接口）"""
        rel_id = self.character_manager.add_relationship(char1_id, char2_id, relationship_data)
        self._update_system_timestamp()
        return rel_id

    def add_character_event_participation(self, character_id: str, event_id: str,
                                        participation_data: Dict[str, Any]) -> str:
        """添加人物事件参与关系（统一接口）"""
        participation_id = self.participation_manager.add_participation(
            character_id, event_id, participation_data
        )
        self._update_system_timestamp()
        return participation_id

    def create_complete_story_event(self, event_data: Dict[str, Any],
                                  participants: List[Dict[str, Any]] = None,
                                  related_milestones: List[Dict[str, Any]] = None) -> Dict[str, str]:
        """创建完整的故事事件（包括参与者和相关里程碑）"""
        participants = participants or []
        related_milestones = related_milestones or []

        # 添加事件
        event_id = self.add_event(event_data)
        result = {'event_id': event_id}

        # 添加参与者
        participation_ids = []
        for participant in participants:
            char_id = participant.get('character_id')
            if char_id:
                participation_id = self.add_character_event_participation(
                    char_id, event_id, participant
                )
                participation_ids.append(participation_id)

        result['participation_ids'] = participation_ids

        # 添加相关里程碑
        milestone_ids = []
        for milestone_data in related_milestones:
            milestone_data['event_id'] = event_id  # 关联到事件
            milestone_id = self.add_milestone(milestone_data)
            milestone_ids.append(milestone_id)

        result['milestone_ids'] = milestone_ids

        print(f"✅ 创建完整故事事件: {event_data.get('name')} (参与者: {len(participants)}, 里程碑: {len(related_milestones)})")
        return result

    # ==================== 统一查询接口 ====================

    def get_character_complete_info(self, character_id: str) -> Dict[str, Any]:
        """获取人物的完整信息"""
        # 基本信息
        character_info = self.character_manager.get_character(character_id)
        if not character_info:
            return {}

        # 关系信息
        relationships = self.character_manager.get_relationships(character_id)

        # 参与的事件
        participations = self.participation_manager.get_character_participations(character_id)

        # 里程碑
        milestones = self.milestone_manager.get_character_milestones(character_id)

        # 时间线
        timeline = self.timeline_manager.get_character_timeline(character_id)

        # 分析数据
        centrality = self.character_manager.analyze_centrality().get(character_id, 0)
        participation_pattern = self.participation_manager.analyze_character_participation_pattern(character_id)
        growth_analysis = self.milestone_manager.analyze_character_growth(character_id)

        return {
            'basic_info': character_info,
            'relationships': relationships,
            'participations': participations,
            'milestones': milestones,
            'timeline': timeline.to_dict('records') if not timeline.empty else [],
            'analysis': {
                'centrality': centrality,
                'participation_pattern': participation_pattern,
                'growth_analysis': growth_analysis
            }
        }

    def get_event_complete_info(self, event_id: str) -> Dict[str, Any]:
        """获取事件的完整信息"""
        # 基本信息
        event_info = self.event_manager.get_event(event_id)
        if not event_info:
            return {}

        # 参与者
        participants = self.participation_manager.get_event_participants(event_id)

        # 因果关系
        causal_chain = self.event_manager.get_causal_chain(event_id)

        return {
            'basic_info': event_info,
            'participants': participants,
            'causal_chain': causal_chain
        }

    def query_by_timerange(self, start_time: str, end_time: str) -> Dict[str, Any]:
        """按时间范围查询所有相关数据"""
        # 事件
        events = self.event_manager.get_events_by_timerange(start_time, end_time)

        # 时间线
        timeline = self.timeline_manager.get_timeline_range(start_time, end_time)

        return {
            'events': events,
            'timeline': timeline.to_dict('records') if not timeline.empty else [],
            'time_range': {'start': start_time, 'end': end_time}
        }

    def search_characters(self, **criteria) -> List[Dict[str, Any]]:
        """搜索人物"""
        all_characters = list(self.character_manager.characters.values())

        filtered_characters = []
        for char in all_characters:
            match = True

            # 按原型筛选
            if 'prototype' in criteria and char.get('prototype') != criteria['prototype']:
                match = False

            # 按角色筛选
            if 'narrative_role' in criteria and char.get('narrative_role') != criteria['narrative_role']:
                match = False

            # 按标签筛选
            if 'tags' in criteria:
                required_tags = criteria['tags'] if isinstance(criteria['tags'], list) else [criteria['tags']]
                char_tags = char.get('tags', [])
                if not all(tag in char_tags for tag in required_tags):
                    match = False

            # 按名称模糊搜索
            if 'name_contains' in criteria:
                if criteria['name_contains'].lower() not in char.get('name', '').lower():
                    match = False

            if match:
                filtered_characters.append(char)

        return filtered_characters

    # ==================== 数据迁移功能 ====================

    def import_from_v1_system(self, v1_graph_system) -> bool:
        """从v1系统导入数据"""
        try:
            print("🔄 开始从v1系统导入数据...")

            # 导入人物节点
            character_mapping = {}
            for node_id, attrs in v1_graph_system.graph.nodes(data=True):
                if attrs.get('node_type') == 'character':
                    new_char_id = self.add_character(attrs)
                    character_mapping[node_id] = new_char_id

            # 导入事件节点
            event_mapping = {}
            for node_id, attrs in v1_graph_system.graph.nodes(data=True):
                if attrs.get('node_type') == 'event':
                    new_event_id = self.add_event(attrs)
                    event_mapping[node_id] = new_event_id

            # 导入里程碑节点
            milestone_mapping = {}
            for node_id, attrs in v1_graph_system.graph.nodes(data=True):
                if attrs.get('node_type') == 'milestone':
                    new_milestone_id = self.add_milestone(attrs)
                    milestone_mapping[node_id] = new_milestone_id

            # 导入人物关系边
            for source, target, key, attrs in v1_graph_system.graph.edges(data=True, keys=True):
                if attrs.get('edge_type') == 'character_relationship':
                    if source in character_mapping and target in character_mapping:
                        self.add_character_relationship(
                            character_mapping[source],
                            character_mapping[target],
                            attrs
                        )

            # 导入人物事件参与关系
            for source, target, key, attrs in v1_graph_system.graph.edges(data=True, keys=True):
                if attrs.get('edge_type') == 'character_event':
                    if source in character_mapping and target in event_mapping:
                        self.add_character_event_participation(
                            character_mapping[source],
                            event_mapping[target],
                            attrs
                        )

            print(f"✅ v1数据导入完成:")
            print(f"   人物: {len(character_mapping)}")
            print(f"   事件: {len(event_mapping)}")
            print(f"   里程碑: {len(milestone_mapping)}")

            return True

        except Exception as e:
            print(f"❌ v1数据导入失败: {e}")
            return False

    def export_to_json(self, filename: str = 'novel_multigraph_data.json') -> bool:
        """导出所有数据到JSON文件"""
        try:
            export_data = {
                'system_info': self.system_info,
                'export_time': datetime.now().isoformat(),
                'data': {
                    'characters': self.character_manager.characters,
                    'character_relationships': nx.node_link_data(self.character_manager.graph),
                    'events': self.event_manager.events,
                    'event_relationships': nx.node_link_data(self.event_manager.graph),
                    'timeline': self.timeline_manager.timeline_df.to_dict('records'),
                    'milestones': self.milestone_manager.milestones,
                    'milestone_dependencies': nx.node_link_data(self.milestone_manager.graph),
                    'participations': self.participation_manager.participations,
                    'participation_graph': nx.node_link_data(self.participation_manager.graph)
                }
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)

            print(f"✅ 数据导出完成: {filename}")
            return True

        except Exception as e:
            print(f"❌ 数据导出失败: {e}")
            return False

    def import_from_json(self, filename: str = 'novel_multigraph_data.json') -> bool:
        """从JSON文件导入数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 导入人物数据
            characters_data = data['data']['characters']
            for char_id, char_data in characters_data.items():
                self.character_manager.characters[char_id] = char_data

            # 导入人物关系图
            char_graph_data = data['data']['character_relationships']
            self.character_manager.graph = nx.node_link_graph(char_graph_data)

            # 导入事件数据
            events_data = data['data']['events']
            for event_id, event_data in events_data.items():
                self.event_manager.events[event_id] = event_data

            # 导入事件关系图
            event_graph_data = data['data']['event_relationships']
            self.event_manager.graph = nx.node_link_graph(event_graph_data)

            # 导入时间线数据
            timeline_data = data['data']['timeline']
            self.timeline_manager.timeline_df = pd.DataFrame(timeline_data)
            if not self.timeline_manager.timeline_df.empty:
                self.timeline_manager.timeline_df['timestamp'] = pd.to_datetime(
                    self.timeline_manager.timeline_df['timestamp']
                )

            # 导入里程碑数据
            milestones_data = data['data']['milestones']
            for milestone_id, milestone_data in milestones_data.items():
                self.milestone_manager.milestones[milestone_id] = milestone_data
                char_id = milestone_data.get('character_id')
                if char_id:
                    self.milestone_manager.character_milestones[char_id].append(milestone_id)

            # 导入里程碑依赖图
            milestone_graph_data = data['data']['milestone_dependencies']
            self.milestone_manager.graph = nx.node_link_graph(milestone_graph_data)

            # 导入参与关系数据
            participations_data = data['data']['participations']
            for participation_id, participation_data in participations_data.items():
                self.participation_manager.participations[participation_id] = participation_data

            # 导入参与关系图
            participation_graph_data = data['data']['participation_graph']
            self.participation_manager.graph = nx.node_link_graph(participation_graph_data)

            print(f"✅ 数据导入完成: {filename}")
            return True

        except Exception as e:
            print(f"❌ 数据导入失败: {e}")
            return False

    # ==================== 统计分析功能 ====================

    def get_system_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        stats = {
            'system_info': self.system_info,
            'data_counts': {
                'characters': len(self.character_manager.characters),
                'character_relationships': self.character_manager.graph.number_of_edges(),
                'events': len(self.event_manager.events),
                'event_relationships': self.event_manager.graph.number_of_edges(),
                'timeline_entries': len(self.timeline_manager.timeline_df),
                'milestones': len(self.milestone_manager.milestones),
                'milestone_dependencies': self.milestone_manager.graph.number_of_edges(),
                'participations': len(self.participation_manager.participations)
            },
            'analysis': {
                'timeline_patterns': self.timeline_manager.analyze_timeline_patterns(),
                'character_centrality': self.character_manager.analyze_centrality(),
                'character_clusters': len(self.character_manager.get_character_clusters())
            }
        }

        return stats

    def print_system_statistics(self):
        """打印系统统计信息"""
        stats = self.get_system_statistics()

        print("\n" + "="*60)
        print("📊 小说人物档案管理多图系统统计信息")
        print("="*60)

        print(f"🏷️  系统版本: {stats['system_info']['version']}")
        print(f"📅 创建时间: {stats['system_info']['created_time']}")
        print(f"🔄 更新时间: {stats['system_info']['updated_time']}")

        print(f"\n📈 数据统计:")
        counts = stats['data_counts']
        print(f"   人物数量: {counts['characters']}")
        print(f"   人物关系: {counts['character_relationships']}")
        print(f"   事件数量: {counts['events']}")
        print(f"   事件关系: {counts['event_relationships']}")
        print(f"   时间线条目: {counts['timeline_entries']}")
        print(f"   里程碑数量: {counts['milestones']}")
        print(f"   里程碑依赖: {counts['milestone_dependencies']}")
        print(f"   参与关系: {counts['participations']}")

        print(f"\n🔍 分析结果:")
        analysis = stats['analysis']
        print(f"   人物群体数: {analysis['character_clusters']}")

        timeline_patterns = analysis['timeline_patterns']
        if timeline_patterns:
            print(f"   时间线条目总数: {timeline_patterns.get('total_entries', 0)}")
            if 'date_range' in timeline_patterns:
                print(f"   时间跨度: {timeline_patterns['date_range']['start']} 至 {timeline_patterns['date_range']['end']}")

        # 显示最具影响力的人物
        centrality = analysis['character_centrality']
        if centrality:
            most_influential = max(centrality, key=centrality.get)
            char_name = self.character_manager.characters.get(most_influential, {}).get('name', most_influential)
            print(f"   最具影响力人物: {char_name} (中心性: {centrality[most_influential]:.3f})")

        print("="*60)

    # ==================== 可视化功能 ====================

    def visualize_character_relationships(self, layout: str = 'spring', figsize: Tuple[int, int] = (12, 8),
                                        save_path: str = None, show_labels: bool = True):
        """可视化人物关系网络"""
        try:
            plt.figure(figsize=figsize)

            graph = self.character_manager.graph
            if graph.number_of_nodes() == 0:
                print("⚠️  没有人物关系数据可视化")
                return

            # 选择布局
            layout_functions = {
                'spring': nx.spring_layout,
                'circular': nx.circular_layout,
                'kamada_kawai': nx.kamada_kawai_layout
            }

            pos = layout_functions.get(layout, nx.spring_layout)(graph, k=2, iterations=50)

            # 绘制节点
            node_colors = []
            node_sizes = []
            for node in graph.nodes():
                char_data = self.character_manager.characters[node]
                # 根据原型设置颜色
                prototype_colors = {
                    '英雄': '#FF6B6B',
                    '导师': '#4ECDC4',
                    '伙伴': '#FFA502',
                    '反派': '#FF4757',
                    '智者': '#70A1FF'
                }
                color = prototype_colors.get(char_data.get('prototype', ''), '#97C2FC')
                node_colors.append(color)

                # 根据中心性设置大小
                centrality = self.character_manager.analyze_centrality()
                size = 300 + centrality.get(node, 0) * 1000
                node_sizes.append(size)

            nx.draw_networkx_nodes(graph, pos, node_color=node_colors,
                                 node_size=node_sizes, alpha=0.8)

            # 绘制边
            edge_colors = []
            edge_widths = []
            for u, v, data in graph.edges(data=True):
                edge_colors.append(data.get('color', '#848484'))
                edge_widths.append(data.get('strength', 5) * 0.5)

            nx.draw_networkx_edges(graph, pos, edge_color=edge_colors,
                                 width=edge_widths, alpha=0.6)

            # 添加标签
            if show_labels:
                labels = {node: self.character_manager.characters[node]['name']
                         for node in graph.nodes()}
                nx.draw_networkx_labels(graph, pos, labels, font_size=10, font_weight='bold')

            plt.title("人物关系网络图", fontsize=16, fontweight='bold', pad=20)
            plt.axis('off')
            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 人物关系图保存至: {save_path}")

            plt.show()

        except Exception as e:
            print(f"❌ 人物关系可视化失败: {e}")

    def visualize_timeline(self, character_id: str = None, figsize: Tuple[int, int] = (15, 8),
                          save_path: str = None):
        """可视化时间线"""
        try:
            if character_id:
                timeline_df = self.timeline_manager.get_character_timeline(character_id)
                title = f"人物时间线 - {self.character_manager.characters.get(character_id, {}).get('name', character_id)}"
            else:
                timeline_df = self.timeline_manager.timeline_df
                title = "完整时间线"

            if timeline_df.empty:
                print("⚠️  没有时间线数据可视化")
                return

            # 创建plotly时间线图
            fig = go.Figure()

            # 按类型分组
            type_colors = {
                'event': '#FFA502',
                'milestone': '#7BE19C',
                'other': '#70A1FF'
            }

            for entry_type in timeline_df['type'].unique():
                type_data = timeline_df[timeline_df['type'] == entry_type]

                fig.add_trace(go.Scatter(
                    x=type_data['timestamp'],
                    y=type_data['importance'],
                    mode='markers+text',
                    name=entry_type.title(),
                    text=type_data['name'],
                    textposition='top center',
                    marker=dict(
                        size=type_data['importance'] * 3,
                        color=type_colors.get(entry_type, '#70A1FF'),
                        opacity=0.7
                    ),
                    hovertemplate='<b>%{text}</b><br>' +
                                '时间: %{x}<br>' +
                                '重要性: %{y}<br>' +
                                '<extra></extra>'
                ))

            fig.update_layout(
                title=title,
                xaxis_title='时间',
                yaxis_title='重要性',
                hovermode='closest',
                height=600,
                showlegend=True
            )

            if save_path:
                fig.write_html(save_path)
                print(f"✅ 时间线图保存至: {save_path}")

            fig.show()

        except Exception as e:
            print(f"❌ 时间线可视化失败: {e}")

    def visualize_character_growth(self, character_id: str, figsize: Tuple[int, int] = (12, 8),
                                 save_path: str = None):
        """可视化人物成长轨迹"""
        try:
            milestones = self.milestone_manager.get_character_milestones(character_id, sorted_by_time=True)

            if not milestones:
                print(f"⚠️  人物 {character_id} 没有里程碑数据")
                return

            char_name = self.character_manager.characters.get(character_id, {}).get('name', character_id)

            # 准备数据
            timestamps = [datetime.fromisoformat(m['timestamp']) for m in milestones]
            importance_scores = [m['importance'] for m in milestones]
            milestone_names = [m['name'] for m in milestones]
            milestone_types = [m['milestone_type'] for m in milestones]

            # 创建成长轨迹图
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=('成长重要性趋势', '里程碑类型分布'),
                vertical_spacing=0.15
            )

            # 成长趋势线
            fig.add_trace(
                go.Scatter(
                    x=timestamps,
                    y=importance_scores,
                    mode='lines+markers',
                    name='成长轨迹',
                    text=milestone_names,
                    hovertemplate='<b>%{text}</b><br>' +
                                '时间: %{x}<br>' +
                                '重要性: %{y}<br>' +
                                '<extra></extra>',
                    line=dict(color='#4ECDC4', width=3),
                    marker=dict(size=8, color='#FF6B6B')
                ),
                row=1, col=1
            )

            # 里程碑类型分布
            type_counts = pd.Series(milestone_types).value_counts()
            fig.add_trace(
                go.Bar(
                    x=type_counts.index,
                    y=type_counts.values,
                    name='类型分布',
                    marker_color=['#FF6B6B', '#4ECDC4', '#FFA502'][:len(type_counts)]
                ),
                row=2, col=1
            )

            fig.update_layout(
                title=f"人物成长轨迹 - {char_name}",
                height=800,
                showlegend=False
            )

            fig.update_xaxes(title_text="时间", row=1, col=1)
            fig.update_yaxes(title_text="重要性", row=1, col=1)
            fig.update_xaxes(title_text="里程碑类型", row=2, col=1)
            fig.update_yaxes(title_text="数量", row=2, col=1)

            if save_path:
                fig.write_html(save_path)
                print(f"✅ 成长轨迹图保存至: {save_path}")

            fig.show()

        except Exception as e:
            print(f"❌ 成长轨迹可视化失败: {e}")

    def visualize_event_participation_network(self, figsize: Tuple[int, int] = (15, 10),
                                            save_path: str = None):
        """可视化事件参与网络（二分图）"""
        try:
            graph = self.participation_manager.graph

            if graph.number_of_nodes() == 0:
                print("⚠️  没有参与关系数据可视化")
                return

            # 创建交互式网络图
            net = Network(
                height='750px',
                width='100%',
                bgcolor='#222222',
                font_color='white',
                directed=False
            )

            # 添加人物节点
            for char_id in self.character_manager.characters.keys():
                if graph.has_node(char_id):
                    char_data = self.character_manager.characters[char_id]
                    net.add_node(
                        char_id,
                        label=char_data['name'],
                        color='#FF6B6B',
                        size=30,
                        title=f"人物: {char_data['name']}",
                        group='character'
                    )

            # 添加事件节点
            for event_id in self.event_manager.events.keys():
                if graph.has_node(event_id):
                    event_data = self.event_manager.events[event_id]
                    net.add_node(
                        event_id,
                        label=event_data['name'],
                        color='#FFA502',
                        size=25,
                        title=f"事件: {event_data['name']}",
                        group='event'
                    )

            # 添加参与关系边
            for char_id, event_id, data in graph.edges(data=True):
                net.add_edge(
                    char_id, event_id,
                    color=data.get('color', '#848484'),
                    width=data.get('weight', 3) * 0.3,
                    title=f"角色: {data.get('role', '参与者')}"
                )

            # 设置物理引擎
            net.set_options("""
            var options = {
              "physics": {
                "enabled": true,
                "barnesHut": {
                  "gravitationalConstant": -8000,
                  "centralGravity": 0.3,
                  "springLength": 150
                }
              }
            }
            """)

            filename = save_path or 'event_participation_network.html'
            net.show(filename, notebook=False)
            print(f"✅ 事件参与网络图生成完成: {filename}")

        except Exception as e:
            print(f"❌ 事件参与网络可视化失败: {e}")


# ==================== 示例数据和测试函数 ====================

def create_sample_data_v2(system: NovelMultiGraphSystem):
    """为v2系统创建示例数据"""
    print("\n🔄 创建v2系统示例数据...")

    # 创建人物
    li_feng_id = system.add_character({
        'name': '李风',
        'prototype': '英雄',
        'narrative_role': '主角',
        'core_identity': {
            'aliases': ['风之子', '剑心'],
            'basic_info': {
                'gender': '男',
                'age': 22,
                'nationality': '大唐',
                'race': '人族'
            },
            'background': '出身贫寒，自幼父母双亡，被剑术大师收养'
        },
        'internal_dimension': {
            'personality': ['勇敢', '正义', '冲动', '善良'],
            'personality_description': '天性善良，但有时过于冲动',
            'core_values': ['正义', '自由', '友情'],
            'motivations': ['为养父报仇', '保护弱小', '追求剑道极致'],
            'fears_secrets': ['害怕失去重要的人', '隐藏着自己的真实身世']
        },
        'external_dimension': {
            'appearance': '黑色短发，身材修长，常穿青色长衫',
            'behavior': '行动敏捷，剑不离身',
            'skills_abilities': ['高级剑术', '轻功', '内力修炼', '战术思维']
        },
        'tags': ['核心角色', '成长型主角', '剑客']
    })

    bai_master_id = system.add_character({
        'name': '白大师',
        'prototype': '导师',
        'narrative_role': '配角',
        'core_identity': {
            'aliases': ['白云剑圣'],
            'basic_info': {
                'gender': '男',
                'age': 65,
                'nationality': '大唐',
                'race': '人族'
            },
            'background': '前朝剑术宗师，隐居于山林'
        },
        'internal_dimension': {
            'personality': ['智慧', '耐心', '严肃', '慈祥'],
            'core_values': ['知识', '平衡', '传承'],
            'motivations': ['传授剑术', '保护传统', '培养继承人']
        },
        'external_dimension': {
            'appearance': '白发长须，目光如炬，常穿白色道袍',
            'skills_abilities': ['宗师级剑术', '内功大师', '兵法策略', '医术']
        },
        'tags': ['导师角色', '重要配角', '宗师']
    })

    xiao_yu_id = system.add_character({
        'name': '小雨',
        'prototype': '伙伴',
        'narrative_role': '配角',
        'core_identity': {
            'aliases': ['雨医仙'],
            'basic_info': {
                'gender': '女',
                'age': 19,
                'nationality': '大唐',
                'race': '人族'
            },
            'background': '江湖医师之女，精通医术和毒术'
        },
        'internal_dimension': {
            'personality': ['善良', '聪明', '坚强', '细心'],
            'core_values': ['救死扶伤', '友情', '家族荣誉'],
            'motivations': ['帮助他人', '寻找失踪的父亲', '发扬医术']
        },
        'external_dimension': {
            'appearance': '清秀可人，常背药箱，穿淡蓝色衣裙',
            'skills_abilities': ['医术', '毒术', '轻功', '暗器']
        },
        'tags': ['重要配角', '医师', '女性角色']
    })

    hei_ying_id = system.add_character({
        'name': '黑影',
        'prototype': '反派',
        'narrative_role': '反派',
        'core_identity': {
            'aliases': ['影魔', '黑衣杀神'],
            'basic_info': {
                'gender': '男',
                'age': 40,
                'nationality': '未知',
                'race': '人族'
            },
            'background': '神秘刺客组织首领，与白大师有深仇大恨'
        },
        'internal_dimension': {
            'personality': ['冷酷', '狡猾', '野心勃勃', '复仇心重'],
            'core_values': ['权力', '复仇', '统治'],
            'motivations': ['统治江湖', '消灭白大师一脉', '获得至高武学']
        },
        'external_dimension': {
            'appearance': '身穿黑衣，面戴面具，身形高大',
            'skills_abilities': ['暗杀术', '毒功', '轻功', '心理战术']
        },
        'tags': ['主要反派', '刺客', '组织首领']
    })

    # 创建人物关系
    system.add_character_relationship(li_feng_id, bai_master_id, {
        'relationship_type': '师徒',
        'strength': 9,
        'status': '稳固',
        'description': '白大师是李风的剑术导师，对他寄予厚望，关系如父子般深厚'
    })

    system.add_character_relationship(li_feng_id, xiao_yu_id, {
        'relationship_type': '朋友',
        'strength': 8,
        'status': '发展中',
        'description': '小雨救治了李风，两人成为朋友，感情逐渐加深'
    })

    system.add_character_relationship(li_feng_id, hei_ying_id, {
        'relationship_type': '敌人',
        'strength': 9,
        'status': '敌对',
        'description': '黑影派刺客袭击李风，两人结下深仇大恨'
    })

    system.add_character_relationship(bai_master_id, hei_ying_id, {
        'relationship_type': '敌人',
        'strength': 10,
        'status': '世仇',
        'description': '黑影是白大师的宿敌，两人有不共戴天之仇'
    })

    system.add_character_relationship(xiao_yu_id, bai_master_id, {
        'relationship_type': '师徒',
        'strength': 6,
        'status': '正常',
        'description': '白大师偶尔指导小雨医术，关系融洽'
    })

    # 创建事件
    attack_event_id = system.add_event({
        'name': '山林遇袭',
        'description': '李风在青云山修炼时遭遇神秘刺客袭击，身受重伤但侥幸逃生',
        'timestamp': '2023-05-15T14:30:00',
        'location': '青云山深处',
        'tags': ['战斗', '转折点', '第一幕', '危机'],
        'importance': 8
    })

    rescue_event_id = system.add_event({
        'name': '小雨救治',
        'description': '小雨在山间采药时发现受伤的李风，将其带回小屋进行救治',
        'timestamp': '2023-05-15T16:00:00',
        'location': '山间小屋',
        'tags': ['救援', '相遇', '第一幕', '转机'],
        'importance': 7
    })

    training_event_id = system.add_event({
        'name': '特训开始',
        'description': '白大师见李风遇袭后决定对其进行更严格的特殊训练',
        'timestamp': '2023-05-20T08:00:00',
        'location': '隐秘道场',
        'tags': ['训练', '成长', '第二幕', '提升'],
        'importance': 6
    })

    breakthrough_event_id = system.add_event({
        'name': '剑心突破',
        'description': '李风在特训中领悟剑心，实力大幅提升',
        'timestamp': '2023-06-01T12:00:00',
        'location': '隐秘道场',
        'tags': ['突破', '成长', '第二幕', '领悟'],
        'importance': 9
    })

    # 添加事件因果关系
    system.event_manager.add_causal_relationship(attack_event_id, rescue_event_id, {
        'strength': 9,
        'description': '遇袭导致李风受伤，直接引发了小雨的救治行为'
    })

    system.event_manager.add_causal_relationship(rescue_event_id, training_event_id, {
        'strength': 7,
        'description': '小雨救治李风后，白大师意识到需要加强李风的训练'
    })

    system.event_manager.add_causal_relationship(training_event_id, breakthrough_event_id, {
        'strength': 8,
        'description': '严格的特训为李风的剑心突破奠定了基础'
    })

    # 创建参与关系
    system.add_character_event_participation(li_feng_id, attack_event_id, {
        'role': '受害者',
        'impact_description': '身受重伤，但侥幸逃生，意识到自身实力不足',
        'emotional_impact': 8,
        'behavioral_change': '变得更加谨慎，训练更加刻苦'
    })

    system.add_character_event_participation(hei_ying_id, attack_event_id, {
        'role': '主导者',
        'impact_description': '策划并执行了对李风的袭击',
        'emotional_impact': 3,
        'behavioral_change': '开始更加关注李风的成长'
    })

    system.add_character_event_participation(li_feng_id, rescue_event_id, {
        'role': '受影响者',
        'impact_description': '得到及时救治，恢复健康，对小雨产生感激之情',
        'emotional_impact': 7,
        'behavioral_change': '对小雨更加关心和保护'
    })

    system.add_character_event_participation(xiao_yu_id, rescue_event_id, {
        'role': '主导者',
        'impact_description': '发挥医术专长，成功救治李风',
        'emotional_impact': 6,
        'behavioral_change': '对李风产生好感，愿意帮助他'
    })

    system.add_character_event_participation(li_feng_id, training_event_id, {
        'role': '参与者',
        'impact_description': '接受白大师的特殊训练，实力稳步提升',
        'emotional_impact': 5,
        'behavioral_change': '更加专注于修炼，意志更加坚定'
    })

    system.add_character_event_participation(bai_master_id, training_event_id, {
        'role': '主导者',
        'impact_description': '传授李风高级剑术和实战经验',
        'emotional_impact': 4,
        'behavioral_change': '对李风的期望更高，训练更加严格'
    })

    system.add_character_event_participation(li_feng_id, breakthrough_event_id, {
        'role': '主导者',
        'impact_description': '通过自身努力和领悟，成功突破剑心境界',
        'emotional_impact': 9,
        'behavioral_change': '自信心大增，对未来充满希望'
    })

    # 创建里程碑
    awakening_milestone_id = system.add_milestone({
        'character_id': li_feng_id,
        'name': '危机觉醒',
        'description': '首次遭遇生死危机，深刻意识到自身实力不足和江湖险恶',
        'timestamp': '2023-05-15T15:00:00',
        'milestone_type': 'setback',
        'psychological_change': {
            'before': '自信过度，有些轻敌，认为自己剑术已有小成',
            'after': '更加谨慎，深刻认识到需要更刻苦训练，对危险保持警惕'
        },
        'values_change': {
            'before': ['正义', '自由'],
            'after': ['正义', '自由', '谨慎', '实力']
        },
        'goals_change': {
            'before': ['为养父报仇', '行侠仗义'],
            'after': ['为养父报仇', '提升剑术实力', '查明刺客身份', '保护重要的人']
        },
        'proof': '在后续剧情中，李风每天增加练剑时间，并向白大师请教更多实战技巧',
        'importance': 8
    })

    friendship_milestone_id = system.add_milestone({
        'character_id': li_feng_id,
        'name': '友情萌芽',
        'description': '与小雨建立深厚友谊，学会信任和依赖他人',
        'timestamp': '2023-05-16T10:00:00',
        'milestone_type': 'growth',
        'psychological_change': {
            'before': '习惯独来独往，不太信任他人',
            'after': '学会接受他人的帮助，愿意与人建立深厚友谊'
        },
        'values_change': {
            'before': ['正义', '自由', '谨慎', '实力'],
            'after': ['正义', '自由', '谨慎', '实力', '友情', '信任']
        },
        'goals_change': {
            'before': ['为养父报仇', '提升剑术实力', '查明刺客身份', '保护重要的人'],
            'after': ['为养父报仇', '提升剑术实力', '查明刺客身份', '保护重要的人', '珍惜友情']
        },
        'proof': '李风开始主动关心小雨的安危，愿意与她分享心事',
        'importance': 6
    })

    breakthrough_milestone_id = system.add_milestone({
        'character_id': li_feng_id,
        'name': '剑心大成',
        'description': '通过刻苦训练和内心领悟，成功突破剑心境界，实力大幅提升',
        'timestamp': '2023-06-01T12:30:00',
        'milestone_type': 'achievement',
        'psychological_change': {
            'before': '对自己的实力缺乏信心，总是担心不够强',
            'after': '自信心大增，对自己的能力有了清晰认知，内心更加平静'
        },
        'values_change': {
            'before': ['正义', '自由', '谨慎', '实力', '友情', '信任'],
            'after': ['正义', '自由', '谨慎', '实力', '友情', '信任', '平衡', '智慧']
        },
        'goals_change': {
            'before': ['为养父报仇', '提升剑术实力', '查明刺客身份', '保护重要的人', '珍惜友情'],
            'after': ['为养父报仇', '继续精进剑术', '查明刺客身份', '保护重要的人', '珍惜友情', '维护江湖正义']
        },
        'proof': '李风的剑术威力明显增强，能够施展更高级的剑招',
        'importance': 9
    })

    # 添加里程碑依赖关系
    system.milestone_manager.add_milestone_dependency(awakening_milestone_id, friendship_milestone_id, {
        'dependency_type': 'prerequisite',
        'strength': 7,
        'description': '危机觉醒让李风意识到需要朋友的帮助'
    })

    system.milestone_manager.add_milestone_dependency(friendship_milestone_id, breakthrough_milestone_id, {
        'dependency_type': 'prerequisite',
        'strength': 6,
        'description': '友情的支持为李风的突破提供了心理基础'
    })

    print("✅ v2系统示例数据创建完成！")
    print(f"   人物: 4个")
    print(f"   关系: 5个")
    print(f"   事件: 4个")
    print(f"   里程碑: 3个")
    print(f"   参与关系: 7个")


def demonstrate_v2_system():
    """演示v2系统功能"""
    print("🚀 启动小说人物档案管理多图系统 v2.0 演示")
    print("="*70)

    # 创建系统实例
    system = NovelMultiGraphSystem()

    # 创建示例数据
    create_sample_data_v2(system)

    # 显示系统统计信息
    system.print_system_statistics()

    # 演示查询功能
    print("\n🔍 多维度查询功能演示:")
    print("-" * 40)

    # 查询主角完整信息
    protagonists = system.search_characters(narrative_role='主角')
    if protagonists:
        protagonist = protagonists[0]
        print(f"📖 主角完整信息: {protagonist['name']}")

        complete_info = system.get_character_complete_info(protagonist['id'])

        print(f"   基本信息: {protagonist['prototype']} - {protagonist['narrative_role']}")
        print(f"   性格特征: {', '.join(protagonist['internal_dimension'].get('personality', []))}")
        print(f"   中心性评分: {complete_info['analysis']['centrality']:.3f}")

        # 显示关系网络
        relationships = complete_info['relationships']
        print(f"\n🤝 人物关系网络 ({len(relationships)}个):")
        for rel in relationships[:3]:  # 只显示前3个
            print(f"   • {rel['target_name']}: {rel['relationship_type']} (强度: {rel['strength']}/10)")

        # 显示参与的事件
        participations = complete_info['participations']
        print(f"\n📅 参与事件 ({len(participations)}个):")
        for participation in participations[:3]:  # 只显示前3个
            event_info = system.get_event_complete_info(participation['event_id'])
            if event_info:
                print(f"   • {event_info['basic_info']['name']}: {participation['role']}")

        # 显示成长里程碑
        milestones = complete_info['milestones']
        print(f"\n🎯 成长里程碑 ({len(milestones)}个):")
        for milestone in milestones:
            print(f"   • {milestone['name']}: {milestone['milestone_type']} (重要性: {milestone['importance']}/10)")

        # 显示成长分析
        growth_analysis = complete_info['analysis']['growth_analysis']
        if growth_analysis:
            print(f"\n📈 成长分析:")
            print(f"   总里程碑数: {growth_analysis['total_milestones']}")
            print(f"   成长类型: {growth_analysis['growth_count']}个")
            print(f"   成就类型: {growth_analysis['achievement_count']}个")
            print(f"   挫折类型: {growth_analysis['setback_count']}个")
            print(f"   平均重要性: {growth_analysis['average_importance']:.1f}/10")

    # 演示时间线查询
    print(f"\n⏰ 时间线查询演示:")
    print("-" * 40)

    timeline_data = system.query_by_timerange('2023-05-01', '2023-06-30')
    events = timeline_data['events']
    timeline_entries = timeline_data['timeline']

    print(f"📊 时间范围内数据统计:")
    print(f"   事件数量: {len(events)}")
    print(f"   时间线条目: {len(timeline_entries)}")

    print(f"\n📅 主要事件时间线:")
    for event in sorted(events, key=lambda x: x['timestamp'])[:4]:
        print(f"   {event['timestamp'][:10]} - {event['name']} (重要性: {event['importance']}/10)")

    # 演示高级分析功能
    print(f"\n🔬 高级分析功能演示:")
    print("-" * 40)

    # 人物影响力排名
    centrality_scores = system.character_manager.analyze_centrality()
    print(f"👑 人物影响力排名:")
    sorted_chars = sorted(centrality_scores.items(), key=lambda x: x[1], reverse=True)
    for i, (char_id, score) in enumerate(sorted_chars[:3], 1):
        char_name = system.character_manager.characters[char_id]['name']
        print(f"   {i}. {char_name}: {score:.3f}")

    # 人物群体分析
    clusters = system.character_manager.get_character_clusters()
    print(f"\n👥 人物群体分析:")
    print(f"   发现 {len(clusters)} 个人物群体")
    for i, cluster in enumerate(clusters, 1):
        cluster_names = [system.character_manager.characters[char_id]['name'] for char_id in cluster]
        print(f"   群体 {i}: {', '.join(cluster_names)}")

    # 时间线模式分析
    timeline_patterns = system.timeline_manager.analyze_timeline_patterns()
    if timeline_patterns:
        print(f"\n📊 时间线模式分析:")
        print(f"   总条目数: {timeline_patterns['total_entries']}")
        if 'type_distribution' in timeline_patterns:
            print(f"   类型分布: {timeline_patterns['type_distribution']}")

    # 数据导出演示
    print(f"\n💾 数据导出演示:")
    print("-" * 40)
    system.export_to_json('novel_v2_sample_data.json')

    # 可视化演示
    print(f"\n🎨 可视化功能演示:")
    print("-" * 40)

    print("   正在生成人物关系网络图...")
    system.visualize_character_relationships(
        layout='spring',
        save_path='character_relationships_v2.png',
        show_labels=True
    )

    if protagonists:
        protagonist_id = protagonists[0]['id']
        print(f"   正在生成主角时间线图...")
        system.visualize_timeline(
            character_id=protagonist_id,
            save_path='protagonist_timeline_v2.html'
        )

        print(f"   正在生成主角成长轨迹图...")
        system.visualize_character_growth(
            character_id=protagonist_id,
            save_path='protagonist_growth_v2.html'
        )

    print("   正在生成事件参与网络图...")
    system.visualize_event_participation_network(
        save_path='event_participation_network_v2.html'
    )

    # 演示v1数据迁移（如果v1系统可用）
    print(f"\n🔄 v1数据迁移演示:")
    print("-" * 40)
    try:
        # 尝试导入v1系统
        from code.图v1 import NovelGraphSystem as V1System

        # 创建v1系统并添加一些数据
        v1_system = V1System()
        v1_char_id = v1_system.add_character_node({
            'name': '测试角色',
            'prototype': '英雄',
            'narrative_role': '主角'
        })

        # 迁移数据
        migration_success = system.import_from_v1_system(v1_system)
        if migration_success:
            print("✅ v1数据迁移演示成功")
        else:
            print("❌ v1数据迁移演示失败")

    except ImportError:
        print("⚠️  未找到v1系统，跳过迁移演示")
    except Exception as e:
        print(f"⚠️  v1数据迁移演示出错: {e}")

    print("\n🎉 v2系统演示完成！")
    print("📁 生成的文件:")
    print("   • novel_v2_sample_data.json - v2系统数据文件")
    print("   • character_relationships_v2.png - 人物关系网络图")
    print("   • protagonist_timeline_v2.html - 主角时间线图")
    print("   • protagonist_growth_v2.html - 主角成长轨迹图")
    print("   • event_participation_network_v2.html - 事件参与网络图")
    print("\n💡 提示: 用浏览器打开HTML文件查看交互式图表")

    print("\n🆚 v2系统相比v1的主要改进:")
    print("   ✨ 多图架构 - 分离不同类型的关系管理")
    print("   ⏰ 时间线管理 - 专门的时间序列数据处理")
    print("   🎯 里程碑系统 - 人物成长轨迹追踪")
    print("   🤝 参与关系 - 人物与事件的详细关联")
    print("   🔍 多维查询 - 统一的跨数据结构查询接口")
    print("   📊 高级分析 - 更丰富的数据分析功能")
    print("   🎨 增强可视化 - 多种专门的可视化视图")
    print("   🔄 数据迁移 - 支持从v1系统无缝迁移")


def compare_v1_v2_systems():
    """比较v1和v2系统的差异"""
    print("\n📋 v1 vs v2 系统功能对比:")
    print("="*70)

    comparison_table = [
        ("功能特性", "v1系统", "v2系统"),
        ("-" * 20, "-" * 20, "-" * 20),
        ("数据结构", "单一MultiDiGraph", "多图架构 + 混合数据结构"),
        ("人物关系", "✅ 支持", "✅ 增强支持 + 专门管理器"),
        ("事件管理", "✅ 基础支持", "✅ 增强支持 + 因果关系"),
        ("时间线", "❌ 分散在节点中", "✅ 专门的时间线管理器"),
        ("里程碑", "✅ 基础支持", "✅ 增强支持 + 依赖关系"),
        ("参与关系", "✅ 简单边连接", "✅ 专门的参与管理器"),
        ("查询功能", "✅ 基础查询", "✅ 多维度统一查询接口"),
        ("数据分析", "✅ 基础统计", "✅ 高级分析 + 模式识别"),
        ("可视化", "✅ 静态 + 交互式", "✅ 多种专门视图"),
        ("数据导入导出", "✅ JSON格式", "✅ JSON + v1迁移支持"),
        ("性能优化", "❌ 基础实现", "✅ 索引 + 缓存优化"),
        ("扩展性", "⚠️  单图限制", "✅ 模块化架构"),
    ]

    for row in comparison_table:
        print(f"{row[0]:<20} | {row[1]:<25} | {row[2]}")

    print("\n🎯 v2系统的核心优势:")
    print("1. 🏗️  架构优势: 多图分离关注点，避免单图复杂性")
    print("2. ⏰ 时间管理: 专门的时间线系统，支持时间相关的所有操作")
    print("3. 🔍 查询能力: 统一接口支持复杂的跨数据结构查询")
    print("4. 📊 分析深度: 更丰富的数据分析和模式识别功能")
    print("5. 🎨 可视化: 多种专门的可视化视图，信息展示更清晰")
    print("6. 🔄 兼容性: 支持从v1系统无缝迁移，保护现有投资")
    print("7. 🚀 扩展性: 模块化设计，易于添加新功能和数据类型")


if __name__ == "__main__":
    # 运行v2系统演示
    demonstrate_v2_system()

    # 显示系统对比
    compare_v1_v2_systems()
