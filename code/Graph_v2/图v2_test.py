# -*- coding: utf-8 -*-
"""
小说人物档案管理系统 v2.0 - 核心功能测试版
测试多图架构的核心功能，不包含复杂的可视化依赖
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict

try:
    import networkx as nx
    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False
    print("⚠️  NetworkX未安装，使用简化的图结构")

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("⚠️  Pandas未安装，使用简化的时间线管理")


class SimpleGraph:
    """简化的图结构实现（当NetworkX不可用时）"""
    
    def __init__(self, directed=False):
        self.directed = directed
        self.nodes_data = {}
        self.edges_data = defaultdict(dict)
        
    def add_node(self, node_id, **attrs):
        self.nodes_data[node_id] = attrs
        
    def add_edge(self, source, target, **attrs):
        if self.directed:
            self.edges_data[source][target] = attrs
        else:
            self.edges_data[source][target] = attrs
            self.edges_data[target][source] = attrs
    
    def has_node(self, node_id):
        return node_id in self.nodes_data
    
    def has_edge(self, source, target):
        return target in self.edges_data.get(source, {})
    
    def nodes(self, data=False):
        if data:
            return self.nodes_data.items()
        return self.nodes_data.keys()
    
    def edges(self, data=False):
        edges = []
        for source, targets in self.edges_data.items():
            for target, attrs in targets.items():
                if self.directed or source <= target:  # 避免无向图重复边
                    if data:
                        edges.append((source, target, attrs))
                    else:
                        edges.append((source, target))
        return edges
    
    def neighbors(self, node_id):
        return list(self.edges_data.get(node_id, {}).keys())
    
    def number_of_nodes(self):
        return len(self.nodes_data)
    
    def number_of_edges(self):
        return len(self.edges(data=False))


class BaseManager:
    """基础管理器类"""
    
    def __init__(self):
        self.created_time = datetime.now()
        self.updated_time = datetime.now()
    
    def _generate_id(self, prefix: str = "") -> str:
        return f"{prefix}_{uuid.uuid4().hex[:8]}" if prefix else uuid.uuid4().hex[:8]
    
    def _update_timestamp(self):
        self.updated_time = datetime.now()


class CharacterRelationshipManager(BaseManager):
    """人物关系管理器"""
    
    def __init__(self):
        super().__init__()
        if NETWORKX_AVAILABLE:
            self.graph = nx.Graph()
        else:
            self.graph = SimpleGraph(directed=False)
        self.characters = {}
        
        self.RELATIONSHIP_TYPES = {
            '师徒': {'color': '#FF6B6B', 'strength_range': (7, 10)},
            '朋友': {'color': '#4ECDC4', 'strength_range': (5, 9)},
            '敌人': {'color': '#FF4757', 'strength_range': (3, 8)},
            '恋人': {'color': '#FF3838', 'strength_range': (8, 10)},
            '亲人': {'color': '#FFA502', 'strength_range': (6, 10)},
        }
    
    def add_character(self, character_data: Dict[str, Any]) -> str:
        char_id = character_data.get('id', self._generate_id('char'))
        
        standardized_data = {
            'id': char_id,
            'name': character_data.get('name', '未命名'),
            'prototype': character_data.get('prototype', ''),
            'narrative_role': character_data.get('narrative_role', ''),
            'tags': character_data.get('tags', []),
            'created_time': datetime.now().isoformat(),
        }
        
        self.characters[char_id] = standardized_data
        self.graph.add_node(char_id, **standardized_data)
        self._update_timestamp()
        
        print(f"✅ 添加人物: {standardized_data['name']} (ID: {char_id})")
        return char_id
    
    def add_relationship(self, char1_id: str, char2_id: str, relationship_data: Dict[str, Any]) -> str:
        if not (self.graph.has_node(char1_id) and self.graph.has_node(char2_id)):
            print(f"❌ 人物不存在: {char1_id} 或 {char2_id}")
            return ""
        
        rel_id = relationship_data.get('id', self._generate_id('rel'))
        rel_type = relationship_data.get('relationship_type', '朋友')
        
        standardized_rel = {
            'id': rel_id,
            'relationship_type': rel_type,
            'strength': relationship_data.get('strength', 5),
            'description': relationship_data.get('description', ''),
            'created_time': datetime.now().isoformat(),
        }
        
        self.graph.add_edge(char1_id, char2_id, **standardized_rel)
        self._update_timestamp()
        
        char1_name = self.characters[char1_id]['name']
        char2_name = self.characters[char2_id]['name']
        print(f"✅ 添加关系: {char1_name} ↔ {char2_name} ({rel_type})")
        return rel_id
    
    def get_character(self, char_id: str) -> Optional[Dict[str, Any]]:
        return self.characters.get(char_id)
    
    def get_relationships(self, char_id: str) -> List[Dict[str, Any]]:
        if not self.graph.has_node(char_id):
            return []
        
        relationships = []
        for neighbor in self.graph.neighbors(char_id):
            if NETWORKX_AVAILABLE:
                edge_data = self.graph[char_id][neighbor]
            else:
                edge_data = self.graph.edges_data[char_id][neighbor]
            
            relationships.append({
                'target_id': neighbor,
                'target_name': self.characters[neighbor]['name'],
                **edge_data
            })
        
        return relationships


class EventManager(BaseManager):
    """事件管理器"""
    
    def __init__(self):
        super().__init__()
        if NETWORKX_AVAILABLE:
            self.graph = nx.DiGraph()
        else:
            self.graph = SimpleGraph(directed=True)
        self.events = {}
    
    def add_event(self, event_data: Dict[str, Any]) -> str:
        event_id = event_data.get('id', self._generate_id('event'))
        
        standardized_data = {
            'id': event_id,
            'name': event_data.get('name', '未命名事件'),
            'description': event_data.get('description', ''),
            'timestamp': event_data.get('timestamp', datetime.now().isoformat()),
            'location': event_data.get('location', ''),
            'importance': event_data.get('importance', 5),
            'created_time': datetime.now().isoformat(),
        }
        
        self.events[event_id] = standardized_data
        self.graph.add_node(event_id, **standardized_data)
        self._update_timestamp()
        
        print(f"✅ 添加事件: {standardized_data['name']} (ID: {event_id})")
        return event_id
    
    def get_event(self, event_id: str) -> Optional[Dict[str, Any]]:
        return self.events.get(event_id)


class SimpleTimelineManager(BaseManager):
    """简化的时间线管理器"""
    
    def __init__(self):
        super().__init__()
        self.timeline_entries = []
    
    def add_timeline_entry(self, entry_data: Dict[str, Any]) -> str:
        entry_id = entry_data.get('id', self._generate_id('timeline'))
        
        standardized_entry = {
            'id': entry_id,
            'type': entry_data.get('type', 'event'),
            'name': entry_data.get('name', '未命名条目'),
            'timestamp': entry_data.get('timestamp', datetime.now().isoformat()),
            'description': entry_data.get('description', ''),
            'importance': entry_data.get('importance', 5)
        }
        
        self.timeline_entries.append(standardized_entry)
        self.timeline_entries.sort(key=lambda x: x['timestamp'])
        self._update_timestamp()
        
        print(f"✅ 添加时间线条目: {standardized_entry['name']}")
        return entry_id
    
    def get_timeline_range(self, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        filtered = []
        for entry in self.timeline_entries:
            if start_time <= entry['timestamp'] <= end_time:
                filtered.append(entry)
        return filtered


class NovelMultiGraphSystemTest:
    """小说人物档案管理多图系统 - 测试版"""
    
    def __init__(self):
        print("🚀 初始化小说人物档案管理多图系统 v2.0 (测试版)...")
        
        self.character_manager = CharacterRelationshipManager()
        self.event_manager = EventManager()
        
        if PANDAS_AVAILABLE:
            # 使用完整的时间线管理器（需要pandas）
            from 图v2 import TimelineManager
            self.timeline_manager = TimelineManager()
        else:
            # 使用简化的时间线管理器
            self.timeline_manager = SimpleTimelineManager()
        
        self.system_info = {
            'version': '2.0-test',
            'created_time': datetime.now().isoformat(),
            'networkx_available': NETWORKX_AVAILABLE,
            'pandas_available': PANDAS_AVAILABLE
        }
        
        print("✅ 测试版多图系统初始化完成")
    
    def add_character(self, character_data: Dict[str, Any]) -> str:
        return self.character_manager.add_character(character_data)
    
    def add_event(self, event_data: Dict[str, Any]) -> str:
        event_id = self.event_manager.add_event(event_data)
        
        # 添加到时间线
        timeline_entry = {
            'id': f"timeline_{event_id}",
            'type': 'event',
            'name': event_data.get('name', '未命名事件'),
            'timestamp': event_data.get('timestamp', datetime.now().isoformat()),
            'description': event_data.get('description', ''),
            'importance': event_data.get('importance', 5)
        }
        self.timeline_manager.add_timeline_entry(timeline_entry)
        
        return event_id
    
    def add_character_relationship(self, char1_id: str, char2_id: str, relationship_data: Dict[str, Any]) -> str:
        return self.character_manager.add_relationship(char1_id, char2_id, relationship_data)
    
    def get_character_info(self, character_id: str) -> Dict[str, Any]:
        character_info = self.character_manager.get_character(character_id)
        if not character_info:
            return {}
        
        relationships = self.character_manager.get_relationships(character_id)
        
        return {
            'basic_info': character_info,
            'relationships': relationships
        }
    
    def get_system_statistics(self) -> Dict[str, Any]:
        return {
            'system_info': self.system_info,
            'data_counts': {
                'characters': len(self.character_manager.characters),
                'character_relationships': self.character_manager.graph.number_of_edges(),
                'events': len(self.event_manager.events),
                'timeline_entries': len(getattr(self.timeline_manager, 'timeline_entries', []))
            }
        }
    
    def print_statistics(self):
        stats = self.get_system_statistics()
        
        print("\n" + "="*50)
        print("📊 系统统计信息 (测试版)")
        print("="*50)
        
        print(f"🏷️  系统版本: {stats['system_info']['version']}")
        print(f"📦 NetworkX可用: {'✅' if stats['system_info']['networkx_available'] else '❌'}")
        print(f"📦 Pandas可用: {'✅' if stats['system_info']['pandas_available'] else '❌'}")
        
        counts = stats['data_counts']
        print(f"\n📈 数据统计:")
        print(f"   人物数量: {counts['characters']}")
        print(f"   人物关系: {counts['character_relationships']}")
        print(f"   事件数量: {counts['events']}")
        print(f"   时间线条目: {counts['timeline_entries']}")
        
        print("="*50)


def test_v2_core_functionality():
    """测试v2系统核心功能"""
    print("🧪 开始测试v2系统核心功能")
    print("="*50)
    
    # 创建系统
    system = NovelMultiGraphSystemTest()
    
    # 添加测试数据
    print("\n📝 添加测试数据...")
    
    # 添加人物
    li_feng_id = system.add_character({
        'name': '李风',
        'prototype': '英雄',
        'narrative_role': '主角',
        'tags': ['主角', '剑客']
    })
    
    bai_master_id = system.add_character({
        'name': '白大师',
        'prototype': '导师',
        'narrative_role': '配角',
        'tags': ['导师', '宗师']
    })
    
    # 添加关系
    system.add_character_relationship(li_feng_id, bai_master_id, {
        'relationship_type': '师徒',
        'strength': 9,
        'description': '师父与弟子的关系'
    })
    
    # 添加事件
    system.add_event({
        'name': '初次见面',
        'description': '李风初次见到白大师',
        'timestamp': '2023-05-01T10:00:00',
        'importance': 7
    })
    
    # 显示统计信息
    system.print_statistics()
    
    # 测试查询功能
    print("\n🔍 测试查询功能...")
    
    li_feng_info = system.get_character_info(li_feng_id)
    print(f"\n👤 {li_feng_info['basic_info']['name']} 的信息:")
    print(f"   原型: {li_feng_info['basic_info']['prototype']}")
    print(f"   角色: {li_feng_info['basic_info']['narrative_role']}")
    print(f"   关系数量: {len(li_feng_info['relationships'])}")
    
    for rel in li_feng_info['relationships']:
        print(f"   • 与 {rel['target_name']} 的关系: {rel['relationship_type']} (强度: {rel['strength']})")
    
    print("\n✅ 核心功能测试完成！")
    print("\n🎯 测试结果:")
    print("   ✅ 多图架构基础功能正常")
    print("   ✅ 人物管理功能正常")
    print("   ✅ 事件管理功能正常")
    print("   ✅ 关系管理功能正常")
    print("   ✅ 时间线管理功能正常")
    print("   ✅ 查询功能正常")


if __name__ == "__main__":
    test_v2_core_functionality()
