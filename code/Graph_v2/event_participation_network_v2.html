<html>
    <head>
        <meta charset="utf-8">
        
            <script src="lib/bindings/utils.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 750px;
                 background-color: #222222;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"font": {"color": "white"}, "group": "character", "id": "char_4367c4af", "label": "\u674e\u98ce", "shape": "dot", "size": 30, "title": "\u4eba\u7269: \u674e\u98ce"}, {"font": {"color": "white"}, "group": "character", "id": "char_aff945b6", "label": "\u767d\u5927\u5e08", "shape": "dot", "size": 30, "title": "\u4eba\u7269: \u767d\u5927\u5e08"}, {"font": {"color": "white"}, "group": "character", "id": "char_3013cf8c", "label": "\u5c0f\u96e8", "shape": "dot", "size": 30, "title": "\u4eba\u7269: \u5c0f\u96e8"}, {"font": {"color": "white"}, "group": "character", "id": "char_78a8205b", "label": "\u9ed1\u5f71", "shape": "dot", "size": 30, "title": "\u4eba\u7269: \u9ed1\u5f71"}, {"font": {"color": "white"}, "group": "event", "id": "event_74050fbf", "label": "\u5c71\u6797\u9047\u88ad", "shape": "dot", "size": 25, "title": "\u4e8b\u4ef6: \u5c71\u6797\u9047\u88ad"}, {"font": {"color": "white"}, "group": "event", "id": "event_1228fbaa", "label": "\u5c0f\u96e8\u6551\u6cbb", "shape": "dot", "size": 25, "title": "\u4e8b\u4ef6: \u5c0f\u96e8\u6551\u6cbb"}, {"font": {"color": "white"}, "group": "event", "id": "event_0279e6b9", "label": "\u7279\u8bad\u5f00\u59cb", "shape": "dot", "size": 25, "title": "\u4e8b\u4ef6: \u7279\u8bad\u5f00\u59cb"}, {"font": {"color": "white"}, "group": "event", "id": "event_8df72faf", "label": "\u5251\u5fc3\u7a81\u7834", "shape": "dot", "size": 25, "title": "\u4e8b\u4ef6: \u5251\u5fc3\u7a81\u7834"}]);
                  edges = new vis.DataSet([{"color": "#FF4757", "from": "char_4367c4af", "title": "\u89d2\u8272: \u53d7\u5bb3\u8005", "to": "event_74050fbf", "width": 1.7999999999999998}, {"color": "#FFA502", "from": "char_4367c4af", "title": "\u89d2\u8272: \u53d7\u5f71\u54cd\u8005", "to": "event_1228fbaa", "width": 1.5}, {"color": "#4ECDC4", "from": "char_4367c4af", "title": "\u89d2\u8272: \u53c2\u4e0e\u8005", "to": "event_0279e6b9", "width": 2.1}, {"color": "#FF6B6B", "from": "char_4367c4af", "title": "\u89d2\u8272: \u4e3b\u5bfc\u8005", "to": "event_8df72faf", "width": 3.0}, {"color": "#FF6B6B", "from": "event_74050fbf", "title": "\u89d2\u8272: \u4e3b\u5bfc\u8005", "to": "char_78a8205b", "width": 3.0}, {"color": "#FF6B6B", "from": "event_1228fbaa", "title": "\u89d2\u8272: \u4e3b\u5bfc\u8005", "to": "char_3013cf8c", "width": 3.0}, {"color": "#FF6B6B", "from": "event_0279e6b9", "title": "\u89d2\u8272: \u4e3b\u5bfc\u8005", "to": "char_aff945b6", "width": 3.0}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "barnesHut": {"gravitationalConstant": -8000, "centralGravity": 0.3, "springLength": 150}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>