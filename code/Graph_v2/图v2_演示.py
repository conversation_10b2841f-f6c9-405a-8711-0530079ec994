# -*- coding: utf-8 -*-
"""
小说人物档案管理系统 v2.0 - 功能演示脚本
展示v2系统的主要功能和优势
"""

import sys
import os

# 尝试导入完整版本，如果失败则使用测试版本
try:
    from 图v2 import NovelMultiGraphSystem, create_sample_data_v2
    FULL_VERSION = True
    print("✅ 使用完整版 v2.0 系统")
except ImportError as e:
    print(f"⚠️  完整版导入失败: {e}")
    try:
        from 图v2_test import NovelMultiGraphSystemTest as NovelMultiGraphSystem
        FULL_VERSION = False
        print("✅ 使用测试版 v2.0 系统")
    except ImportError:
        print("❌ 无法导入任何版本的v2系统")
        sys.exit(1)


def demo_basic_functionality():
    """演示基础功能"""
    print("\n" + "="*60)
    print("🎯 基础功能演示")
    print("="*60)
    
    # 创建系统
    system = NovelMultiGraphSystem()
    
    print("\n📝 1. 添加人物...")
    
    # 添加主角
    li_feng_id = system.add_character({
        'name': '李风',
        'prototype': '英雄',
        'narrative_role': '主角',
        'tags': ['主角', '剑客', '成长型']
    })
    
    # 添加导师
    bai_master_id = system.add_character({
        'name': '白大师',
        'prototype': '导师',
        'narrative_role': '配角',
        'tags': ['导师', '宗师', '智者']
    })
    
    # 添加伙伴
    xiao_yu_id = system.add_character({
        'name': '小雨',
        'prototype': '伙伴',
        'narrative_role': '配角',
        'tags': ['伙伴', '医师', '女性']
    })
    
    print("\n🤝 2. 建立人物关系...")
    
    # 师徒关系
    system.add_character_relationship(li_feng_id, bai_master_id, {
        'relationship_type': '师徒',
        'strength': 9,
        'description': '深厚的师父弟子关系'
    })
    
    # 朋友关系
    system.add_character_relationship(li_feng_id, xiao_yu_id, {
        'relationship_type': '朋友',
        'strength': 7,
        'description': '患难与共的朋友关系'
    })
    
    print("\n📅 3. 添加重要事件...")
    
    # 添加关键事件
    attack_event_id = system.add_event({
        'name': '山林遇袭',
        'description': '李风在修炼时遭遇神秘刺客袭击',
        'timestamp': '2023-05-15T14:30:00',
        'location': '青云山',
        'importance': 8
    })
    
    training_event_id = system.add_event({
        'name': '特训开始',
        'description': '白大师决定对李风进行特殊训练',
        'timestamp': '2023-05-20T08:00:00',
        'location': '隐秘道场',
        'importance': 6
    })
    
    if FULL_VERSION:
        print("\n🎯 4. 添加成长里程碑...")
        
        # 添加里程碑
        system.add_milestone({
            'character_id': li_feng_id,
            'name': '危机觉醒',
            'description': '首次遭遇生死危机，深刻认识到实力不足',
            'timestamp': '2023-05-15T15:00:00',
            'milestone_type': 'setback',
            'psychological_change': {
                'before': '自信过度，轻敌',
                'after': '谨慎小心，刻苦训练'
            },
            'importance': 8
        })
        
        print("\n🤝 5. 添加参与关系...")
        
        # 添加参与关系
        system.add_character_event_participation(li_feng_id, attack_event_id, {
            'role': '受害者',
            'impact_description': '身受重伤但侥幸逃生',
            'emotional_impact': 8
        })
        
        system.add_character_event_participation(li_feng_id, training_event_id, {
            'role': '学员',
            'impact_description': '接受严格训练，实力提升',
            'emotional_impact': 6
        })
        
        system.add_character_event_participation(bai_master_id, training_event_id, {
            'role': '导师',
            'impact_description': '传授高级剑术和实战经验',
            'emotional_impact': 4
        })
    
    return system, li_feng_id, bai_master_id, xiao_yu_id


def demo_query_functionality(system, li_feng_id, bai_master_id, xiao_yu_id):
    """演示查询功能"""
    print("\n" + "="*60)
    print("🔍 查询功能演示")
    print("="*60)
    
    print("\n👤 1. 人物信息查询...")
    
    if FULL_VERSION:
        # 获取完整信息
        complete_info = system.get_character_complete_info(li_feng_id)
        basic_info = complete_info['basic_info']
        relationships = complete_info['relationships']
        
        print(f"📖 {basic_info['name']} 的完整信息:")
        print(f"   原型: {basic_info['prototype']}")
        print(f"   角色: {basic_info['narrative_role']}")
        print(f"   标签: {', '.join(basic_info['tags'])}")
        print(f"   关系数量: {len(relationships)}")
        
        for rel in relationships:
            print(f"   • 与 {rel['target_name']} 的关系: {rel['relationship_type']} (强度: {rel['strength']})")
        
        # 分析数据
        analysis = complete_info['analysis']
        print(f"\n📊 分析数据:")
        print(f"   影响力评分: {analysis['centrality']:.3f}")
        
        if 'participation_pattern' in analysis and analysis['participation_pattern']:
            pattern = analysis['participation_pattern']
            print(f"   参与事件数: {pattern['total_participations']}")
            print(f"   最常见角色: {pattern['most_common_role']}")
        
        if 'growth_analysis' in analysis and analysis['growth_analysis']:
            growth = analysis['growth_analysis']
            print(f"   里程碑数量: {growth['total_milestones']}")
            print(f"   平均重要性: {growth['average_importance']:.1f}/10")
    else:
        # 测试版查询
        char_info = system.get_character_info(li_feng_id)
        basic_info = char_info['basic_info']
        relationships = char_info['relationships']
        
        print(f"📖 {basic_info['name']} 的基本信息:")
        print(f"   原型: {basic_info['prototype']}")
        print(f"   角色: {basic_info['narrative_role']}")
        print(f"   关系数量: {len(relationships)}")
        
        for rel in relationships:
            print(f"   • 与 {rel['target_name']} 的关系: {rel['relationship_type']} (强度: {rel['strength']})")
    
    print("\n🔍 2. 搜索功能...")
    
    if FULL_VERSION:
        # 按角色搜索
        protagonists = system.search_characters(narrative_role='主角')
        print(f"主角数量: {len(protagonists)}")
        for char in protagonists:
            print(f"   • {char['name']}")
        
        # 按原型搜索
        heroes = system.search_characters(prototype='英雄')
        print(f"英雄数量: {len(heroes)}")
        
        # 按标签搜索
        sword_users = system.search_characters(tags=['剑客'])
        print(f"剑客数量: {len(sword_users)}")
    
    print("\n⏰ 3. 时间线查询...")
    
    if FULL_VERSION:
        timeline_data = system.query_by_timerange('2023-05-01', '2023-06-30')
        events = timeline_data['events']
        print(f"时间范围内事件数: {len(events)}")
        
        for event in sorted(events, key=lambda x: x['timestamp'])[:3]:
            print(f"   • {event['timestamp'][:10]} - {event['name']} (重要性: {event['importance']}/10)")


def demo_analysis_functionality(system):
    """演示分析功能"""
    print("\n" + "="*60)
    print("📊 分析功能演示")
    print("="*60)
    
    # 显示系统统计
    print("\n📈 系统统计信息:")
    if hasattr(system, 'print_system_statistics'):
        system.print_system_statistics()
    else:
        system.print_statistics()
    
    if FULL_VERSION:
        print("\n🔬 高级分析:")
        
        # 人物影响力分析
        centrality = system.character_manager.analyze_centrality()
        if centrality:
            print("\n👑 人物影响力排名:")
            sorted_chars = sorted(centrality.items(), key=lambda x: x[1], reverse=True)
            for i, (char_id, score) in enumerate(sorted_chars[:3], 1):
                char_name = system.character_manager.characters[char_id]['name']
                print(f"   {i}. {char_name}: {score:.3f}")
        
        # 人物群体分析
        clusters = system.character_manager.get_character_clusters()
        print(f"\n👥 人物群体分析:")
        print(f"   发现 {len(clusters)} 个人物群体")
        for i, cluster in enumerate(clusters, 1):
            cluster_names = [system.character_manager.characters[char_id]['name'] for char_id in cluster]
            print(f"   群体 {i}: {', '.join(cluster_names)}")


def demo_data_management(system):
    """演示数据管理功能"""
    print("\n" + "="*60)
    print("💾 数据管理演示")
    print("="*60)
    
    print("\n📤 数据导出...")
    
    # 导出数据
    export_filename = 'demo_novel_data.json'
    if hasattr(system, 'export_to_json'):
        success = system.export_to_json(export_filename)
        if success:
            print(f"✅ 数据已导出到: {export_filename}")
        else:
            print("❌ 数据导出失败")
    else:
        print("⚠️  当前版本不支持数据导出")
    
    print("\n📊 数据统计:")
    stats = system.get_system_statistics()
    counts = stats['data_counts']
    
    print(f"   人物数量: {counts['characters']}")
    print(f"   人物关系: {counts['character_relationships']}")
    print(f"   事件数量: {counts['events']}")
    
    if 'timeline_entries' in counts:
        print(f"   时间线条目: {counts['timeline_entries']}")
    if 'milestones' in counts:
        print(f"   里程碑数量: {counts['milestones']}")
    if 'participations' in counts:
        print(f"   参与关系: {counts['participations']}")


def main():
    """主演示函数"""
    print("🚀 小说人物档案管理系统 v2.0 功能演示")
    print("="*70)
    
    if FULL_VERSION:
        print("✨ 运行完整版演示")
    else:
        print("⚡ 运行测试版演示")
    
    try:
        # 基础功能演示
        system, li_feng_id, bai_master_id, xiao_yu_id = demo_basic_functionality()
        
        # 查询功能演示
        demo_query_functionality(system, li_feng_id, bai_master_id, xiao_yu_id)
        
        # 分析功能演示
        demo_analysis_functionality(system)
        
        # 数据管理演示
        demo_data_management(system)
        
        print("\n" + "="*70)
        print("🎉 演示完成！")
        print("="*70)
        
        print("\n🌟 v2系统核心优势:")
        print("   ✅ 多图架构 - 分离不同类型的数据管理")
        print("   ✅ 统一接口 - 简化复杂操作")
        print("   ✅ 时间线管理 - 专门的时间序列处理")
        print("   ✅ 高级分析 - 深入的数据洞察")
        print("   ✅ 灵活扩展 - 模块化设计易于扩展")
        
        if FULL_VERSION:
            print("\n💡 下一步建议:")
            print("   • 尝试可视化功能: system.visualize_character_relationships()")
            print("   • 导入更多数据: system.import_from_json('your_data.json')")
            print("   • 探索高级分析: system.milestone_manager.analyze_character_growth()")
            print("   • 查看使用说明: 图v2_使用说明.md")
        else:
            print("\n💡 升级建议:")
            print("   • 安装完整依赖: pip install networkx pandas matplotlib plotly pyvis")
            print("   • 运行完整版本: python 图v2.py")
            print("   • 体验可视化功能和高级分析")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查系统环境和依赖安装")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
