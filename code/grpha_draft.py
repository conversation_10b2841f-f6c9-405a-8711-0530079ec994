# -*- coding: utf-8 -*-
"""
Interactive directed graph with asymmetric edge attributes
Author: you
"""
import networkx as nx
from pyvis.network import Network

# 1. 创建多重有向图，允许平行边
G = nx.MultiDiGraph()

# 2. 添加节点（可带任意属性）
nodes = {
    "A": {"label": "A", "color": "#97c2fc", "title": "Node A<br>type: start"},
    "B": {"label": "B", "color": "#ffaa00", "title": "Node B<br>type: middle"},
    "C": {"label": "C", "color": "#ffaa00", "title": "Node C<br>type: middle"},
    "D": {"label": "D", "color": "#7be19c", "title": "Node D<br>type: end"},
}
for n, attr in nodes.items():
    G.add_node(n, **attr)

# 3. 添加边（A→B 与 B→A 属性不同）
edges = [
    ("A", "B", {"weight": 5, "color": "#848484", "label": "5", "title": "A→B cost=5"}),
    ("B", "A", {"weight": 3, "color": "#ff2e63", "label": "3", "title": "B→A cost=3"}),
    ("B", "C", {"weight": 2, "color": "#848484", "label": "2", "title": "B→C cost=2"}),
    ("C", "B", {"weight": 4, "color": "#ff2e63", "label": "4", "title": "C→B cost=4"}),
    ("C", "D", {"weight": 1, "color": "#848484", "label": "1", "title": "C→D cost=1"}),
    ("D", "C", {"weight": 6, "color": "#ff2e63", "label": "6", "title": "D→C cost=6"}),
]
for u, v, attr in edges:
    G.add_edge(u, v, **attr)

# 4. 转成 pyvis 网络并开启物理拖拽
net = Network(
    height="750px",
    width="100%",
    bgcolor="#222222",
    font_color="white",
    directed=True,       # 显示箭头
    select_menu=True,    # 左上角搜索框
    filter_menu=True,    # 左上角过滤框
)

# 把 nx 图直接搬过去
net.from_nx(G)

# 5. 全局物理引擎参数（可调）
net.set_options("""
var options = {
  "physics": {
    "enabled": true,
    "stabilization": {"iterations": 200},
    "barnesHut": {
      "gravitationalConstant": -8000,
      "centralGravity": 0.3,
      "springLength": 150,
      "springConstant": 0.04
    }
  },
  "edges": {
    "smooth": {
      "type": "curvedCW",
      "roundness": 0.2
    }
  }
}
""")

# 6. 生成文件
net.show("interactive_graph.html", notebook=False)
print("已生成 interactive_graph.html，请用浏览器打开。")