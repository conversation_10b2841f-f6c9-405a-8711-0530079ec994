"""
AI小说管理工具 (AI Novel Management System)
为作家创作过程提供信息管理和决策支持的本地工具

主要功能：
- 人物档案与发展轨迹管理
- 人物间复杂关系网络分析
- 情节事件时间线和逻辑关系
- 世界观设定一致性维护
- 创作过程中的灵感和想法记录
"""

__version__ = "1.0.0"
__author__ = "AI Novel Management Team"
__description__ = "AI小说管理工具 - 为作家创作提供信息管理和决策支持"

# 导入主要类和函数
from .main import NovelManagementSystem, create_system, quick_start
from .mcp_interface import MCPNovelInterface, create_mcp_interface
from .api.novel_api import NovelManagementAPI

# 导入核心模块
from .core.models import (
    Character, Relationship, Event, WorldSetting, CreativeNote,
    RelationshipType, EventType, NoteType, EntityStatus
)

# 导入管理器
from .managers import (
    CharacterManager, RelationshipManager, PlotManager,
    WorldBuildingManager, NotesManager
)

# 导入配置
from .config import get_config

__all__ = [
    # 版本信息
    "__version__",
    "__author__", 
    "__description__",
    
    # 主要类
    "NovelManagementSystem",
    "MCPNovelInterface", 
    "NovelManagementAPI",
    
    # 工厂函数
    "create_system",
    "quick_start",
    "create_mcp_interface",
    
    # 数据模型
    "Character",
    "Relationship", 
    "Event",
    "WorldSetting",
    "CreativeNote",
    "RelationshipType",
    "EventType",
    "NoteType", 
    "EntityStatus",
    
    # 管理器
    "CharacterManager",
    "RelationshipManager",
    "PlotManager", 
    "WorldBuildingManager",
    "NotesManager",
    
    # 配置
    "get_config"
]
