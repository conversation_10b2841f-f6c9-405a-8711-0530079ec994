"""
AI小说管理工具 - 世界观设定管理模块
提供世界观设定的创建、查询、更新和一致性检查功能
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Set, Tuple
from dataclasses import asdict
from collections import defaultdict

from ..core.database import DatabaseManager
from ..core.models import WorldSetting, EntityStatus
from ..core.exceptions import (
    ValidationError, EntityNotFoundError, DuplicateEntityError, 
    DatabaseError, ConsistencyError
)
from ..config import get_config
from ..utils.validators import validate_world_setting_data
from ..utils.helpers import generate_id, calculate_text_similarity, extract_keywords

class WorldBuildingManager:
    """世界观设定管理器"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """初始化世界观管理器"""
        self.db = db_manager or DatabaseManager()
        self.logger = logging.getLogger(__name__)
        self.validation_config = get_config("validation")
        
        # 预定义的设定分类
        self.setting_categories = {
            "geography": "地理环境",
            "history": "历史背景", 
            "culture": "文化社会",
            "magic_system": "魔法体系",
            "technology": "科技水平",
            "politics": "政治制度",
            "economy": "经济体系",
            "religion": "宗教信仰",
            "language": "语言文字",
            "creatures": "生物种族",
            "artifacts": "物品道具",
            "natural_laws": "自然法则"
        }
    
    def create_setting(self, category: str, name: str, description: str = "",
                      rules: List[str] = None, related_settings: List[str] = None,
                      **kwargs) -> str:
        """创建新的世界观设定"""
        try:
            # 验证输入数据
            data = {
                "category": category,
                "name": name,
                "description": description,
                "rules": rules or [],
                "related_settings": related_settings or [],
                **kwargs
            }
            
            errors = validate_world_setting_data(data)
            if errors:
                raise ValidationError("; ".join(errors))
            
            # 检查是否已存在相同名称的设定
            existing = self.search_settings({"name": name.strip(), "category": category})
            if existing:
                raise DuplicateEntityError("WorldSetting", f"{category}/{name}")
            
            # 验证相关设定是否存在
            if related_settings:
                for related_id in related_settings:
                    if not self._setting_exists(related_id):
                        raise EntityNotFoundError("WorldSetting", related_id)
            
            # 创建设定对象
            setting = WorldSetting(
                category=category,
                name=name.strip(),
                description=description,
                rules=rules or [],
                related_settings=related_settings or [],
                **kwargs
            )
            
            # 保存到数据库
            setting_id = self.db.insert_entity("world_settings", setting)
            
            # 更新相关设定的反向引用
            if related_settings:
                self._update_related_settings_references(setting_id, related_settings)
            
            self.logger.info(f"Created world setting: {category}/{name} (ID: {setting_id})")
            return setting_id
            
        except Exception as e:
            self.logger.error(f"Failed to create world setting {category}/{name}: {str(e)}")
            raise
    
    def get_setting(self, setting_id: str) -> Optional[WorldSetting]:
        """根据ID获取世界观设定"""
        try:
            data = self.db.get_entity("world_settings", setting_id)
            if not data:
                return None
            
            # 反序列化JSON字段
            data = self._deserialize_setting_data(data)
            
            return WorldSetting.from_dict(data)
            
        except Exception as e:
            self.logger.error(f"Failed to get world setting {setting_id}: {str(e)}")
            raise DatabaseError(f"Failed to retrieve world setting: {str(e)}")
    
    def update_setting(self, setting_id: str, updates: Dict[str, Any]) -> bool:
        """更新世界观设定"""
        try:
            # 检查设定是否存在
            existing = self.get_setting(setting_id)
            if not existing:
                raise EntityNotFoundError("WorldSetting", setting_id)
            
            # 验证更新数据
            if updates:
                errors = validate_world_setting_data(updates)
                if errors:
                    raise ValidationError("; ".join(errors))
            
            # 检查名称冲突（如果更新了名称）
            if "name" in updates or "category" in updates:
                new_name = updates.get("name", existing.name)
                new_category = updates.get("category", existing.category)
                
                existing_with_name = self.search_settings({"name": new_name, "category": new_category})
                if existing_with_name and existing_with_name[0]["id"] != setting_id:
                    raise DuplicateEntityError("WorldSetting", f"{new_category}/{new_name}")
            
            # 验证相关设定是否存在
            if "related_settings" in updates and updates["related_settings"]:
                for related_id in updates["related_settings"]:
                    if related_id != setting_id and not self._setting_exists(related_id):
                        raise EntityNotFoundError("WorldSetting", related_id)
            
            # 处理版本更新
            if self._is_significant_update(existing, updates):
                updates["version"] = existing.version + 1
            
            # 序列化复杂字段
            serialized_updates = self._serialize_setting_updates(updates)
            
            # 更新数据库
            success = self.db.update_entity("world_settings", setting_id, serialized_updates)
            
            if success:
                self.logger.info(f"Updated world setting {setting_id}")
                
                # 更新相关设定的引用
                if "related_settings" in updates:
                    self._update_related_settings_references(setting_id, updates["related_settings"])
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to update world setting {setting_id}: {str(e)}")
            raise
    
    def delete_setting(self, setting_id: str) -> bool:
        """删除世界观设定（软删除）"""
        try:
            # 检查设定是否存在
            existing = self.get_setting(setting_id)
            if not existing:
                raise EntityNotFoundError("WorldSetting", setting_id)
            
            # 检查是否有其他设定依赖此设定
            dependent_settings = self.get_dependent_settings(setting_id)
            if dependent_settings:
                setting_names = [s["name"] for s in dependent_settings]
                raise ConsistencyError(
                    f"Cannot delete setting: {len(dependent_settings)} settings depend on it",
                    setting_names
                )
            
            # 执行软删除
            success = self.db.delete_entity("world_settings", setting_id)
            
            if success:
                self.logger.info(f"Deleted world setting {setting_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to delete world setting {setting_id}: {str(e)}")
            raise
    
    def search_settings(self, criteria: Dict[str, Any] = None, 
                       limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """搜索世界观设定"""
        try:
            # 默认只查询活跃状态的设定
            search_criteria = {"status": "active"}
            if criteria:
                search_criteria.update(criteria)
            
            results = self.db.search_entities("world_settings", search_criteria, limit, offset)
            
            # 反序列化结果
            for result in results:
                result.update(self._deserialize_setting_data(result))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to search world settings: {str(e)}")
            raise DatabaseError(f"World setting search failed: {str(e)}")
    
    def get_settings_by_category(self, category: str) -> List[Dict[str, Any]]:
        """根据分类获取设定"""
        return self.search_settings({"category": category})
    
    def add_setting_rule(self, setting_id: str, rule: str) -> bool:
        """为设定添加规则"""
        try:
            setting = self.get_setting(setting_id)
            if not setting:
                raise EntityNotFoundError("WorldSetting", setting_id)
            
            # 检查规则是否已存在
            if rule in setting.rules:
                raise ValidationError(f"Rule already exists: {rule}")
            
            # 添加规则
            setting.rules.append(rule)
            
            # 更新数据库
            updates = {"rules": setting.rules}
            return self.update_setting(setting_id, updates)
            
        except Exception as e:
            self.logger.error(f"Failed to add setting rule: {str(e)}")
            raise
    
    def remove_setting_rule(self, setting_id: str, rule: str) -> bool:
        """移除设定规则"""
        try:
            setting = self.get_setting(setting_id)
            if not setting:
                raise EntityNotFoundError("WorldSetting", setting_id)
            
            # 移除规则
            if rule in setting.rules:
                setting.rules.remove(rule)
                
                # 更新数据库
                updates = {"rules": setting.rules}
                return self.update_setting(setting_id, updates)
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to remove setting rule: {str(e)}")
            raise
    
    def add_consistency_note(self, setting_id: str, note: str) -> bool:
        """添加一致性注释"""
        try:
            setting = self.get_setting(setting_id)
            if not setting:
                raise EntityNotFoundError("WorldSetting", setting_id)
            
            # 添加注释
            setting.consistency_notes.append(note)
            
            # 更新数据库
            updates = {"consistency_notes": setting.consistency_notes}
            return self.update_setting(setting_id, updates)
            
        except Exception as e:
            self.logger.error(f"Failed to add consistency note: {str(e)}")
            raise
    
    def check_consistency(self) -> List[Dict[str, Any]]:
        """检查世界观设定的一致性"""
        try:
            consistency_issues = []
            settings = self.search_settings()
            
            # 检查重复设定
            consistency_issues.extend(self._check_duplicate_settings(settings))
            
            # 检查冲突规则
            consistency_issues.extend(self._check_conflicting_rules(settings))
            
            # 检查缺失的相关设定
            consistency_issues.extend(self._check_missing_related_settings(settings))
            
            # 检查循环引用
            consistency_issues.extend(self._check_circular_references(settings))
            
            # 检查设定完整性
            consistency_issues.extend(self._check_setting_completeness(settings))
            
            return consistency_issues
            
        except Exception as e:
            self.logger.error(f"Failed to check consistency: {str(e)}")
            raise DatabaseError(f"Failed to check consistency: {str(e)}")
    
    def get_setting_network(self, setting_id: str, max_depth: int = 3) -> Dict[str, Any]:
        """获取设定关联网络"""
        try:
            network = {
                "nodes": {},  # setting_id -> setting_info
                "edges": [],  # relationship info
                "center": setting_id
            }
            
            visited = set()
            queue = [(setting_id, 0)]  # (setting_id, depth)
            
            while queue:
                current_id, depth = queue.pop(0)
                
                if current_id in visited or depth > max_depth:
                    continue
                
                visited.add(current_id)
                
                # 添加节点信息
                setting = self.get_setting(current_id)
                if setting:
                    network["nodes"][current_id] = {
                        "id": setting.id,
                        "name": setting.name,
                        "category": setting.category,
                        "version": setting.version,
                        "rule_count": len(setting.rules),
                        "depth": depth
                    }
                    
                    # 添加关联关系
                    for related_id in setting.related_settings:
                        if related_id not in visited:
                            queue.append((related_id, depth + 1))
                        
                        # 添加边信息
                        edge_info = {
                            "source": current_id,
                            "target": related_id,
                            "type": "related"
                        }
                        
                        # 避免重复边
                        edge_key = f"{min(current_id, related_id)}-{max(current_id, related_id)}"
                        if not any(e.get("key") == edge_key for e in network["edges"]):
                            edge_info["key"] = edge_key
                            network["edges"].append(edge_info)
            
            return network
            
        except Exception as e:
            self.logger.error(f"Failed to get setting network for {setting_id}: {str(e)}")
            raise DatabaseError(f"Failed to get setting network: {str(e)}")
    
    def get_setting_statistics(self) -> Dict[str, Any]:
        """获取世界观设定统计信息"""
        try:
            stats = {}
            
            # 总设定数
            total_sql = "SELECT COUNT(*) as count FROM world_settings WHERE status = 'active'"
            total_result = self.db.execute_custom_query(total_sql)
            stats["total_settings"] = total_result[0]["count"] if total_result else 0
            
            # 按分类分布
            category_sql = """
                SELECT category, COUNT(*) as count 
                FROM world_settings 
                WHERE status = 'active' 
                GROUP BY category 
                ORDER BY count DESC
            """
            category_result = self.db.execute_custom_query(category_sql)
            stats["category_distribution"] = {
                row["category"]: row["count"] for row in category_result
            }
            
            # 按版本分布
            version_sql = """
                SELECT version, COUNT(*) as count 
                FROM world_settings 
                WHERE status = 'active' 
                GROUP BY version 
                ORDER BY version DESC
            """
            version_result = self.db.execute_custom_query(version_sql)
            stats["version_distribution"] = {
                str(row["version"]): row["count"] for row in version_result
            }
            
            # 规则统计
            settings = self.search_settings()
            rule_counts = [len(s.get("rules", [])) for s in settings]
            stats["rule_statistics"] = {
                "total_rules": sum(rule_counts),
                "average_rules_per_setting": sum(rule_counts) / len(rule_counts) if rule_counts else 0,
                "max_rules_per_setting": max(rule_counts) if rule_counts else 0,
                "settings_without_rules": sum(1 for count in rule_counts if count == 0)
            }
            
            # 关联统计
            related_counts = [len(s.get("related_settings", [])) for s in settings]
            stats["relationship_statistics"] = {
                "total_relationships": sum(related_counts) // 2,  # 每个关系被计算两次
                "average_relationships_per_setting": sum(related_counts) / len(related_counts) if related_counts else 0,
                "isolated_settings": sum(1 for count in related_counts if count == 0)
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get setting statistics: {str(e)}")
            raise DatabaseError(f"Failed to get statistics: {str(e)}")
    
    def get_dependent_settings(self, setting_id: str) -> List[Dict[str, Any]]:
        """获取依赖于指定设定的所有设定"""
        try:
            sql = """
                SELECT * FROM world_settings 
                WHERE status = 'active' AND (
                    related_settings LIKE ? OR related_settings LIKE ? OR related_settings LIKE ?
                )
            """
            
            # 构建搜索模式
            patterns = [
                f'%"{setting_id}"%',  # 在数组中间
                f'["{setting_id}"%',  # 在数组开头
                f'%"{setting_id}"]'   # 在数组结尾
            ]
            
            results = self.db.execute_custom_query(sql, tuple(patterns))
            
            # 反序列化结果并精确过滤
            filtered_results = []
            for result in results:
                result.update(self._deserialize_setting_data(result))
                if setting_id in result.get("related_settings", []):
                    filtered_results.append(result)
            
            return filtered_results
            
        except Exception as e:
            self.logger.error(f"Failed to get dependent settings: {str(e)}")
            raise DatabaseError(f"Failed to get dependent settings: {str(e)}")
    
    def _setting_exists(self, setting_id: str) -> bool:
        """检查设定是否存在"""
        try:
            setting_data = self.db.get_entity("world_settings", setting_id)
            return setting_data is not None and setting_data.get("status") == "active"
        except Exception:
            return False
    
    def _is_significant_update(self, existing: WorldSetting, updates: Dict[str, Any]) -> bool:
        """判断是否为重要更新（需要增加版本号）"""
        significant_fields = ["description", "rules", "related_settings"]
        
        for field in significant_fields:
            if field in updates:
                old_value = getattr(existing, field, None)
                new_value = updates[field]
                
                if old_value != new_value:
                    return True
        
        return False
    
    def _update_related_settings_references(self, setting_id: str, related_settings: List[str]):
        """更新相关设定的反向引用"""
        try:
            for related_id in related_settings:
                related_setting = self.get_setting(related_id)
                if related_setting and setting_id not in related_setting.related_settings:
                    related_setting.related_settings.append(setting_id)
                    updates = {"related_settings": related_setting.related_settings}
                    self.update_setting(related_id, updates)
        except Exception as e:
            self.logger.warning(f"Failed to update related settings references: {str(e)}")
    
    def _check_duplicate_settings(self, settings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检查重复设定"""
        issues = []
        name_category_map = defaultdict(list)
        
        for setting in settings:
            key = (setting["name"].lower(), setting["category"])
            name_category_map[key].append(setting)
        
        for (name, category), setting_list in name_category_map.items():
            if len(setting_list) > 1:
                issues.append({
                    "type": "duplicate_setting",
                    "severity": "warning",
                    "message": f"Duplicate settings found: {name} in {category}",
                    "affected_settings": [s["id"] for s in setting_list]
                })
        
        return issues
    
    def _check_conflicting_rules(self, settings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检查冲突规则"""
        issues = []
        
        # 按分类分组检查
        category_settings = defaultdict(list)
        for setting in settings:
            category_settings[setting["category"]].append(setting)
        
        for category, category_setting_list in category_settings.items():
            # 收集所有规则
            all_rules = []
            for setting in category_setting_list:
                for rule in setting.get("rules", []):
                    all_rules.append((rule, setting["id"], setting["name"]))
            
            # 检查相似或冲突的规则
            for i, (rule1, id1, name1) in enumerate(all_rules):
                for rule2, id2, name2 in all_rules[i+1:]:
                    if id1 != id2:
                        similarity = calculate_text_similarity(rule1, rule2)
                        if similarity > 0.8:  # 高相似度可能是重复
                            issues.append({
                                "type": "similar_rules",
                                "severity": "info",
                                "message": f"Similar rules found in {name1} and {name2}",
                                "rule1": rule1,
                                "rule2": rule2,
                                "similarity": similarity,
                                "affected_settings": [id1, id2]
                            })
                        elif self._rules_conflict(rule1, rule2):
                            issues.append({
                                "type": "conflicting_rules",
                                "severity": "error",
                                "message": f"Conflicting rules between {name1} and {name2}",
                                "rule1": rule1,
                                "rule2": rule2,
                                "affected_settings": [id1, id2]
                            })
        
        return issues
    
    def _check_missing_related_settings(self, settings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检查缺失的相关设定"""
        issues = []
        setting_ids = {s["id"] for s in settings}
        
        for setting in settings:
            for related_id in setting.get("related_settings", []):
                if related_id not in setting_ids:
                    issues.append({
                        "type": "missing_related_setting",
                        "severity": "error",
                        "message": f"Setting '{setting['name']}' references non-existent setting",
                        "setting_id": setting["id"],
                        "missing_related_id": related_id
                    })
        
        return issues
    
    def _check_circular_references(self, settings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检查循环引用"""
        issues = []
        
        def has_circular_reference(setting_id: str, visited: Set[str], path: List[str]) -> Optional[List[str]]:
            if setting_id in visited:
                # 找到循环的起始点
                cycle_start = path.index(setting_id)
                return path[cycle_start:] + [setting_id]
            
            visited.add(setting_id)
            path.append(setting_id)
            
            # 查找当前设定
            current_setting = next((s for s in settings if s["id"] == setting_id), None)
            if current_setting:
                for related_id in current_setting.get("related_settings", []):
                    cycle = has_circular_reference(related_id, visited.copy(), path.copy())
                    if cycle:
                        return cycle
            
            return None
        
        checked = set()
        for setting in settings:
            if setting["id"] not in checked:
                cycle = has_circular_reference(setting["id"], set(), [])
                if cycle:
                    issues.append({
                        "type": "circular_reference",
                        "severity": "warning",
                        "message": f"Circular reference detected in settings",
                        "cycle_path": cycle
                    })
                    checked.update(cycle)
        
        return issues
    
    def _check_setting_completeness(self, settings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检查设定完整性"""
        issues = []
        
        for setting in settings:
            # 检查描述是否为空
            if not setting.get("description", "").strip():
                issues.append({
                    "type": "missing_description",
                    "severity": "warning",
                    "message": f"Setting '{setting['name']}' has no description",
                    "setting_id": setting["id"]
                })
            
            # 检查是否有规则
            if not setting.get("rules"):
                issues.append({
                    "type": "no_rules",
                    "severity": "info",
                    "message": f"Setting '{setting['name']}' has no rules defined",
                    "setting_id": setting["id"]
                })
            
            # 检查孤立设定
            if not setting.get("related_settings"):
                issues.append({
                    "type": "isolated_setting",
                    "severity": "info",
                    "message": f"Setting '{setting['name']}' has no related settings",
                    "setting_id": setting["id"]
                })
        
        return issues
    
    def _rules_conflict(self, rule1: str, rule2: str) -> bool:
        """简单的规则冲突检测"""
        # 这里可以实现更复杂的冲突检测逻辑
        # 目前只是简单的关键词检测
        
        conflict_patterns = [
            ("cannot", "can"),
            ("impossible", "possible"),
            ("never", "always"),
            ("forbidden", "allowed"),
            ("无法", "可以"),
            ("不能", "能够"),
            ("禁止", "允许")
        ]
        
        rule1_lower = rule1.lower()
        rule2_lower = rule2.lower()
        
        for negative, positive in conflict_patterns:
            if negative in rule1_lower and positive in rule2_lower:
                return True
            if positive in rule1_lower and negative in rule2_lower:
                return True
        
        return False
    
    def _serialize_setting_updates(self, updates: Dict[str, Any]) -> Dict[str, Any]:
        """序列化设定更新数据中的复杂字段"""
        serialized = updates.copy()
        
        # 处理需要序列化的字段
        json_fields = ["rules", "related_settings", "consistency_notes", "tags"]
        
        for field in json_fields:
            if field in serialized:
                serialized[field] = self.db._serialize_json_field(serialized[field])
        
        return serialized
    
    def _deserialize_setting_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """反序列化设定数据中的JSON字段"""
        deserialized = data.copy()
        
        # 处理需要反序列化的字段
        json_fields = ["rules", "related_settings", "consistency_notes", "tags"]
        
        for field in json_fields:
            if field in deserialized and deserialized[field]:
                deserialized[field] = self.db._deserialize_json_field(deserialized[field])
        
        return deserialized
