"""
AI小说管理工具 - 关系网络管理模块
提供人物关系的创建、查询、更新和网络分析功能
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import asdict
from collections import defaultdict, deque

from ..core.database import DatabaseManager
from ..core.models import Relationship, RelationshipHistory, RelationshipType, EntityStatus
from ..core.exceptions import (
    ValidationError, EntityNotFoundError, DuplicateEntityError, 
    DatabaseError, RelationshipError
)
from ..config import get_config
from ..utils.validators import validate_relationship_data
from ..utils.helpers import generate_id

class RelationshipManager:
    """关系网络管理器"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """初始化关系管理器"""
        self.db = db_manager or DatabaseManager()
        self.logger = logging.getLogger(__name__)
        self.validation_config = get_config("validation")
    
    def create_relationship(self, character_a: str, character_b: str, 
                          relationship_type: str, strength: float = 0.5,
                          description: str = "", is_mutual: bool = True, **kwargs) -> str:
        """创建人物关系"""
        try:
            # 验证输入数据
            data = {
                "character_a": character_a,
                "character_b": character_b,
                "relationship_type": relationship_type,
                "strength": strength,
                "description": description,
                "is_mutual": is_mutual,
                **kwargs
            }
            
            errors = validate_relationship_data(data)
            if errors:
                raise ValidationError("; ".join(errors))
            
            # 检查人物是否存在
            if not self._character_exists(character_a):
                raise EntityNotFoundError("Character", character_a)
            if not self._character_exists(character_b):
                raise EntityNotFoundError("Character", character_b)
            
            # 检查是否已存在相同关系
            existing = self.get_relationship_between(character_a, character_b, relationship_type)
            if existing:
                raise DuplicateEntityError("Relationship", f"{character_a}-{character_b}-{relationship_type}")
            
            # 创建关系对象
            relationship = Relationship(
                character_a=character_a,
                character_b=character_b,
                relationship_type=RelationshipType(relationship_type),
                strength=strength,
                description=description,
                is_mutual=is_mutual,
                **kwargs
            )
            
            # 保存到数据库
            relationship_id = self.db.insert_entity("relationships", relationship)
            
            self.logger.info(f"Created relationship: {character_a} -> {character_b} ({relationship_type})")
            return relationship_id
            
        except Exception as e:
            self.logger.error(f"Failed to create relationship: {str(e)}")
            raise
    
    def get_relationship(self, relationship_id: str) -> Optional[Relationship]:
        """根据ID获取关系"""
        try:
            data = self.db.get_entity("relationships", relationship_id)
            if not data:
                return None
            
            # 反序列化JSON字段
            data = self._deserialize_relationship_data(data)
            
            return Relationship.from_dict(data)
            
        except Exception as e:
            self.logger.error(f"Failed to get relationship {relationship_id}: {str(e)}")
            raise DatabaseError(f"Failed to retrieve relationship: {str(e)}")
    
    def get_relationship_between(self, character_a: str, character_b: str, 
                               relationship_type: str = None) -> Optional[Dict[str, Any]]:
        """获取两个人物之间的关系"""
        try:
            conditions = {
                "character_a": character_a,
                "character_b": character_b,
                "status": "active"
            }
            
            if relationship_type:
                conditions["relationship_type"] = relationship_type
            
            results = self.db.search_entities("relationships", conditions, limit=1)
            
            if results:
                result = results[0]
                result.update(self._deserialize_relationship_data(result))
                return result
            
            # 如果是双向关系，也检查反向
            conditions_reverse = {
                "character_a": character_b,
                "character_b": character_a,
                "status": "active"
            }
            
            if relationship_type:
                conditions_reverse["relationship_type"] = relationship_type
            
            results_reverse = self.db.search_entities("relationships", conditions_reverse, limit=1)
            
            if results_reverse:
                result = results_reverse[0]
                result.update(self._deserialize_relationship_data(result))
                return result
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get relationship between {character_a} and {character_b}: {str(e)}")
            raise DatabaseError(f"Failed to get relationship: {str(e)}")
    
    def get_character_relationships(self, character_id: str, 
                                  relationship_type: str = None,
                                  include_reverse: bool = True) -> List[Dict[str, Any]]:
        """获取某个人物的所有关系"""
        try:
            relationships = []
            
            # 获取作为character_a的关系
            conditions_a = {"character_a": character_id, "status": "active"}
            if relationship_type:
                conditions_a["relationship_type"] = relationship_type
            
            results_a = self.db.search_entities("relationships", conditions_a)
            for result in results_a:
                result.update(self._deserialize_relationship_data(result))
                result["direction"] = "outgoing"
                relationships.append(result)
            
            # 获取作为character_b的关系（如果包含反向）
            if include_reverse:
                conditions_b = {"character_b": character_id, "status": "active"}
                if relationship_type:
                    conditions_b["relationship_type"] = relationship_type
                
                results_b = self.db.search_entities("relationships", conditions_b)
                for result in results_b:
                    result.update(self._deserialize_relationship_data(result))
                    result["direction"] = "incoming"
                    relationships.append(result)
            
            return relationships
            
        except Exception as e:
            self.logger.error(f"Failed to get relationships for character {character_id}: {str(e)}")
            raise DatabaseError(f"Failed to get character relationships: {str(e)}")
    
    def update_relationship(self, relationship_id: str, updates: Dict[str, Any]) -> bool:
        """更新关系信息"""
        try:
            # 检查关系是否存在
            existing = self.get_relationship(relationship_id)
            if not existing:
                raise EntityNotFoundError("Relationship", relationship_id)
            
            # 验证更新数据
            if updates:
                errors = validate_relationship_data(updates)
                if errors:
                    raise ValidationError("; ".join(errors))
            
            # 序列化复杂字段
            serialized_updates = self._serialize_relationship_updates(updates)
            
            # 更新数据库
            success = self.db.update_entity("relationships", relationship_id, serialized_updates)
            
            if success:
                self.logger.info(f"Updated relationship {relationship_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to update relationship {relationship_id}: {str(e)}")
            raise
    
    def delete_relationship(self, relationship_id: str) -> bool:
        """删除关系（软删除）"""
        try:
            # 检查关系是否存在
            existing = self.get_relationship(relationship_id)
            if not existing:
                raise EntityNotFoundError("Relationship", relationship_id)
            
            # 执行软删除
            success = self.db.delete_entity("relationships", relationship_id)
            
            if success:
                self.logger.info(f"Deleted relationship {relationship_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to delete relationship {relationship_id}: {str(e)}")
            raise
    
    def add_relationship_history(self, relationship_id: str, event: str, 
                               impact: str, timestamp: datetime = None) -> bool:
        """为关系添加历史记录"""
        try:
            relationship = self.get_relationship(relationship_id)
            if not relationship:
                raise EntityNotFoundError("Relationship", relationship_id)
            
            # 创建历史记录
            history_entry = RelationshipHistory(
                event=event,
                impact=impact,
                timestamp=timestamp or datetime.now()
            )
            
            # 添加到历史记录
            relationship.history.append(history_entry)
            
            # 更新数据库
            updates = {"history": [asdict(h) for h in relationship.history]}
            return self.update_relationship(relationship_id, updates)
            
        except Exception as e:
            self.logger.error(f"Failed to add relationship history: {str(e)}")
            raise
    
    def get_relationship_network(self, character_id: str, max_depth: int = 2) -> Dict[str, Any]:
        """获取人物关系网络"""
        try:
            network = {
                "nodes": {},  # character_id -> character_info
                "edges": [],  # relationship info
                "center": character_id
            }
            
            visited = set()
            queue = deque([(character_id, 0)])  # (character_id, depth)
            
            while queue and len(visited) < 100:  # 限制网络大小
                current_char, depth = queue.popleft()
                
                if current_char in visited or depth > max_depth:
                    continue
                
                visited.add(current_char)
                
                # 添加节点信息
                if current_char not in network["nodes"]:
                    char_info = self._get_character_basic_info(current_char)
                    if char_info:
                        network["nodes"][current_char] = char_info
                
                # 获取关系
                relationships = self.get_character_relationships(current_char)
                
                for rel in relationships:
                    # 确定关系的另一端
                    other_char = rel["character_b"] if rel["character_a"] == current_char else rel["character_a"]
                    
                    # 添加边信息
                    edge_info = {
                        "id": rel["id"],
                        "source": rel["character_a"],
                        "target": rel["character_b"],
                        "type": rel["relationship_type"],
                        "strength": rel["strength"],
                        "description": rel["description"],
                        "is_mutual": rel["is_mutual"]
                    }
                    
                    # 避免重复边
                    edge_key = f"{min(rel['character_a'], rel['character_b'])}-{max(rel['character_a'], rel['character_b'])}-{rel['relationship_type']}"
                    if not any(e.get("key") == edge_key for e in network["edges"]):
                        edge_info["key"] = edge_key
                        network["edges"].append(edge_info)
                    
                    # 将相关人物加入队列
                    if other_char not in visited and depth < max_depth:
                        queue.append((other_char, depth + 1))
            
            return network
            
        except Exception as e:
            self.logger.error(f"Failed to get relationship network for {character_id}: {str(e)}")
            raise DatabaseError(f"Failed to get relationship network: {str(e)}")
    
    def find_relationship_path(self, start_character: str, end_character: str, 
                             max_depth: int = 6) -> List[Dict[str, Any]]:
        """查找两个人物之间的关系路径"""
        try:
            if start_character == end_character:
                return []
            
            # 使用BFS查找最短路径
            queue = deque([(start_character, [])])
            visited = set([start_character])
            
            while queue:
                current_char, path = queue.popleft()
                
                if len(path) >= max_depth:
                    continue
                
                # 获取当前人物的所有关系
                relationships = self.get_character_relationships(current_char)
                
                for rel in relationships:
                    # 确定关系的另一端
                    other_char = rel["character_b"] if rel["character_a"] == current_char else rel["character_a"]
                    
                    if other_char == end_character:
                        # 找到目标，返回路径
                        return path + [rel]
                    
                    if other_char not in visited:
                        visited.add(other_char)
                        queue.append((other_char, path + [rel]))
            
            return []  # 未找到路径
            
        except Exception as e:
            self.logger.error(f"Failed to find relationship path: {str(e)}")
            raise DatabaseError(f"Failed to find relationship path: {str(e)}")
    
    def get_relationship_statistics(self) -> Dict[str, Any]:
        """获取关系统计信息"""
        try:
            stats = {}
            
            # 总关系数
            total_sql = "SELECT COUNT(*) as count FROM relationships WHERE status = 'active'"
            total_result = self.db.execute_custom_query(total_sql)
            stats["total_relationships"] = total_result[0]["count"] if total_result else 0
            
            # 按关系类型分布
            type_sql = """
                SELECT relationship_type, COUNT(*) as count 
                FROM relationships 
                WHERE status = 'active' 
                GROUP BY relationship_type 
                ORDER BY count DESC
            """
            type_result = self.db.execute_custom_query(type_sql)
            stats["type_distribution"] = {
                row["relationship_type"]: row["count"] for row in type_result
            }
            
            # 按强度分布
            strength_sql = """
                SELECT 
                    CASE 
                        WHEN strength < 0.2 THEN 'very_weak'
                        WHEN strength < 0.4 THEN 'weak'
                        WHEN strength < 0.6 THEN 'moderate'
                        WHEN strength < 0.8 THEN 'strong'
                        ELSE 'very_strong'
                    END as strength_level,
                    COUNT(*) as count
                FROM relationships 
                WHERE status = 'active'
                GROUP BY strength_level
            """
            strength_result = self.db.execute_custom_query(strength_sql)
            stats["strength_distribution"] = {
                row["strength_level"]: row["count"] for row in strength_result
            }
            
            # 最活跃的人物（关系数最多）
            active_sql = """
                SELECT character_id, relationship_count FROM (
                    SELECT character_a as character_id, COUNT(*) as relationship_count
                    FROM relationships WHERE status = 'active'
                    GROUP BY character_a
                    UNION ALL
                    SELECT character_b as character_id, COUNT(*) as relationship_count
                    FROM relationships WHERE status = 'active'
                    GROUP BY character_b
                ) 
                GROUP BY character_id
                ORDER BY SUM(relationship_count) DESC
                LIMIT 10
            """
            active_result = self.db.execute_custom_query(active_sql)
            stats["most_connected_characters"] = [
                {"character_id": row["character_id"], "connection_count": row["relationship_count"]}
                for row in active_result
            ]
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get relationship statistics: {str(e)}")
            raise DatabaseError(f"Failed to get statistics: {str(e)}")
    
    def _character_exists(self, character_id: str) -> bool:
        """检查人物是否存在"""
        try:
            char_data = self.db.get_entity("characters", character_id)
            return char_data is not None and char_data.get("status") == "active"
        except Exception:
            return False
    
    def _get_character_basic_info(self, character_id: str) -> Optional[Dict[str, Any]]:
        """获取人物基本信息"""
        try:
            char_data = self.db.get_entity("characters", character_id)
            if char_data:
                return {
                    "id": char_data["id"],
                    "name": char_data["name"],
                    "importance": char_data.get("importance", 5),
                    "gender": char_data.get("gender", ""),
                    "occupation": char_data.get("occupation", "")
                }
            return None
        except Exception:
            return None
    
    def _serialize_relationship_updates(self, updates: Dict[str, Any]) -> Dict[str, Any]:
        """序列化关系更新数据中的复杂字段"""
        serialized = updates.copy()
        
        # 处理需要序列化的字段
        json_fields = ["history", "tags"]
        
        for field in json_fields:
            if field in serialized:
                serialized[field] = self.db._serialize_json_field(serialized[field])
        
        return serialized
    
    def _deserialize_relationship_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """反序列化关系数据中的JSON字段"""
        deserialized = data.copy()
        
        # 处理需要反序列化的字段
        json_fields = ["history", "tags"]
        
        for field in json_fields:
            if field in deserialized and deserialized[field]:
                deserialized[field] = self.db._deserialize_json_field(deserialized[field])
        
        return deserialized
