"""
AI小说管理工具 - 情节事件管理模块
提供事件的创建、查询、更新和时间线分析功能
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import asdict
from collections import defaultdict, deque

from ..core.database import DatabaseManager
from ..core.models import Event, EventType, EntityStatus
from ..core.exceptions import (
    ValidationError, EntityNotFoundError, DuplicateEntityError, 
    DatabaseError, ConsistencyError
)
from ..config import get_config
from ..utils.validators import validate_event_data
from ..utils.helpers import generate_id, parse_datetime, format_datetime

class PlotManager:
    """情节事件管理器"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """初始化情节管理器"""
        self.db = db_manager or DatabaseManager()
        self.logger = logging.getLogger(__name__)
        self.validation_config = get_config("validation")
    
    def create_event(self, title: str, description: str = "", event_type: str = "plot",
                    story_timestamp: datetime = None, location: str = "",
                    participants: List[str] = None, importance: int = 5, **kwargs) -> str:
        """创建新事件"""
        try:
            # 验证输入数据
            data = {
                "title": title,
                "description": description,
                "event_type": event_type,
                "story_timestamp": story_timestamp,
                "location": location,
                "participants": participants or [],
                "importance": importance,
                **kwargs
            }
            
            errors = validate_event_data(data)
            if errors:
                raise ValidationError("; ".join(errors))
            
            # 检查是否已存在相同标题的事件
            existing = self.search_events({"title": title.strip()})
            if existing:
                raise DuplicateEntityError("Event", title)
            
            # 验证参与者是否存在
            if participants:
                for participant_id in participants:
                    if not self._character_exists(participant_id):
                        raise EntityNotFoundError("Character", participant_id)
            
            # 创建事件对象
            event = Event(
                title=title.strip(),
                description=description,
                event_type=EventType(event_type),
                story_timestamp=story_timestamp,
                location=location,
                participants=participants or [],
                importance=importance,
                **kwargs
            )
            
            # 保存到数据库
            event_id = self.db.insert_entity("events", event)
            
            self.logger.info(f"Created event: {title} (ID: {event_id})")
            return event_id
            
        except Exception as e:
            self.logger.error(f"Failed to create event {title}: {str(e)}")
            raise
    
    def get_event(self, event_id: str) -> Optional[Event]:
        """根据ID获取事件"""
        try:
            data = self.db.get_entity("events", event_id)
            if not data:
                return None
            
            # 反序列化JSON字段
            data = self._deserialize_event_data(data)
            
            return Event.from_dict(data)
            
        except Exception as e:
            self.logger.error(f"Failed to get event {event_id}: {str(e)}")
            raise DatabaseError(f"Failed to retrieve event: {str(e)}")
    
    def update_event(self, event_id: str, updates: Dict[str, Any]) -> bool:
        """更新事件信息"""
        try:
            # 检查事件是否存在
            existing = self.get_event(event_id)
            if not existing:
                raise EntityNotFoundError("Event", event_id)
            
            # 验证更新数据
            if updates:
                errors = validate_event_data(updates)
                if errors:
                    raise ValidationError("; ".join(errors))
            
            # 验证参与者是否存在
            if "participants" in updates and updates["participants"]:
                for participant_id in updates["participants"]:
                    if not self._character_exists(participant_id):
                        raise EntityNotFoundError("Character", participant_id)
            
            # 序列化复杂字段
            serialized_updates = self._serialize_event_updates(updates)
            
            # 更新数据库
            success = self.db.update_entity("events", event_id, serialized_updates)
            
            if success:
                self.logger.info(f"Updated event {event_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to update event {event_id}: {str(e)}")
            raise
    
    def delete_event(self, event_id: str) -> bool:
        """删除事件（软删除）"""
        try:
            # 检查事件是否存在
            existing = self.get_event(event_id)
            if not existing:
                raise EntityNotFoundError("Event", event_id)
            
            # 检查是否有其他事件依赖此事件
            dependent_events = self.get_dependent_events(event_id)
            if dependent_events:
                event_titles = [e["title"] for e in dependent_events]
                raise ConsistencyError(
                    f"Cannot delete event: {len(dependent_events)} events depend on it",
                    event_titles
                )
            
            # 执行软删除
            success = self.db.delete_entity("events", event_id)
            
            if success:
                self.logger.info(f"Deleted event {event_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to delete event {event_id}: {str(e)}")
            raise
    
    def search_events(self, criteria: Dict[str, Any] = None, 
                     limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """搜索事件"""
        try:
            # 默认只查询活跃状态的事件
            search_criteria = {"status": "active"}
            if criteria:
                search_criteria.update(criteria)
            
            results = self.db.search_entities("events", search_criteria, limit, offset)
            
            # 反序列化结果
            for result in results:
                result.update(self._deserialize_event_data(result))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to search events: {str(e)}")
            raise DatabaseError(f"Event search failed: {str(e)}")
    
    def get_timeline(self, start_time: datetime = None, end_time: datetime = None,
                    event_types: List[str] = None, participants: List[str] = None) -> List[Dict[str, Any]]:
        """获取时间线事件"""
        try:
            conditions = ["status = 'active'", "story_timestamp IS NOT NULL"]
            params = []
            
            if start_time:
                conditions.append("story_timestamp >= ?")
                params.append(start_time.isoformat())
            
            if end_time:
                conditions.append("story_timestamp <= ?")
                params.append(end_time.isoformat())
            
            if event_types:
                type_placeholders = ",".join(["?" for _ in event_types])
                conditions.append(f"event_type IN ({type_placeholders})")
                params.extend(event_types)
            
            sql = f"""
                SELECT * FROM events 
                WHERE {" AND ".join(conditions)}
                ORDER BY story_timestamp ASC
            """
            
            results = self.db.execute_custom_query(sql, tuple(params))
            
            # 反序列化结果
            for result in results:
                result.update(self._deserialize_event_data(result))
            
            # 过滤参与者
            if participants:
                filtered_results = []
                for result in results:
                    event_participants = result.get("participants", [])
                    if any(p in event_participants for p in participants):
                        filtered_results.append(result)
                results = filtered_results
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to get timeline: {str(e)}")
            raise DatabaseError(f"Failed to get timeline: {str(e)}")
    
    def add_causal_relationship(self, cause_event_id: str, effect_event_id: str) -> bool:
        """添加因果关系"""
        try:
            # 检查事件是否存在
            cause_event = self.get_event(cause_event_id)
            effect_event = self.get_event(effect_event_id)
            
            if not cause_event:
                raise EntityNotFoundError("Event", cause_event_id)
            if not effect_event:
                raise EntityNotFoundError("Event", effect_event_id)
            
            # 检查是否会形成循环依赖
            if self._would_create_cycle(cause_event_id, effect_event_id):
                raise ConsistencyError("Adding this causal relationship would create a cycle")
            
            # 为结果事件添加原因
            if cause_event_id not in effect_event.causes:
                effect_event.causes.append(cause_event_id)
                updates = {"causes": effect_event.causes}
                self.update_event(effect_event_id, updates)
            
            # 为原因事件添加结果
            if effect_event_id not in cause_event.consequences:
                cause_event.consequences.append(effect_event_id)
                updates = {"consequences": cause_event.consequences}
                self.update_event(cause_event_id, updates)
            
            self.logger.info(f"Added causal relationship: {cause_event_id} -> {effect_event_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add causal relationship: {str(e)}")
            raise
    
    def remove_causal_relationship(self, cause_event_id: str, effect_event_id: str) -> bool:
        """移除因果关系"""
        try:
            # 检查事件是否存在
            cause_event = self.get_event(cause_event_id)
            effect_event = self.get_event(effect_event_id)
            
            if not cause_event or not effect_event:
                return False
            
            # 从结果事件移除原因
            if cause_event_id in effect_event.causes:
                effect_event.causes.remove(cause_event_id)
                updates = {"causes": effect_event.causes}
                self.update_event(effect_event_id, updates)
            
            # 从原因事件移除结果
            if effect_event_id in cause_event.consequences:
                cause_event.consequences.remove(effect_event_id)
                updates = {"consequences": cause_event.consequences}
                self.update_event(cause_event_id, updates)
            
            self.logger.info(f"Removed causal relationship: {cause_event_id} -> {effect_event_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to remove causal relationship: {str(e)}")
            raise
    
    def get_event_chain(self, event_id: str, direction: str = "both", max_depth: int = 5) -> Dict[str, Any]:
        """获取事件链（因果关系链）"""
        try:
            event = self.get_event(event_id)
            if not event:
                raise EntityNotFoundError("Event", event_id)
            
            chain = {
                "center_event": asdict(event),
                "causes": [],
                "consequences": [],
                "full_chain": []
            }
            
            # 获取原因链
            if direction in ["both", "causes"]:
                chain["causes"] = self._get_causal_chain(event_id, "causes", max_depth)
            
            # 获取结果链
            if direction in ["both", "consequences"]:
                chain["consequences"] = self._get_causal_chain(event_id, "consequences", max_depth)
            
            # 构建完整链条
            chain["full_chain"] = self._build_full_chain(chain["causes"], [asdict(event)], chain["consequences"])
            
            return chain
            
        except Exception as e:
            self.logger.error(f"Failed to get event chain for {event_id}: {str(e)}")
            raise DatabaseError(f"Failed to get event chain: {str(e)}")
    
    def analyze_plot_structure(self) -> Dict[str, Any]:
        """分析情节结构"""
        try:
            analysis = {
                "basic_metrics": self._calculate_plot_metrics(),
                "timeline_analysis": self._analyze_timeline(),
                "causal_network": self._analyze_causal_network(),
                "character_involvement": self._analyze_character_involvement(),
                "plot_holes": self._detect_plot_holes()
            }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Failed to analyze plot structure: {str(e)}")
            raise DatabaseError(f"Failed to analyze plot structure: {str(e)}")
    
    def get_dependent_events(self, event_id: str) -> List[Dict[str, Any]]:
        """获取依赖于指定事件的所有事件"""
        try:
            sql = """
                SELECT * FROM events 
                WHERE status = 'active' AND (
                    causes LIKE ? OR causes LIKE ? OR causes LIKE ?
                )
            """
            
            # 构建搜索模式
            patterns = [
                f'%"{event_id}"%',  # 在数组中间
                f'["{event_id}"%',  # 在数组开头
                f'%"{event_id}"]'   # 在数组结尾
            ]
            
            results = self.db.execute_custom_query(sql, tuple(patterns))
            
            # 反序列化结果并精确过滤
            filtered_results = []
            for result in results:
                result.update(self._deserialize_event_data(result))
                if event_id in result.get("causes", []):
                    filtered_results.append(result)
            
            return filtered_results
            
        except Exception as e:
            self.logger.error(f"Failed to get dependent events: {str(e)}")
            raise DatabaseError(f"Failed to get dependent events: {str(e)}")
    
    def _character_exists(self, character_id: str) -> bool:
        """检查人物是否存在"""
        try:
            char_data = self.db.get_entity("characters", character_id)
            return char_data is not None and char_data.get("status") == "active"
        except Exception:
            return False
    
    def _would_create_cycle(self, cause_event_id: str, effect_event_id: str) -> bool:
        """检查添加因果关系是否会创建循环依赖"""
        try:
            # 使用DFS检查从effect_event_id是否能到达cause_event_id
            visited = set()
            stack = [effect_event_id]
            
            while stack:
                current_id = stack.pop()
                if current_id == cause_event_id:
                    return True
                
                if current_id in visited:
                    continue
                
                visited.add(current_id)
                
                # 获取当前事件的所有结果
                current_event = self.get_event(current_id)
                if current_event:
                    for consequence_id in current_event.consequences:
                        if consequence_id not in visited:
                            stack.append(consequence_id)
            
            return False
            
        except Exception:
            return True  # 出错时保守处理，认为会创建循环
    
    def _get_causal_chain(self, event_id: str, direction: str, max_depth: int, 
                         visited: Set[str] = None) -> List[Dict[str, Any]]:
        """递归获取因果链"""
        if visited is None:
            visited = set()
        
        if event_id in visited or max_depth <= 0:
            return []
        
        visited.add(event_id)
        chain = []
        
        event = self.get_event(event_id)
        if not event:
            return []
        
        related_ids = event.causes if direction == "causes" else event.consequences
        
        for related_id in related_ids:
            related_event = self.get_event(related_id)
            if related_event:
                event_dict = asdict(related_event)
                event_dict["depth"] = max_depth
                chain.append(event_dict)
                
                # 递归获取更深层的关系
                deeper_chain = self._get_causal_chain(related_id, direction, max_depth - 1, visited.copy())
                chain.extend(deeper_chain)
        
        return chain
    
    def _build_full_chain(self, causes: List[Dict[str, Any]], center: List[Dict[str, Any]], 
                         consequences: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """构建完整的事件链"""
        # 按时间排序原因事件
        sorted_causes = sorted(causes, key=lambda x: x.get("story_timestamp", ""), reverse=True)
        
        # 按时间排序结果事件
        sorted_consequences = sorted(consequences, key=lambda x: x.get("story_timestamp", ""))
        
        return sorted_causes + center + sorted_consequences
    
    def _calculate_plot_metrics(self) -> Dict[str, Any]:
        """计算情节基本指标"""
        try:
            # 总事件数
            total_sql = "SELECT COUNT(*) as count FROM events WHERE status = 'active'"
            total_result = self.db.execute_custom_query(total_sql)
            total_events = total_result[0]["count"] if total_result else 0
            
            # 按类型分布
            type_sql = """
                SELECT event_type, COUNT(*) as count 
                FROM events 
                WHERE status = 'active' 
                GROUP BY event_type 
                ORDER BY count DESC
            """
            type_result = self.db.execute_custom_query(type_sql)
            type_distribution = {row["event_type"]: row["count"] for row in type_result}
            
            # 按重要性分布
            importance_sql = """
                SELECT importance, COUNT(*) as count 
                FROM events 
                WHERE status = 'active' 
                GROUP BY importance 
                ORDER BY importance DESC
            """
            importance_result = self.db.execute_custom_query(importance_sql)
            importance_distribution = {str(row["importance"]): row["count"] for row in importance_result}
            
            # 有时间戳的事件数
            timestamped_sql = "SELECT COUNT(*) as count FROM events WHERE status = 'active' AND story_timestamp IS NOT NULL"
            timestamped_result = self.db.execute_custom_query(timestamped_sql)
            timestamped_events = timestamped_result[0]["count"] if timestamped_result else 0
            
            return {
                "total_events": total_events,
                "type_distribution": type_distribution,
                "importance_distribution": importance_distribution,
                "timestamped_events": timestamped_events,
                "timeline_coverage": timestamped_events / total_events if total_events > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"Failed to calculate plot metrics: {str(e)}")
            return {}
    
    def _analyze_timeline(self) -> Dict[str, Any]:
        """分析时间线"""
        try:
            timeline_events = self.get_timeline()
            
            if not timeline_events:
                return {"message": "No timestamped events found"}
            
            # 时间跨度
            timestamps = [parse_datetime(e["story_timestamp"]) for e in timeline_events if e.get("story_timestamp")]
            timestamps = [t for t in timestamps if t is not None]
            
            if timestamps:
                time_span = max(timestamps) - min(timestamps)
                avg_interval = time_span / (len(timestamps) - 1) if len(timestamps) > 1 else timedelta(0)
            else:
                time_span = timedelta(0)
                avg_interval = timedelta(0)
            
            # 事件密度分析
            density_analysis = self._analyze_event_density(timeline_events)
            
            return {
                "total_timeline_events": len(timeline_events),
                "time_span_days": time_span.days,
                "average_interval_hours": avg_interval.total_seconds() / 3600,
                "density_analysis": density_analysis,
                "timeline_gaps": self._find_timeline_gaps(timeline_events)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to analyze timeline: {str(e)}")
            return {}
    
    def _analyze_causal_network(self) -> Dict[str, Any]:
        """分析因果网络"""
        try:
            events = self.search_events()
            
            total_causal_links = 0
            events_with_causes = 0
            events_with_consequences = 0
            max_causes = 0
            max_consequences = 0
            
            for event in events:
                causes = event.get("causes", [])
                consequences = event.get("consequences", [])
                
                total_causal_links += len(causes) + len(consequences)
                
                if causes:
                    events_with_causes += 1
                    max_causes = max(max_causes, len(causes))
                
                if consequences:
                    events_with_consequences += 1
                    max_consequences = max(max_consequences, len(consequences))
            
            total_events = len(events)
            
            return {
                "total_causal_links": total_causal_links // 2,  # 每个链接被计算了两次
                "events_with_causes": events_with_causes,
                "events_with_consequences": events_with_consequences,
                "causal_connectivity": (events_with_causes + events_with_consequences) / (2 * total_events) if total_events > 0 else 0,
                "max_causes_per_event": max_causes,
                "max_consequences_per_event": max_consequences,
                "isolated_events": total_events - len(set([e["id"] for e in events if e.get("causes") or e.get("consequences")]))
            }
            
        except Exception as e:
            self.logger.error(f"Failed to analyze causal network: {str(e)}")
            return {}
    
    def _analyze_character_involvement(self) -> Dict[str, Any]:
        """分析人物参与度"""
        try:
            events = self.search_events()
            
            character_event_count = defaultdict(int)
            character_importance_sum = defaultdict(int)
            
            for event in events:
                participants = event.get("participants", [])
                importance = event.get("importance", 5)
                
                for participant in participants:
                    character_event_count[participant] += 1
                    character_importance_sum[participant] += importance
            
            # 计算平均重要性
            character_avg_importance = {}
            for char_id, total_importance in character_importance_sum.items():
                character_avg_importance[char_id] = total_importance / character_event_count[char_id]
            
            # 排序
            most_active = sorted(character_event_count.items(), key=lambda x: x[1], reverse=True)[:10]
            most_important = sorted(character_avg_importance.items(), key=lambda x: x[1], reverse=True)[:10]
            
            return {
                "total_character_participations": sum(character_event_count.values()),
                "unique_characters_involved": len(character_event_count),
                "most_active_characters": [{"character_id": cid, "event_count": count} for cid, count in most_active],
                "highest_importance_characters": [{"character_id": cid, "avg_importance": round(imp, 2)} for cid, imp in most_important],
                "events_without_participants": len([e for e in events if not e.get("participants")])
            }
            
        except Exception as e:
            self.logger.error(f"Failed to analyze character involvement: {str(e)}")
            return {}
    
    def _detect_plot_holes(self) -> List[Dict[str, Any]]:
        """检测情节漏洞"""
        try:
            plot_holes = []
            events = self.search_events()
            
            for event in events:
                event_id = event["id"]
                title = event["title"]
                
                # 检查因果关系的完整性
                causes = event.get("causes", [])
                for cause_id in causes:
                    cause_event = self.get_event(cause_id)
                    if not cause_event:
                        plot_holes.append({
                            "type": "missing_cause_event",
                            "event_id": event_id,
                            "event_title": title,
                            "missing_cause_id": cause_id,
                            "description": f"Event '{title}' references non-existent cause event"
                        })
                    elif event_id not in cause_event.consequences:
                        plot_holes.append({
                            "type": "inconsistent_causal_link",
                            "event_id": event_id,
                            "event_title": title,
                            "cause_id": cause_id,
                            "description": f"Causal relationship not bidirectional between '{title}' and cause event"
                        })
                
                # 检查时间逻辑
                if event.get("story_timestamp"):
                    event_time = parse_datetime(event["story_timestamp"])
                    if event_time:
                        for cause_id in causes:
                            cause_event = self.get_event(cause_id)
                            if cause_event and cause_event.story_timestamp:
                                cause_time = parse_datetime(cause_event.story_timestamp.isoformat() if isinstance(cause_event.story_timestamp, datetime) else cause_event.story_timestamp)
                                if cause_time and cause_time > event_time:
                                    plot_holes.append({
                                        "type": "temporal_inconsistency",
                                        "event_id": event_id,
                                        "event_title": title,
                                        "cause_id": cause_id,
                                        "description": f"Cause event occurs after effect event '{title}'"
                                    })
            
            return plot_holes
            
        except Exception as e:
            self.logger.error(f"Failed to detect plot holes: {str(e)}")
            return []
    
    def _analyze_event_density(self, timeline_events: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析事件密度"""
        if len(timeline_events) < 2:
            return {"message": "Insufficient events for density analysis"}
        
        timestamps = []
        for event in timeline_events:
            if event.get("story_timestamp"):
                timestamp = parse_datetime(event["story_timestamp"])
                if timestamp:
                    timestamps.append(timestamp)
        
        if len(timestamps) < 2:
            return {"message": "Insufficient valid timestamps"}
        
        timestamps.sort()
        intervals = []
        
        for i in range(1, len(timestamps)):
            interval = (timestamps[i] - timestamps[i-1]).total_seconds() / 3600  # 小时
            intervals.append(interval)
        
        avg_interval = sum(intervals) / len(intervals)
        min_interval = min(intervals)
        max_interval = max(intervals)
        
        # 找出密集和稀疏区域
        dense_threshold = avg_interval * 0.5
        sparse_threshold = avg_interval * 2.0
        
        dense_periods = sum(1 for interval in intervals if interval < dense_threshold)
        sparse_periods = sum(1 for interval in intervals if interval > sparse_threshold)
        
        return {
            "average_interval_hours": round(avg_interval, 2),
            "min_interval_hours": round(min_interval, 2),
            "max_interval_hours": round(max_interval, 2),
            "dense_periods": dense_periods,
            "sparse_periods": sparse_periods,
            "density_score": dense_periods / len(intervals) if intervals else 0
        }
    
    def _find_timeline_gaps(self, timeline_events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """查找时间线空隙"""
        if len(timeline_events) < 2:
            return []
        
        timestamps = []
        for event in timeline_events:
            if event.get("story_timestamp"):
                timestamp = parse_datetime(event["story_timestamp"])
                if timestamp:
                    timestamps.append((timestamp, event["title"]))
        
        if len(timestamps) < 2:
            return []
        
        timestamps.sort()
        gaps = []
        
        # 计算平均间隔
        intervals = []
        for i in range(1, len(timestamps)):
            interval = (timestamps[i][0] - timestamps[i-1][0]).total_seconds() / 3600
            intervals.append(interval)
        
        if not intervals:
            return []
        
        avg_interval = sum(intervals) / len(intervals)
        gap_threshold = avg_interval * 3.0  # 超过平均间隔3倍认为是空隙
        
        for i in range(1, len(timestamps)):
            interval_hours = (timestamps[i][0] - timestamps[i-1][0]).total_seconds() / 3600
            if interval_hours > gap_threshold:
                gaps.append({
                    "start_event": timestamps[i-1][1],
                    "end_event": timestamps[i][1],
                    "gap_hours": round(interval_hours, 2),
                    "start_time": timestamps[i-1][0].isoformat(),
                    "end_time": timestamps[i][0].isoformat()
                })
        
        return gaps
    
    def _serialize_event_updates(self, updates: Dict[str, Any]) -> Dict[str, Any]:
        """序列化事件更新数据中的复杂字段"""
        serialized = updates.copy()
        
        # 处理需要序列化的字段
        json_fields = ["participants", "consequences", "causes", "tags"]
        
        for field in json_fields:
            if field in serialized:
                serialized[field] = self.db._serialize_json_field(serialized[field])
        
        # 处理时间字段
        if "story_timestamp" in serialized and serialized["story_timestamp"]:
            if isinstance(serialized["story_timestamp"], datetime):
                serialized["story_timestamp"] = serialized["story_timestamp"].isoformat()
        
        return serialized
    
    def _deserialize_event_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """反序列化事件数据中的JSON字段"""
        deserialized = data.copy()
        
        # 处理需要反序列化的字段
        json_fields = ["participants", "consequences", "causes", "tags"]
        
        for field in json_fields:
            if field in deserialized and deserialized[field]:
                deserialized[field] = self.db._deserialize_json_field(deserialized[field])
        
        return deserialized
