"""
AI小说管理工具 - 创作笔记管理模块
提供创作笔记的创建、查询、更新和分类管理功能
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Set
from dataclasses import asdict

from ..core.database import DatabaseManager
from ..core.models import CreativeNote, NoteType, EntityStatus
from ..core.exceptions import (
    ValidationError, EntityNotFoundError, DuplicateEntityError, 
    DatabaseError
)
from ..config import get_config
from ..utils.validators import validate_creative_note_data
from ..utils.helpers import generate_id, extract_keywords, calculate_text_similarity

class NotesManager:
    """创作笔记管理器"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """初始化笔记管理器"""
        self.db = db_manager or DatabaseManager()
        self.logger = logging.getLogger(__name__)
        self.validation_config = get_config("validation")
    
    def create_note(self, title: str, content: str = "", note_type: str = "idea",
                   related_characters: List[str] = None, related_events: List[str] = None,
                   related_settings: List[str] = None, priority: int = 3, **kwargs) -> str:
        """创建新的创作笔记"""
        try:
            # 验证输入数据
            data = {
                "title": title,
                "content": content,
                "note_type": note_type,
                "related_characters": related_characters or [],
                "related_events": related_events or [],
                "related_settings": related_settings or [],
                "priority": priority,
                **kwargs
            }
            
            errors = validate_creative_note_data(data)
            if errors:
                raise ValidationError("; ".join(errors))
            
            # 检查是否已存在相同标题的笔记
            existing = self.search_notes({"title": title.strip()})
            if existing:
                raise DuplicateEntityError("CreativeNote", title)
            
            # 验证关联实体是否存在
            self._validate_related_entities(related_characters, related_events, related_settings)
            
            # 创建笔记对象
            note = CreativeNote(
                title=title.strip(),
                content=content,
                note_type=NoteType(note_type),
                related_characters=related_characters or [],
                related_events=related_events or [],
                related_settings=related_settings or [],
                priority=priority,
                **kwargs
            )
            
            # 自动提取标签
            if not note.tags:
                note.tags = self._extract_auto_tags(title, content)
            
            # 保存到数据库
            note_id = self.db.insert_entity("creative_notes", note)
            
            self.logger.info(f"Created creative note: {title} (ID: {note_id})")
            return note_id
            
        except Exception as e:
            self.logger.error(f"Failed to create creative note {title}: {str(e)}")
            raise
    
    def get_note(self, note_id: str) -> Optional[CreativeNote]:
        """根据ID获取创作笔记"""
        try:
            data = self.db.get_entity("creative_notes", note_id)
            if not data:
                return None
            
            # 反序列化JSON字段
            data = self._deserialize_note_data(data)
            
            return CreativeNote.from_dict(data)
            
        except Exception as e:
            self.logger.error(f"Failed to get creative note {note_id}: {str(e)}")
            raise DatabaseError(f"Failed to retrieve creative note: {str(e)}")
    
    def update_note(self, note_id: str, updates: Dict[str, Any]) -> bool:
        """更新创作笔记"""
        try:
            # 检查笔记是否存在
            existing = self.get_note(note_id)
            if not existing:
                raise EntityNotFoundError("CreativeNote", note_id)
            
            # 验证更新数据
            if updates:
                errors = validate_creative_note_data(updates)
                if errors:
                    raise ValidationError("; ".join(errors))
            
            # 检查标题冲突（如果更新了标题）
            if "title" in updates:
                existing_with_title = self.search_notes({"title": updates["title"].strip()})
                if existing_with_title and existing_with_title[0]["id"] != note_id:
                    raise DuplicateEntityError("CreativeNote", updates["title"])
            
            # 验证关联实体是否存在
            related_chars = updates.get("related_characters")
            related_events = updates.get("related_events") 
            related_settings = updates.get("related_settings")
            
            if any([related_chars, related_events, related_settings]):
                self._validate_related_entities(related_chars, related_events, related_settings)
            
            # 自动更新标签（如果内容或标题发生变化）
            if "title" in updates or "content" in updates:
                new_title = updates.get("title", existing.title)
                new_content = updates.get("content", existing.content)
                updates["tags"] = self._extract_auto_tags(new_title, new_content)
            
            # 序列化复杂字段
            serialized_updates = self._serialize_note_updates(updates)
            
            # 更新数据库
            success = self.db.update_entity("creative_notes", note_id, serialized_updates)
            
            if success:
                self.logger.info(f"Updated creative note {note_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to update creative note {note_id}: {str(e)}")
            raise
    
    def delete_note(self, note_id: str) -> bool:
        """删除创作笔记（软删除）"""
        try:
            # 检查笔记是否存在
            existing = self.get_note(note_id)
            if not existing:
                raise EntityNotFoundError("CreativeNote", note_id)
            
            # 执行软删除
            success = self.db.delete_entity("creative_notes", note_id)
            
            if success:
                self.logger.info(f"Deleted creative note {note_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to delete creative note {note_id}: {str(e)}")
            raise
    
    def search_notes(self, criteria: Dict[str, Any] = None, 
                    limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """搜索创作笔记"""
        try:
            # 默认只查询活跃状态的笔记
            search_criteria = {"status": "active"}
            if criteria:
                search_criteria.update(criteria)
            
            results = self.db.search_entities("creative_notes", search_criteria, limit, offset)
            
            # 反序列化结果
            for result in results:
                result.update(self._deserialize_note_data(result))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to search creative notes: {str(e)}")
            raise DatabaseError(f"Creative note search failed: {str(e)}")
    
    def search_notes_by_content(self, query: str, limit: int = 50) -> List[Dict[str, Any]]:
        """根据内容搜索笔记"""
        try:
            # 使用LIKE查询搜索标题和内容
            sql = """
                SELECT * FROM creative_notes 
                WHERE status = 'active' AND (
                    title LIKE ? OR content LIKE ?
                )
                ORDER BY priority DESC, updated_at DESC
                LIMIT ?
            """
            
            search_pattern = f"%{query}%"
            results = self.db.execute_custom_query(sql, (search_pattern, search_pattern, limit))
            
            # 反序列化结果
            for result in results:
                result.update(self._deserialize_note_data(result))
            
            # 计算相关性分数
            for result in results:
                title_score = self._calculate_relevance_score(query, result.get("title", ""))
                content_score = self._calculate_relevance_score(query, result.get("content", ""))
                result["relevance_score"] = max(title_score, content_score * 0.8)  # 标题权重更高
            
            # 按相关性排序
            results.sort(key=lambda x: x["relevance_score"], reverse=True)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to search notes by content: {str(e)}")
            raise DatabaseError(f"Content search failed: {str(e)}")
    
    def get_notes_by_type(self, note_type: str) -> List[Dict[str, Any]]:
        """根据类型获取笔记"""
        return self.search_notes({"note_type": note_type})
    
    def get_notes_by_priority(self, min_priority: int = 1, max_priority: int = 5) -> List[Dict[str, Any]]:
        """根据优先级获取笔记"""
        try:
            sql = """
                SELECT * FROM creative_notes 
                WHERE status = 'active' AND priority BETWEEN ? AND ?
                ORDER BY priority DESC, updated_at DESC
            """
            
            results = self.db.execute_custom_query(sql, (min_priority, max_priority))
            
            # 反序列化结果
            for result in results:
                result.update(self._deserialize_note_data(result))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to get notes by priority: {str(e)}")
            raise DatabaseError(f"Failed to get notes by priority: {str(e)}")
    
    def get_related_notes(self, entity_type: str, entity_id: str) -> List[Dict[str, Any]]:
        """获取与指定实体相关的笔记"""
        try:
            field_map = {
                "character": "related_characters",
                "event": "related_events", 
                "setting": "related_settings"
            }
            
            if entity_type not in field_map:
                raise ValidationError(f"Invalid entity type: {entity_type}")
            
            field_name = field_map[entity_type]
            
            sql = f"""
                SELECT * FROM creative_notes 
                WHERE status = 'active' AND (
                    {field_name} LIKE ? OR {field_name} LIKE ? OR {field_name} LIKE ?
                )
                ORDER BY priority DESC, updated_at DESC
            """
            
            # 构建搜索模式
            patterns = [
                f'%"{entity_id}"%',  # 在数组中间
                f'["{entity_id}"%',  # 在数组开头
                f'%"{entity_id}"]'   # 在数组结尾
            ]
            
            results = self.db.execute_custom_query(sql, tuple(patterns))
            
            # 反序列化结果并精确过滤
            filtered_results = []
            for result in results:
                result.update(self._deserialize_note_data(result))
                related_list = result.get(field_name, [])
                if entity_id in related_list:
                    filtered_results.append(result)
            
            return filtered_results
            
        except Exception as e:
            self.logger.error(f"Failed to get related notes: {str(e)}")
            raise DatabaseError(f"Failed to get related notes: {str(e)}")
    
    def get_similar_notes(self, note_id: str, similarity_threshold: float = 0.3, 
                         limit: int = 10) -> List[Dict[str, Any]]:
        """获取相似的笔记"""
        try:
            target_note = self.get_note(note_id)
            if not target_note:
                raise EntityNotFoundError("CreativeNote", note_id)
            
            # 获取所有其他笔记
            all_notes = self.search_notes()
            similar_notes = []
            
            target_text = f"{target_note.title} {target_note.content}"
            
            for note in all_notes:
                if note["id"] != note_id:
                    note_text = f"{note.get('title', '')} {note.get('content', '')}"
                    similarity = calculate_text_similarity(target_text, note_text)
                    
                    if similarity >= similarity_threshold:
                        note["similarity_score"] = similarity
                        similar_notes.append(note)
            
            # 按相似度排序
            similar_notes.sort(key=lambda x: x["similarity_score"], reverse=True)
            
            return similar_notes[:limit]
            
        except Exception as e:
            self.logger.error(f"Failed to get similar notes: {str(e)}")
            raise DatabaseError(f"Failed to get similar notes: {str(e)}")
    
    def mark_note_as_used(self, note_id: str) -> bool:
        """标记笔记为已使用"""
        return self.update_note(note_id, {"usage_status": "used"})
    
    def archive_note(self, note_id: str) -> bool:
        """归档笔记"""
        return self.update_note(note_id, {"usage_status": "archived"})
    
    def get_note_statistics(self) -> Dict[str, Any]:
        """获取创作笔记统计信息"""
        try:
            stats = {}
            
            # 总笔记数
            total_sql = "SELECT COUNT(*) as count FROM creative_notes WHERE status = 'active'"
            total_result = self.db.execute_custom_query(total_sql)
            stats["total_notes"] = total_result[0]["count"] if total_result else 0
            
            # 按类型分布
            type_sql = """
                SELECT note_type, COUNT(*) as count 
                FROM creative_notes 
                WHERE status = 'active' 
                GROUP BY note_type 
                ORDER BY count DESC
            """
            type_result = self.db.execute_custom_query(type_sql)
            stats["type_distribution"] = {
                row["note_type"]: row["count"] for row in type_result
            }
            
            # 按优先级分布
            priority_sql = """
                SELECT priority, COUNT(*) as count 
                FROM creative_notes 
                WHERE status = 'active' 
                GROUP BY priority 
                ORDER BY priority DESC
            """
            priority_result = self.db.execute_custom_query(priority_sql)
            stats["priority_distribution"] = {
                str(row["priority"]): row["count"] for row in priority_result
            }
            
            # 按使用状态分布
            usage_sql = """
                SELECT usage_status, COUNT(*) as count 
                FROM creative_notes 
                WHERE status = 'active' 
                GROUP BY usage_status
            """
            usage_result = self.db.execute_custom_query(usage_sql)
            stats["usage_distribution"] = {
                row["usage_status"]: row["count"] for row in usage_result
            }
            
            # 最近活动
            recent_sql = """
                SELECT COUNT(*) as count 
                FROM creative_notes 
                WHERE status = 'active' AND updated_at >= datetime('now', '-7 days')
            """
            recent_result = self.db.execute_custom_query(recent_sql)
            stats["recent_activity"] = recent_result[0]["count"] if recent_result else 0
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get note statistics: {str(e)}")
            raise DatabaseError(f"Failed to get statistics: {str(e)}")
    
    def _validate_related_entities(self, related_characters: List[str] = None,
                                  related_events: List[str] = None,
                                  related_settings: List[str] = None):
        """验证关联实体是否存在"""
        if related_characters:
            for char_id in related_characters:
                if not self._entity_exists("characters", char_id):
                    raise EntityNotFoundError("Character", char_id)
        
        if related_events:
            for event_id in related_events:
                if not self._entity_exists("events", event_id):
                    raise EntityNotFoundError("Event", event_id)
        
        if related_settings:
            for setting_id in related_settings:
                if not self._entity_exists("world_settings", setting_id):
                    raise EntityNotFoundError("WorldSetting", setting_id)
    
    def _entity_exists(self, table: str, entity_id: str) -> bool:
        """检查实体是否存在"""
        try:
            entity_data = self.db.get_entity(table, entity_id)
            return entity_data is not None and entity_data.get("status") == "active"
        except Exception:
            return False
    
    def _extract_auto_tags(self, title: str, content: str) -> List[str]:
        """自动提取标签"""
        text = f"{title} {content}"
        keywords = extract_keywords(text, min_length=2, max_keywords=10)
        
        # 过滤掉过于常见的词汇
        common_words = {
            "idea", "note", "thought", "story", "character", "plot", "scene",
            "想法", "笔记", "思考", "故事", "人物", "情节", "场景"
        }
        
        filtered_keywords = [kw for kw in keywords if kw.lower() not in common_words]
        
        return filtered_keywords[:8]  # 最多8个标签
    
    def _calculate_relevance_score(self, query: str, text: str) -> float:
        """计算相关性分数"""
        if not query or not text:
            return 0.0
        
        query_lower = query.lower()
        text_lower = text.lower()
        
        # 精确匹配得分最高
        if query_lower in text_lower:
            return 1.0
        
        # 计算词汇重叠度
        query_words = set(query_lower.split())
        text_words = set(text_lower.split())
        
        if not query_words or not text_words:
            return 0.0
        
        intersection = query_words.intersection(text_words)
        union = query_words.union(text_words)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _serialize_note_updates(self, updates: Dict[str, Any]) -> Dict[str, Any]:
        """序列化笔记更新数据中的复杂字段"""
        serialized = updates.copy()
        
        # 处理需要序列化的字段
        json_fields = ["related_characters", "related_events", "related_settings", "tags"]
        
        for field in json_fields:
            if field in serialized:
                serialized[field] = self.db._serialize_json_field(serialized[field])
        
        return serialized
    
    def _deserialize_note_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """反序列化笔记数据中的JSON字段"""
        deserialized = data.copy()
        
        # 处理需要反序列化的字段
        json_fields = ["related_characters", "related_events", "related_settings", "tags"]
        
        for field in json_fields:
            if field in deserialized and deserialized[field]:
                deserialized[field] = self.db._deserialize_json_field(deserialized[field])
        
        return deserialized
