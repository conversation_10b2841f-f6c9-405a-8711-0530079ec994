"""
AI小说管理工具 - 增强一致性检查系统
提供世界观和情节一致性检查功能
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from collections import defaultdict, Counter
from datetime import datetime
from difflib import SequenceMatcher

class ConsistencyChecker:
    """一致性检查器"""
    
    def __init__(self):
        """初始化一致性检查器"""
        self.logger = logging.getLogger(__name__)
        
        # 一致性规则
        self.consistency_rules = {
            'character_consistency': [
                self._check_character_name_consistency,
                self._check_character_trait_consistency,
                self._check_character_age_consistency,
                self._check_character_appearance_consistency
            ],
            'relationship_consistency': [
                self._check_relationship_symmetry,
                self._check_relationship_conflicts,
                self._check_relationship_strength_consistency
            ],
            'event_consistency': [
                self._check_timeline_consistency,
                self._check_event_participant_consistency,
                self._check_event_location_consistency,
                self._check_causal_consistency
            ],
            'world_consistency': [
                self._check_world_rule_consistency,
                self._check_location_consistency,
                self._check_setting_conflicts
            ]
        }
        
        # 严重性级别
        self.severity_levels = {
            'critical': 4,    # 严重不一致，需要立即修复
            'major': 3,       # 重要不一致，应该修复
            'minor': 2,       # 轻微不一致，建议修复
            'suggestion': 1   # 建议性改进
        }
    
    def check_consistency(self, data: Dict[str, List[Dict[str, Any]]], 
                         check_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """执行一致性检查"""
        try:
            if check_types is None:
                check_types = list(self.consistency_rules.keys())
            
            all_issues = []
            check_results = {}
            
            for check_type in check_types:
                if check_type in self.consistency_rules:
                    self.logger.info(f"Running {check_type} checks...")
                    
                    type_issues = []
                    rules = self.consistency_rules[check_type]
                    
                    for rule_func in rules:
                        try:
                            issues = rule_func(data)
                            type_issues.extend(issues)
                        except Exception as e:
                            self.logger.error(f"Error in rule {rule_func.__name__}: {str(e)}")
                    
                    check_results[check_type] = {
                        'issues': type_issues,
                        'issue_count': len(type_issues),
                        'severity_distribution': self._analyze_severity_distribution(type_issues)
                    }
                    
                    all_issues.extend(type_issues)
            
            # 按严重性排序
            all_issues.sort(key=lambda x: self.severity_levels.get(x['severity'], 0), reverse=True)
            
            # 生成总体统计
            overall_stats = self._generate_consistency_stats(all_issues, check_results)
            
            return {
                "success": True,
                "check_types": check_types,
                "total_issues": len(all_issues),
                "issues": all_issues,
                "check_results": check_results,
                "overall_stats": overall_stats,
                "recommendations": self._generate_recommendations(all_issues),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Consistency check failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _check_character_name_consistency(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查人物姓名一致性"""
        issues = []
        characters = data.get('characters', [])
        
        # 检查重复姓名
        name_counts = Counter(char.get('name', '').lower() for char in characters if char.get('name'))
        for name, count in name_counts.items():
            if count > 1:
                issues.append({
                    'type': 'character_name_duplicate',
                    'severity': 'critical',
                    'message': f"Duplicate character name: '{name}' appears {count} times",
                    'affected_items': [char for char in characters if char.get('name', '').lower() == name],
                    'suggestion': f"Rename duplicate characters or merge if they are the same person"
                })
        
        # 检查相似姓名（可能的拼写错误）
        names = [char.get('name', '') for char in characters if char.get('name')]
        for i, name1 in enumerate(names):
            for name2 in names[i+1:]:
                similarity = SequenceMatcher(None, name1.lower(), name2.lower()).ratio()
                if 0.7 <= similarity < 1.0:  # 相似但不完全相同
                    issues.append({
                        'type': 'character_name_similar',
                        'severity': 'major',
                        'message': f"Similar character names: '{name1}' and '{name2}' (similarity: {similarity:.2f})",
                        'affected_items': [name1, name2],
                        'suggestion': "Check if these are the same character with different spellings"
                    })
        
        return issues
    
    def _check_character_trait_consistency(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查人物特征一致性"""
        issues = []
        characters = data.get('characters', [])
        
        for char in characters:
            char_name = char.get('name', 'Unknown')
            traits = char.get('personality_traits', [])
            
            if isinstance(traits, list):
                # 检查矛盾的性格特征
                contradictory_pairs = [
                    (['brave', 'courageous'], ['cowardly', 'fearful']),
                    (['kind', 'gentle'], ['cruel', 'harsh']),
                    (['intelligent', 'smart'], ['stupid', 'dumb']),
                    (['honest', 'truthful'], ['dishonest', 'lying']),
                    (['calm', 'peaceful'], ['aggressive', 'violent'])
                ]
                
                trait_lower = [t.lower() for t in traits]
                
                for positive_traits, negative_traits in contradictory_pairs:
                    has_positive = any(pt in trait_lower for pt in positive_traits)
                    has_negative = any(nt in trait_lower for nt in negative_traits)
                    
                    if has_positive and has_negative:
                        issues.append({
                            'type': 'character_trait_contradiction',
                            'severity': 'major',
                            'message': f"Character '{char_name}' has contradictory traits",
                            'affected_items': [char_name],
                            'details': {
                                'character': char_name,
                                'contradictory_traits': traits
                            },
                            'suggestion': "Review and resolve contradictory personality traits"
                        })
        
        return issues
    
    def _check_character_age_consistency(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查人物年龄一致性"""
        issues = []
        characters = data.get('characters', [])
        events = data.get('events', [])
        
        # 检查年龄的合理性
        for char in characters:
            char_name = char.get('name', 'Unknown')
            age = char.get('age')
            
            if age is not None:
                try:
                    age_num = int(age)
                    if age_num < 0:
                        issues.append({
                            'type': 'character_age_negative',
                            'severity': 'critical',
                            'message': f"Character '{char_name}' has negative age: {age}",
                            'affected_items': [char_name],
                            'suggestion': "Set a valid positive age"
                        })
                    elif age_num > 200:
                        issues.append({
                            'type': 'character_age_unrealistic',
                            'severity': 'minor',
                            'message': f"Character '{char_name}' has unusually high age: {age}",
                            'affected_items': [char_name],
                            'suggestion': "Verify if this age is intentional for the story setting"
                        })
                except ValueError:
                    issues.append({
                        'type': 'character_age_invalid',
                        'severity': 'major',
                        'message': f"Character '{char_name}' has invalid age format: {age}",
                        'affected_items': [char_name],
                        'suggestion': "Use numeric age format"
                    })
        
        return issues
    
    def _check_character_appearance_consistency(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查人物外貌一致性"""
        issues = []
        characters = data.get('characters', [])
        
        # 检查外貌描述中的矛盾
        for char in characters:
            char_name = char.get('name', 'Unknown')
            appearance = char.get('appearance', '')
            
            if appearance:
                appearance_lower = appearance.lower()
                
                # 检查身高矛盾
                height_patterns = [
                    (r'tall|high', r'short|small'),
                    (r'big|large', r'tiny|small'),
                    (r'fat|heavy', r'thin|skinny')
                ]
                
                for positive_pattern, negative_pattern in height_patterns:
                    if re.search(positive_pattern, appearance_lower) and re.search(negative_pattern, appearance_lower):
                        issues.append({
                            'type': 'character_appearance_contradiction',
                            'severity': 'minor',
                            'message': f"Character '{char_name}' has contradictory appearance description",
                            'affected_items': [char_name],
                            'details': {
                                'character': char_name,
                                'appearance': appearance
                            },
                            'suggestion': "Review appearance description for contradictions"
                        })
        
        return issues
    
    def _check_relationship_symmetry(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查关系对称性"""
        issues = []
        relationships = data.get('relationships', [])
        
        # 构建关系映射
        relationship_map = {}
        for rel in relationships:
            char_a = rel.get('character_a', '')
            char_b = rel.get('character_b', '')
            rel_type = rel.get('relationship_type', '')
            
            key = tuple(sorted([char_a, char_b]))
            if key not in relationship_map:
                relationship_map[key] = []
            relationship_map[key].append(rel)
        
        # 检查对称关系
        symmetric_relations = ['friend', 'enemy', 'rival', 'ally', 'colleague']
        
        for (char_a, char_b), rels in relationship_map.items():
            if len(rels) == 1:
                rel = rels[0]
                rel_type = rel.get('relationship_type', '').lower()
                
                if rel_type in symmetric_relations:
                    issues.append({
                        'type': 'relationship_asymmetric',
                        'severity': 'minor',
                        'message': f"Asymmetric {rel_type} relationship between '{char_a}' and '{char_b}'",
                        'affected_items': [char_a, char_b],
                        'suggestion': f"Consider adding reciprocal {rel_type} relationship"
                    })
            elif len(rels) > 1:
                # 检查多重关系是否冲突
                rel_types = [r.get('relationship_type', '').lower() for r in rels]
                if len(set(rel_types)) > 1:
                    issues.append({
                        'type': 'relationship_multiple_types',
                        'severity': 'major',
                        'message': f"Multiple relationship types between '{char_a}' and '{char_b}': {rel_types}",
                        'affected_items': [char_a, char_b],
                        'suggestion': "Clarify the primary relationship type or merge relationships"
                    })
        
        return issues
    
    def _check_relationship_conflicts(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查关系冲突"""
        issues = []
        relationships = data.get('relationships', [])
        
        # 定义冲突关系
        conflicting_relations = [
            (['friend', 'ally'], ['enemy', 'rival']),
            (['family', 'parent', 'child'], ['enemy']),
            (['lover', 'romantic'], ['enemy'])
        ]
        
        # 按人物对分组关系
        char_pair_relations = defaultdict(list)
        for rel in relationships:
            char_a = rel.get('character_a', '')
            char_b = rel.get('character_b', '')
            rel_type = rel.get('relationship_type', '').lower()
            
            key = tuple(sorted([char_a, char_b]))
            char_pair_relations[key].append(rel_type)
        
        # 检查冲突
        for (char_a, char_b), rel_types in char_pair_relations.items():
            for positive_rels, negative_rels in conflicting_relations:
                has_positive = any(pr in rel_types for pr in positive_rels)
                has_negative = any(nr in rel_types for nr in negative_rels)
                
                if has_positive and has_negative:
                    issues.append({
                        'type': 'relationship_conflict',
                        'severity': 'major',
                        'message': f"Conflicting relationships between '{char_a}' and '{char_b}': {rel_types}",
                        'affected_items': [char_a, char_b],
                        'suggestion': "Resolve conflicting relationship types"
                    })
        
        return issues
    
    def _check_relationship_strength_consistency(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查关系强度一致性"""
        issues = []
        relationships = data.get('relationships', [])
        
        for rel in relationships:
            char_a = rel.get('character_a', '')
            char_b = rel.get('character_b', '')
            rel_type = rel.get('relationship_type', '').lower()
            strength = rel.get('strength', 0.5)
            
            # 检查强度与关系类型的一致性
            if rel_type in ['enemy', 'rival'] and strength > 0.7:
                issues.append({
                    'type': 'relationship_strength_inconsistent',
                    'severity': 'minor',
                    'message': f"High strength ({strength}) for negative relationship ({rel_type}) between '{char_a}' and '{char_b}'",
                    'affected_items': [char_a, char_b],
                    'suggestion': "Consider if high strength is appropriate for this relationship type"
                })
            elif rel_type in ['friend', 'ally', 'family'] and strength < 0.3:
                issues.append({
                    'type': 'relationship_strength_inconsistent',
                    'severity': 'minor',
                    'message': f"Low strength ({strength}) for positive relationship ({rel_type}) between '{char_a}' and '{char_b}'",
                    'affected_items': [char_a, char_b],
                    'suggestion': "Consider if low strength is appropriate for this relationship type"
                })
        
        return issues

    def _check_timeline_consistency(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查时间线一致性"""
        issues = []
        events = data.get('events', [])

        # 按时间排序事件
        timed_events = []
        for event in events:
            timestamp = event.get('story_timestamp', '')
            if timestamp:
                timed_events.append((timestamp, event))

        timed_events.sort(key=lambda x: x[0])

        # 检查时间线逻辑
        for i in range(len(timed_events) - 1):
            current_time, current_event = timed_events[i]
            next_time, next_event = timed_events[i + 1]

            # 检查因果关系
            current_participants = set(current_event.get('participants', []))
            next_participants = set(next_event.get('participants', []))

            # 如果同一人物在连续事件中，检查逻辑一致性
            common_participants = current_participants & next_participants
            if common_participants:
                current_type = current_event.get('event_type', '').lower()
                next_type = next_event.get('event_type', '').lower()

                # 检查不合理的事件序列
                if current_type == 'death' and next_type not in ['funeral', 'memorial']:
                    for participant in common_participants:
                        issues.append({
                            'type': 'timeline_logic_error',
                            'severity': 'critical',
                            'message': f"Character '{participant}' appears in event after death",
                            'affected_items': [current_event.get('title', ''), next_event.get('title', '')],
                            'suggestion': "Check if character should participate in later events"
                        })

        return issues

    def _check_event_participant_consistency(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查事件参与者一致性"""
        issues = []
        events = data.get('events', [])
        characters = data.get('characters', [])

        # 获取所有已知人物
        known_characters = set(char.get('name', '') for char in characters if char.get('name'))

        for event in events:
            event_title = event.get('title', 'Unknown Event')
            participants = event.get('participants', [])

            if isinstance(participants, list):
                for participant in participants:
                    if participant and participant not in known_characters:
                        issues.append({
                            'type': 'event_unknown_participant',
                            'severity': 'major',
                            'message': f"Unknown participant '{participant}' in event '{event_title}'",
                            'affected_items': [event_title, participant],
                            'suggestion': f"Create character profile for '{participant}' or check spelling"
                        })

        return issues

    def _check_event_location_consistency(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查事件地点一致性"""
        issues = []
        events = data.get('events', [])
        world_settings = data.get('world_settings', [])

        # 获取所有已知地点
        known_locations = set()
        for setting in world_settings:
            if setting.get('category', '').lower() in ['location', 'place', 'geography']:
                known_locations.add(setting.get('name', ''))

        for event in events:
            event_title = event.get('title', 'Unknown Event')
            location = event.get('location', '')

            if location and location not in known_locations:
                issues.append({
                    'type': 'event_unknown_location',
                    'severity': 'minor',
                    'message': f"Unknown location '{location}' in event '{event_title}'",
                    'affected_items': [event_title, location],
                    'suggestion': f"Create world setting for location '{location}' or check spelling"
                })

        return issues

    def _check_causal_consistency(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查因果一致性"""
        issues = []
        events = data.get('events', [])

        # 按时间排序事件
        timed_events = []
        for event in events:
            timestamp = event.get('story_timestamp', '')
            if timestamp:
                timed_events.append((timestamp, event))

        timed_events.sort(key=lambda x: x[0])

        # 检查因果链
        for i in range(len(timed_events)):
            current_time, current_event = timed_events[i]
            current_consequences = current_event.get('consequences', [])

            if isinstance(current_consequences, list):
                for consequence in current_consequences:
                    # 查找后续事件中是否有对应的结果
                    found_consequence = False
                    for j in range(i + 1, len(timed_events)):
                        later_time, later_event = timed_events[j]
                        later_title = later_event.get('title', '').lower()
                        later_description = later_event.get('description', '').lower()

                        if (consequence.lower() in later_title or
                            consequence.lower() in later_description):
                            found_consequence = True
                            break

                    if not found_consequence:
                        issues.append({
                            'type': 'causal_inconsistency',
                            'severity': 'minor',
                            'message': f"Consequence '{consequence}' from event '{current_event.get('title', '')}' not found in later events",
                            'affected_items': [current_event.get('title', '')],
                            'suggestion': "Add follow-up event or remove unfulfilled consequence"
                        })

        return issues

    def _check_world_rule_consistency(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查世界观规则一致性"""
        issues = []
        world_settings = data.get('world_settings', [])
        events = data.get('events', [])

        # 收集所有规则
        world_rules = []
        for setting in world_settings:
            rules = setting.get('rules', [])
            if isinstance(rules, list):
                for rule in rules:
                    world_rules.append({
                        'rule': rule,
                        'setting': setting.get('name', 'Unknown'),
                        'category': setting.get('category', 'Unknown')
                    })

        # 检查事件是否违反世界观规则
        for event in events:
            event_title = event.get('title', 'Unknown Event')
            event_description = event.get('description', '').lower()

            for rule_info in world_rules:
                rule = rule_info['rule'].lower()

                # 简单的规则违反检测（可以扩展为更复杂的NLP分析）
                if 'cannot' in rule or 'impossible' in rule or 'forbidden' in rule:
                    # 提取被禁止的行为
                    forbidden_actions = re.findall(r'cannot\s+(\w+)', rule)
                    forbidden_actions.extend(re.findall(r'impossible\s+to\s+(\w+)', rule))
                    forbidden_actions.extend(re.findall(r'forbidden\s+to\s+(\w+)', rule))

                    for action in forbidden_actions:
                        if action in event_description:
                            issues.append({
                                'type': 'world_rule_violation',
                                'severity': 'major',
                                'message': f"Event '{event_title}' may violate world rule: '{rule_info['rule']}'",
                                'affected_items': [event_title, rule_info['setting']],
                                'details': {
                                    'event': event_title,
                                    'rule': rule_info['rule'],
                                    'setting': rule_info['setting']
                                },
                                'suggestion': "Review event against world setting rules"
                            })

        return issues

    def _check_location_consistency(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查地点一致性"""
        issues = []
        world_settings = data.get('world_settings', [])

        # 收集地点设定
        locations = [setting for setting in world_settings
                    if setting.get('category', '').lower() in ['location', 'place', 'geography']]

        # 检查地点名称重复
        location_names = [loc.get('name', '') for loc in locations if loc.get('name')]
        name_counts = Counter(location_names)

        for name, count in name_counts.items():
            if count > 1:
                issues.append({
                    'type': 'location_name_duplicate',
                    'severity': 'major',
                    'message': f"Duplicate location name: '{name}' appears {count} times",
                    'affected_items': [name],
                    'suggestion': "Rename duplicate locations or merge if they are the same place"
                })

        # 检查地点描述冲突
        for i, loc1 in enumerate(locations):
            for loc2 in locations[i+1:]:
                if loc1.get('name') == loc2.get('name'):
                    desc1 = loc1.get('description', '').lower()
                    desc2 = loc2.get('description', '').lower()

                    if desc1 and desc2 and desc1 != desc2:
                        similarity = SequenceMatcher(None, desc1, desc2).ratio()
                        if similarity < 0.7:  # 描述差异较大
                            issues.append({
                                'type': 'location_description_conflict',
                                'severity': 'minor',
                                'message': f"Conflicting descriptions for location '{loc1.get('name')}'",
                                'affected_items': [loc1.get('name')],
                                'suggestion': "Reconcile conflicting location descriptions"
                            })

        return issues

    def _check_setting_conflicts(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """检查设定冲突"""
        issues = []
        world_settings = data.get('world_settings', [])

        # 按分类分组设定
        settings_by_category = defaultdict(list)
        for setting in world_settings:
            category = setting.get('category', 'Other')
            settings_by_category[category].append(setting)

        # 检查同类设定中的冲突
        for category, settings in settings_by_category.items():
            for i, setting1 in enumerate(settings):
                for setting2 in settings[i+1:]:
                    # 检查规则冲突
                    rules1 = setting1.get('rules', [])
                    rules2 = setting2.get('rules', [])

                    if isinstance(rules1, list) and isinstance(rules2, list):
                        for rule1 in rules1:
                            for rule2 in rules2:
                                # 简单的冲突检测
                                if self._rules_conflict(rule1, rule2):
                                    issues.append({
                                        'type': 'setting_rule_conflict',
                                        'severity': 'major',
                                        'message': f"Conflicting rules in {category} settings",
                                        'affected_items': [setting1.get('name', ''), setting2.get('name', '')],
                                        'details': {
                                            'setting1': setting1.get('name', ''),
                                            'rule1': rule1,
                                            'setting2': setting2.get('name', ''),
                                            'rule2': rule2
                                        },
                                        'suggestion': "Resolve conflicting rules between settings"
                                    })

        return issues

    def _rules_conflict(self, rule1: str, rule2: str) -> bool:
        """检查两个规则是否冲突"""
        rule1_lower = rule1.lower()
        rule2_lower = rule2.lower()

        # 简单的冲突检测模式
        conflict_patterns = [
            (r'can\s+(\w+)', r'cannot\s+\1'),
            (r'allows?\s+(\w+)', r'forbids?\s+\1'),
            (r'possible\s+to\s+(\w+)', r'impossible\s+to\s+\1'),
            (r'(\w+)\s+is\s+true', r'\1\s+is\s+false')
        ]

        for positive_pattern, negative_pattern in conflict_patterns:
            positive_match = re.search(positive_pattern, rule1_lower)
            negative_match = re.search(negative_pattern, rule2_lower)

            if positive_match and negative_match:
                return True

            # 反向检查
            positive_match = re.search(positive_pattern, rule2_lower)
            negative_match = re.search(negative_pattern, rule1_lower)

            if positive_match and negative_match:
                return True

        return False

    def _analyze_severity_distribution(self, issues: List[Dict[str, Any]]) -> Dict[str, int]:
        """分析严重性分布"""
        distribution = defaultdict(int)
        for issue in issues:
            severity = issue.get('severity', 'unknown')
            distribution[severity] += 1
        return dict(distribution)

    def _generate_consistency_stats(self, all_issues: List[Dict[str, Any]],
                                   check_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成一致性统计信息"""
        stats = {
            'total_issues': len(all_issues),
            'severity_distribution': self._analyze_severity_distribution(all_issues),
            'issue_type_distribution': defaultdict(int),
            'most_common_issues': [],
            'consistency_score': 0.0,
            'check_coverage': {}
        }

        # 问题类型分布
        for issue in all_issues:
            issue_type = issue.get('type', 'unknown')
            stats['issue_type_distribution'][issue_type] += 1

        stats['issue_type_distribution'] = dict(stats['issue_type_distribution'])

        # 最常见的问题
        type_counts = Counter(issue.get('type', 'unknown') for issue in all_issues)
        stats['most_common_issues'] = type_counts.most_common(5)

        # 一致性评分（基于问题严重性）
        total_severity_score = 0
        for issue in all_issues:
            severity = issue.get('severity', 'suggestion')
            total_severity_score += self.severity_levels.get(severity, 1)

        # 假设最大可能的问题数为100，每个都是critical级别
        max_possible_score = 100 * self.severity_levels['critical']
        if max_possible_score > 0:
            stats['consistency_score'] = max(0, 100 - (total_severity_score / max_possible_score * 100))
        else:
            stats['consistency_score'] = 100.0

        # 检查覆盖率
        for check_type, result in check_results.items():
            stats['check_coverage'][check_type] = {
                'issues_found': result['issue_count'],
                'rules_executed': len(self.consistency_rules.get(check_type, []))
            }

        return stats

    def _generate_recommendations(self, issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成修复建议"""
        recommendations = []

        # 按严重性分组
        critical_issues = [i for i in issues if i.get('severity') == 'critical']
        major_issues = [i for i in issues if i.get('severity') == 'major']

        if critical_issues:
            recommendations.append({
                'priority': 'high',
                'category': 'critical_fixes',
                'title': 'Critical Issues Requiring Immediate Attention',
                'description': f"Found {len(critical_issues)} critical consistency issues that need immediate fixing",
                'actions': [
                    f"Fix {issue.get('type', 'unknown')}: {issue.get('message', '')}"
                    for issue in critical_issues[:3]  # 只显示前3个
                ]
            })

        if major_issues:
            recommendations.append({
                'priority': 'medium',
                'category': 'major_improvements',
                'title': 'Major Issues for Quality Improvement',
                'description': f"Found {len(major_issues)} major issues that should be addressed",
                'actions': [
                    f"Review {issue.get('type', 'unknown')}: {issue.get('message', '')}"
                    for issue in major_issues[:3]  # 只显示前3个
                ]
            })

        # 基于问题类型的建议
        issue_types = Counter(issue.get('type', 'unknown') for issue in issues)

        if issue_types.get('character_name_duplicate', 0) > 0:
            recommendations.append({
                'priority': 'medium',
                'category': 'character_management',
                'title': 'Character Name Management',
                'description': 'Multiple characters with duplicate or similar names detected',
                'actions': [
                    'Review character names for duplicates',
                    'Consider using unique identifiers or nicknames',
                    'Merge characters if they represent the same person'
                ]
            })

        if issue_types.get('relationship_conflict', 0) > 0:
            recommendations.append({
                'priority': 'medium',
                'category': 'relationship_management',
                'title': 'Relationship Consistency',
                'description': 'Conflicting relationships between characters detected',
                'actions': [
                    'Review conflicting relationship types',
                    'Consider relationship evolution over time',
                    'Clarify complex relationships with detailed descriptions'
                ]
            })

        if issue_types.get('timeline_logic_error', 0) > 0:
            recommendations.append({
                'priority': 'high',
                'category': 'plot_consistency',
                'title': 'Timeline and Plot Logic',
                'description': 'Timeline inconsistencies that affect plot logic',
                'actions': [
                    'Review event chronology',
                    'Check character participation in events',
                    'Ensure causal relationships make sense'
                ]
            })

        return recommendations


class ConsistencyManager:
    """一致性管理器"""

    def __init__(self, api):
        """初始化一致性管理器"""
        self.api = api
        self.checker = ConsistencyChecker()
        self.logger = logging.getLogger(__name__)

    def run_consistency_check(self, check_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """运行一致性检查"""
        try:
            # 获取所有数据
            data = {}

            # 获取人物数据
            char_result = self.api.search_characters({})
            if char_result.get('success'):
                data['characters'] = char_result.get('characters', [])
            else:
                data['characters'] = []

            # 获取关系数据
            rel_result = self.api.search_relationships({})
            if rel_result.get('success'):
                data['relationships'] = rel_result.get('relationships', [])
            else:
                data['relationships'] = []

            # 获取事件数据
            event_result = self.api.search_events({})
            if event_result.get('success'):
                data['events'] = event_result.get('events', [])
            else:
                data['events'] = []

            # 获取世界观数据
            world_result = self.api.search_world_settings({})
            if world_result.get('success'):
                data['world_settings'] = world_result.get('world_settings', [])
            else:
                data['world_settings'] = []

            # 执行一致性检查
            return self.checker.check_consistency(data, check_types)

        except Exception as e:
            self.logger.error(f"Consistency check failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def get_consistency_report(self, check_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """获取一致性报告"""
        try:
            check_result = self.run_consistency_check(check_types)

            if not check_result.get('success'):
                return check_result

            # 生成详细报告
            report = {
                'summary': {
                    'total_issues': check_result['total_issues'],
                    'consistency_score': check_result['overall_stats']['consistency_score'],
                    'check_timestamp': check_result['timestamp'],
                    'severity_breakdown': check_result['overall_stats']['severity_distribution']
                },
                'detailed_analysis': check_result['check_results'],
                'recommendations': check_result['recommendations'],
                'action_items': self._generate_action_items(check_result['issues']),
                'next_steps': self._suggest_next_steps(check_result)
            }

            return {
                'success': True,
                'report': report,
                'raw_results': check_result
            }

        except Exception as e:
            self.logger.error(f"Failed to generate consistency report: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_action_items(self, issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成行动项目"""
        action_items = []

        # 按严重性排序
        sorted_issues = sorted(issues, key=lambda x: self.checker.severity_levels.get(x.get('severity', 'suggestion'), 0), reverse=True)

        for i, issue in enumerate(sorted_issues[:10]):  # 只显示前10个最重要的
            action_items.append({
                'priority': i + 1,
                'severity': issue.get('severity', 'unknown'),
                'type': issue.get('type', 'unknown'),
                'description': issue.get('message', ''),
                'affected_items': issue.get('affected_items', []),
                'suggested_action': issue.get('suggestion', 'Review and fix manually'),
                'estimated_effort': self._estimate_effort(issue)
            })

        return action_items

    def _estimate_effort(self, issue: Dict[str, Any]) -> str:
        """估算修复工作量"""
        severity = issue.get('severity', 'suggestion')
        issue_type = issue.get('type', 'unknown')

        if severity == 'critical':
            return 'High - Requires immediate attention'
        elif severity == 'major':
            if 'duplicate' in issue_type or 'conflict' in issue_type:
                return 'Medium - May require data restructuring'
            else:
                return 'Medium - Requires careful review'
        elif severity == 'minor':
            return 'Low - Can be addressed during regular maintenance'
        else:
            return 'Minimal - Optional improvement'

    def _suggest_next_steps(self, check_result: Dict[str, Any]) -> List[str]:
        """建议下一步行动"""
        next_steps = []

        total_issues = check_result.get('total_issues', 0)
        consistency_score = check_result.get('overall_stats', {}).get('consistency_score', 100)

        if total_issues == 0:
            next_steps.append("Excellent! No consistency issues found. Continue regular monitoring.")
        elif consistency_score >= 90:
            next_steps.append("Good consistency overall. Address minor issues when convenient.")
        elif consistency_score >= 70:
            next_steps.append("Moderate consistency issues. Plan a focused cleanup session.")
        else:
            next_steps.append("Significant consistency issues detected. Prioritize fixing critical and major issues.")

        # 基于问题类型的建议
        severity_dist = check_result.get('overall_stats', {}).get('severity_distribution', {})

        if severity_dist.get('critical', 0) > 0:
            next_steps.append(f"Address {severity_dist['critical']} critical issues immediately.")

        if severity_dist.get('major', 0) > 5:
            next_steps.append("Consider a systematic review of major consistency issues.")

        next_steps.append("Schedule regular consistency checks to maintain quality.")

        return next_steps
