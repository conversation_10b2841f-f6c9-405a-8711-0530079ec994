"""
AI小说管理工具 - 时间线分析工具
提供时间线的分析和可视化功能
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
from collections import defaultdict, Counter
from ..utils.helpers import parse_datetime, format_datetime

class TimelineAnalyzer:
    """时间线分析器"""
    
    def __init__(self):
        """初始化时间线分析器"""
        pass
    
    def create_timeline_visualization(self, events: List[Dict[str, Any]]) -> Dict[str, Any]:
        """创建时间线可视化数据"""
        # 过滤有时间戳的事件
        timestamped_events = []
        for event in events:
            if event.get("story_timestamp"):
                timestamp = parse_datetime(event["story_timestamp"])
                if timestamp:
                    event_copy = event.copy()
                    event_copy["parsed_timestamp"] = timestamp
                    timestamped_events.append(event_copy)
        
        if not timestamped_events:
            return {"error": "No timestamped events found"}
        
        # 按时间排序
        timestamped_events.sort(key=lambda x: x["parsed_timestamp"])
        
        # 创建时间线数据
        timeline_data = {
            "events": self._format_events_for_timeline(timestamped_events),
            "metadata": self._calculate_timeline_metadata(timestamped_events),
            "periods": self._identify_time_periods(timestamped_events),
            "parallel_events": self._find_parallel_events(timestamped_events),
            "causal_chains": self._trace_causal_chains(timestamped_events)
        }
        
        return timeline_data
    
    def _format_events_for_timeline(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """格式化事件用于时间线显示"""
        formatted_events = []
        
        for i, event in enumerate(events):
            formatted_event = {
                "id": event["id"],
                "title": event["title"],
                "description": event.get("description", ""),
                "timestamp": event["parsed_timestamp"].isoformat(),
                "display_time": format_datetime(event["parsed_timestamp"], "%Y-%m-%d %H:%M"),
                "event_type": event.get("event_type", "plot"),
                "importance": event.get("importance", 5),
                "participants": event.get("participants", []),
                "location": event.get("location", ""),
                "position": i,  # 在时间线上的位置
                "duration": self._calculate_event_duration(event),
                "tags": event.get("tags", []),
                "writing_status": event.get("writing_status", "planned")
            }
            
            # 添加因果关系信息
            formatted_event["causes"] = event.get("causes", [])
            formatted_event["consequences"] = event.get("consequences", [])
            formatted_event["has_causal_links"] = bool(formatted_event["causes"] or formatted_event["consequences"])
            
            formatted_events.append(formatted_event)
        
        return formatted_events
    
    def _calculate_timeline_metadata(self, events: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算时间线元数据"""
        if not events:
            return {}
        
        timestamps = [event["parsed_timestamp"] for event in events]
        start_time = min(timestamps)
        end_time = max(timestamps)
        total_duration = end_time - start_time
        
        # 计算事件分布
        type_distribution = Counter(event.get("event_type", "unknown") for event in events)
        importance_distribution = Counter(event.get("importance", 5) for event in events)
        
        # 计算时间间隔统计
        intervals = []
        for i in range(1, len(events)):
            interval = (events[i]["parsed_timestamp"] - events[i-1]["parsed_timestamp"]).total_seconds() / 3600
            intervals.append(interval)
        
        avg_interval = sum(intervals) / len(intervals) if intervals else 0
        
        return {
            "total_events": len(events),
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "total_duration_days": total_duration.days,
            "total_duration_hours": total_duration.total_seconds() / 3600,
            "average_interval_hours": round(avg_interval, 2),
            "type_distribution": dict(type_distribution),
            "importance_distribution": {str(k): v for k, v in importance_distribution.items()},
            "events_per_day": len(events) / max(total_duration.days, 1),
            "timeline_density": self._calculate_timeline_density(events)
        }
    
    def _identify_time_periods(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """识别时间段"""
        if len(events) < 2:
            return []
        
        periods = []
        current_period_start = 0
        
        # 计算平均间隔
        intervals = []
        for i in range(1, len(events)):
            interval = (events[i]["parsed_timestamp"] - events[i-1]["parsed_timestamp"]).total_seconds() / 3600
            intervals.append(interval)
        
        if not intervals:
            return []
        
        avg_interval = sum(intervals) / len(intervals)
        period_threshold = avg_interval * 2.5  # 超过平均间隔2.5倍认为是新时期
        
        for i in range(1, len(events)):
            interval = (events[i]["parsed_timestamp"] - events[i-1]["parsed_timestamp"]).total_seconds() / 3600
            
            if interval > period_threshold:
                # 结束当前时期
                period_events = events[current_period_start:i]
                if period_events:
                    periods.append(self._create_period_summary(period_events, len(periods) + 1))
                
                # 开始新时期
                current_period_start = i
        
        # 添加最后一个时期
        if current_period_start < len(events):
            period_events = events[current_period_start:]
            if period_events:
                periods.append(self._create_period_summary(period_events, len(periods) + 1))
        
        return periods
    
    def _create_period_summary(self, period_events: List[Dict[str, Any]], period_number: int) -> Dict[str, Any]:
        """创建时期摘要"""
        start_time = period_events[0]["parsed_timestamp"]
        end_time = period_events[-1]["parsed_timestamp"]
        duration = end_time - start_time
        
        # 统计事件类型
        type_counts = Counter(event.get("event_type", "unknown") for event in period_events)
        most_common_type = type_counts.most_common(1)[0][0] if type_counts else "unknown"
        
        # 统计参与者
        all_participants = []
        for event in period_events:
            all_participants.extend(event.get("participants", []))
        participant_counts = Counter(all_participants)
        main_characters = [char_id for char_id, count in participant_counts.most_common(5)]
        
        # 计算平均重要性
        importances = [event.get("importance", 5) for event in period_events]
        avg_importance = sum(importances) / len(importances) if importances else 5
        
        return {
            "period_number": period_number,
            "name": f"Period {period_number}",
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration_days": duration.days,
            "duration_hours": duration.total_seconds() / 3600,
            "event_count": len(period_events),
            "dominant_event_type": most_common_type,
            "type_distribution": dict(type_counts),
            "main_characters": main_characters,
            "average_importance": round(avg_importance, 2),
            "event_ids": [event["id"] for event in period_events],
            "key_events": [
                {
                    "id": event["id"],
                    "title": event["title"],
                    "importance": event.get("importance", 5)
                }
                for event in sorted(period_events, key=lambda x: x.get("importance", 5), reverse=True)[:3]
            ]
        }
    
    def _find_parallel_events(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """查找并行事件"""
        parallel_groups = []
        time_tolerance = timedelta(hours=1)  # 1小时内认为是并行
        
        i = 0
        while i < len(events):
            current_event = events[i]
            current_time = current_event["parsed_timestamp"]
            parallel_events = [current_event]
            
            # 查找时间接近的事件
            j = i + 1
            while j < len(events):
                next_event = events[j]
                next_time = next_event["parsed_timestamp"]
                
                if abs((next_time - current_time).total_seconds()) <= time_tolerance.total_seconds():
                    parallel_events.append(next_event)
                    j += 1
                else:
                    break
            
            # 如果有多个并行事件，创建并行组
            if len(parallel_events) > 1:
                parallel_groups.append({
                    "group_id": f"parallel_{len(parallel_groups) + 1}",
                    "timestamp": current_time.isoformat(),
                    "event_count": len(parallel_events),
                    "events": [
                        {
                            "id": event["id"],
                            "title": event["title"],
                            "event_type": event.get("event_type", "plot"),
                            "importance": event.get("importance", 5),
                            "participants": event.get("participants", [])
                        }
                        for event in parallel_events
                    ],
                    "total_importance": sum(event.get("importance", 5) for event in parallel_events),
                    "involved_characters": list(set(
                        char_id for event in parallel_events 
                        for char_id in event.get("participants", [])
                    ))
                })
            
            i = j if j > i + 1 else i + 1
        
        return parallel_groups
    
    def _trace_causal_chains(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """追踪因果链"""
        # 创建事件ID到事件的映射
        event_map = {event["id"]: event for event in events}
        
        # 找到所有因果链的起点（没有原因的事件）
        root_events = [event for event in events if not event.get("causes")]
        
        causal_chains = []
        for root_event in root_events:
            chain = self._build_causal_chain(root_event, event_map, set())
            if len(chain) > 1:  # 只保留有多个事件的链
                causal_chains.append({
                    "chain_id": f"chain_{len(causal_chains) + 1}",
                    "root_event_id": root_event["id"],
                    "root_event_title": root_event["title"],
                    "chain_length": len(chain),
                    "events": chain,
                    "total_importance": sum(event.get("importance", 5) for event in chain),
                    "time_span_hours": self._calculate_chain_time_span(chain),
                    "involved_characters": list(set(
                        char_id for event in chain 
                        for char_id in event.get("participants", [])
                    ))
                })
        
        # 按链长度排序
        causal_chains.sort(key=lambda x: x["chain_length"], reverse=True)
        
        return causal_chains
    
    def _build_causal_chain(self, event: Dict[str, Any], event_map: Dict[str, Dict[str, Any]], 
                           visited: set) -> List[Dict[str, Any]]:
        """递归构建因果链"""
        if event["id"] in visited:
            return []
        
        visited.add(event["id"])
        chain = [event]
        
        # 添加所有后果事件
        consequences = event.get("consequences", [])
        for consequence_id in consequences:
            if consequence_id in event_map:
                consequence_event = event_map[consequence_id]
                sub_chain = self._build_causal_chain(consequence_event, event_map, visited.copy())
                chain.extend(sub_chain)
        
        return chain
    
    def _calculate_chain_time_span(self, chain: List[Dict[str, Any]]) -> float:
        """计算因果链的时间跨度"""
        timestamps = [event["parsed_timestamp"] for event in chain if "parsed_timestamp" in event]
        if len(timestamps) < 2:
            return 0.0
        
        return (max(timestamps) - min(timestamps)).total_seconds() / 3600
    
    def _calculate_timeline_density(self, events: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算时间线密度"""
        if len(events) < 2:
            return {"density_score": 0, "classification": "sparse"}
        
        timestamps = [event["parsed_timestamp"] for event in events]
        total_time_span = (max(timestamps) - min(timestamps)).total_seconds() / 3600  # 小时
        
        if total_time_span == 0:
            return {"density_score": 1.0, "classification": "concentrated"}
        
        # 计算密度分数（事件数 / 时间跨度）
        density_score = len(events) / total_time_span
        
        # 分类密度
        if density_score > 1.0:
            classification = "very_dense"
        elif density_score > 0.5:
            classification = "dense"
        elif density_score > 0.1:
            classification = "moderate"
        elif density_score > 0.01:
            classification = "sparse"
        else:
            classification = "very_sparse"
        
        return {
            "density_score": round(density_score, 4),
            "classification": classification,
            "events_per_hour": round(density_score, 4),
            "average_interval_hours": round(total_time_span / (len(events) - 1), 2) if len(events) > 1 else 0
        }
    
    def _calculate_event_duration(self, event: Dict[str, Any]) -> Optional[float]:
        """计算事件持续时间"""
        duration_str = event.get("duration")
        if not duration_str:
            return None
        
        try:
            # 假设duration存储为秒数或ISO格式
            if isinstance(duration_str, (int, float)):
                return duration_str / 3600  # 转换为小时
            elif isinstance(duration_str, str):
                # 尝试解析ISO duration格式或简单的数字
                if duration_str.isdigit():
                    return float(duration_str) / 3600
                # 可以添加更复杂的duration解析逻辑
        except (ValueError, TypeError):
            pass
        
        return None
    
    def export_timeline_data(self, timeline_data: Dict[str, Any], format_type: str = "json") -> str:
        """导出时间线数据"""
        if format_type == "json":
            return json.dumps(timeline_data, ensure_ascii=False, indent=2, default=str)
        elif format_type == "csv":
            return self._export_to_csv(timeline_data)
        elif format_type == "markdown":
            return self._export_to_markdown(timeline_data)
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    def _export_to_csv(self, timeline_data: Dict[str, Any]) -> str:
        """导出为CSV格式"""
        events = timeline_data.get("events", [])
        if not events:
            return "No events to export"
        
        # CSV头部
        headers = ["ID", "Title", "Timestamp", "Event Type", "Importance", "Location", "Participants", "Description"]
        csv_lines = [",".join(headers)]
        
        # 事件数据
        for event in events:
            row = [
                event.get("id", ""),
                f'"{event.get("title", "")}"',
                event.get("timestamp", ""),
                event.get("event_type", ""),
                str(event.get("importance", "")),
                f'"{event.get("location", "")}"',
                f'"{"; ".join(event.get("participants", []))}"',
                f'"{event.get("description", "")}"'
            ]
            csv_lines.append(",".join(row))
        
        return "\n".join(csv_lines)
    
    def _export_to_markdown(self, timeline_data: Dict[str, Any]) -> str:
        """导出为Markdown格式"""
        events = timeline_data.get("events", [])
        metadata = timeline_data.get("metadata", {})
        periods = timeline_data.get("periods", [])
        
        md_lines = [
            "# Timeline Analysis",
            "",
            "## Overview",
            f"- Total Events: {metadata.get('total_events', 0)}",
            f"- Time Span: {metadata.get('total_duration_days', 0)} days",
            f"- Average Interval: {metadata.get('average_interval_hours', 0)} hours",
            ""
        ]
        
        # 时期摘要
        if periods:
            md_lines.extend([
                "## Time Periods",
                ""
            ])
            
            for period in periods:
                md_lines.extend([
                    f"### {period['name']}",
                    f"- Duration: {period['duration_days']} days",
                    f"- Events: {period['event_count']}",
                    f"- Dominant Type: {period['dominant_event_type']}",
                    f"- Average Importance: {period['average_importance']}",
                    ""
                ])
        
        # 事件列表
        md_lines.extend([
            "## Events Timeline",
            ""
        ])
        
        for event in events:
            md_lines.extend([
                f"### {event['title']}",
                f"- **Time**: {event['display_time']}",
                f"- **Type**: {event['event_type']}",
                f"- **Importance**: {event['importance']}",
                f"- **Location**: {event.get('location', 'N/A')}",
                f"- **Participants**: {', '.join(event.get('participants', []))}",
                f"- **Description**: {event.get('description', 'N/A')}",
                ""
            ])
        
        return "\n".join(md_lines)
    
    def generate_timeline_summary(self, timeline_data: Dict[str, Any]) -> str:
        """生成时间线摘要"""
        metadata = timeline_data.get("metadata", {})
        periods = timeline_data.get("periods", [])
        parallel_events = timeline_data.get("parallel_events", [])
        causal_chains = timeline_data.get("causal_chains", [])
        
        summary_parts = [
            f"时间线包含 {metadata.get('total_events', 0)} 个事件",
            f"时间跨度: {metadata.get('total_duration_days', 0)} 天",
            f"平均事件间隔: {metadata.get('average_interval_hours', 0)} 小时"
        ]
        
        if periods:
            summary_parts.append(f"识别出 {len(periods)} 个时间段")
        
        if parallel_events:
            summary_parts.append(f"发现 {len(parallel_events)} 组并行事件")
        
        if causal_chains:
            longest_chain = max(causal_chains, key=lambda x: x["chain_length"])
            summary_parts.append(f"最长因果链包含 {longest_chain['chain_length']} 个事件")
        
        density = metadata.get("timeline_density", {})
        if density:
            summary_parts.append(f"时间线密度: {density.get('classification', 'unknown')}")
        
        return "；".join(summary_parts)
