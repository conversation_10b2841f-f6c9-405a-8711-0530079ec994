"""
AI小说管理工具 - 数据验证工具
提供各种数据验证功能
"""

import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from ..core.exceptions import ValidationError
from ..config import get_config

def validate_character_data(data: Dict[str, Any]) -> List[str]:
    """验证人物数据"""
    errors = []
    validation_config = get_config("validation")
    
    # 验证必要字段
    if not data.get("name") or not data["name"].strip():
        errors.append("Character name is required")
    elif len(data["name"]) > validation_config["max_name_length"]:
        errors.append(f"Character name too long (max {validation_config['max_name_length']} chars)")
    
    # 验证年龄
    if "age" in data and data["age"] is not None:
        if not isinstance(data["age"], int) or data["age"] < 0 or data["age"] > 1000:
            errors.append("Character age must be a number between 0 and 1000")
    
    # 验证重要性
    if "importance" in data:
        if not isinstance(data["importance"], int) or data["importance"] < 1 or data["importance"] > 10:
            errors.append("Character importance must be between 1 and 10")
    
    # 验证文本长度
    text_fields = {
        "background": "background",
        "physical_description": "physical description"
    }
    
    for field, display_name in text_fields.items():
        if field in data and data[field]:
            if len(data[field]) > validation_config["max_description_length"]:
                errors.append(f"Character {display_name} too long")
    
    # 验证列表字段
    list_fields = ["aliases", "motivations", "fears", "goals", "distinctive_features", "tags"]
    for field in list_fields:
        if field in data and data[field] is not None:
            if not isinstance(data[field], list):
                errors.append(f"Character {field} must be a list")
    
    return errors

def validate_relationship_data(data: Dict[str, Any]) -> List[str]:
    """验证关系数据"""
    errors = []
    validation_config = get_config("validation")
    
    # 验证必要字段
    required_fields = ["character_a", "character_b", "relationship_type"]
    for field in required_fields:
        if not data.get(field):
            errors.append(f"Relationship {field} is required")
    
    # 验证关系强度
    if "strength" in data:
        strength = data["strength"]
        min_strength = validation_config["min_relationship_strength"]
        max_strength = validation_config["max_relationship_strength"]
        
        if not isinstance(strength, (int, float)) or strength < min_strength or strength > max_strength:
            errors.append(f"Relationship strength must be between {min_strength} and {max_strength}")
    
    # 验证不能与自己建立关系
    if data.get("character_a") == data.get("character_b"):
        errors.append("Character cannot have relationship with themselves")
    
    # 验证描述长度
    if "description" in data and data["description"]:
        if len(data["description"]) > validation_config["max_description_length"]:
            errors.append("Relationship description too long")
    
    return errors

def validate_event_data(data: Dict[str, Any]) -> List[str]:
    """验证事件数据"""
    errors = []
    validation_config = get_config("validation")
    
    # 验证必要字段
    if not data.get("title") or not data["title"].strip():
        errors.append("Event title is required")
    elif len(data["title"]) > validation_config["max_name_length"]:
        errors.append(f"Event title too long (max {validation_config['max_name_length']} chars)")
    
    # 验证重要性
    if "importance" in data:
        if not isinstance(data["importance"], int) or data["importance"] < 1 or data["importance"] > validation_config["max_importance_level"]:
            errors.append(f"Event importance must be between 1 and {validation_config['max_importance_level']}")
    
    # 验证描述长度
    if "description" in data and data["description"]:
        if len(data["description"]) > validation_config["max_description_length"]:
            errors.append("Event description too long")
    
    # 验证列表字段
    list_fields = ["participants", "consequences", "causes", "tags"]
    for field in list_fields:
        if field in data and data[field] is not None:
            if not isinstance(data[field], list):
                errors.append(f"Event {field} must be a list")
    
    # 验证时间戳格式
    if "story_timestamp" in data and data["story_timestamp"]:
        try:
            if isinstance(data["story_timestamp"], str):
                datetime.fromisoformat(data["story_timestamp"])
        except ValueError:
            errors.append("Invalid story timestamp format")
    
    return errors

def validate_world_setting_data(data: Dict[str, Any]) -> List[str]:
    """验证世界观设定数据"""
    errors = []
    validation_config = get_config("validation")
    
    # 验证必要字段
    required_fields = ["category", "name"]
    for field in required_fields:
        if not data.get(field) or not data[field].strip():
            errors.append(f"World setting {field} is required")
    
    # 验证名称长度
    if data.get("name") and len(data["name"]) > validation_config["max_name_length"]:
        errors.append(f"World setting name too long (max {validation_config['max_name_length']} chars)")
    
    # 验证描述长度
    if "description" in data and data["description"]:
        if len(data["description"]) > validation_config["max_description_length"]:
            errors.append("World setting description too long")
    
    # 验证版本号
    if "version" in data:
        if not isinstance(data["version"], int) or data["version"] < 1:
            errors.append("World setting version must be a positive integer")
    
    # 验证列表字段
    list_fields = ["rules", "related_settings", "consistency_notes", "tags"]
    for field in list_fields:
        if field in data and data[field] is not None:
            if not isinstance(data[field], list):
                errors.append(f"World setting {field} must be a list")
    
    return errors

def validate_creative_note_data(data: Dict[str, Any]) -> List[str]:
    """验证创作笔记数据"""
    errors = []
    validation_config = get_config("validation")
    
    # 验证必要字段
    if not data.get("title") or not data["title"].strip():
        errors.append("Creative note title is required")
    elif len(data["title"]) > validation_config["max_name_length"]:
        errors.append(f"Creative note title too long (max {validation_config['max_name_length']} chars)")
    
    # 验证内容长度
    if "content" in data and data["content"]:
        if len(data["content"]) > validation_config["max_content_length"]:
            errors.append("Creative note content too long")
    
    # 验证优先级
    if "priority" in data:
        if not isinstance(data["priority"], int) or data["priority"] < 1 or data["priority"] > 5:
            errors.append("Creative note priority must be between 1 and 5")
    
    # 验证列表字段
    list_fields = ["related_characters", "related_events", "related_settings", "tags"]
    for field in list_fields:
        if field in data and data[field] is not None:
            if not isinstance(data[field], list):
                errors.append(f"Creative note {field} must be a list")
    
    return errors

def validate_id_format(entity_id: str) -> bool:
    """验证ID格式"""
    if not entity_id or not isinstance(entity_id, str):
        return False
    
    # 简单的UUID格式验证
    uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    return bool(re.match(uuid_pattern, entity_id, re.IGNORECASE))

def validate_text_content(text: str, max_length: int = None, min_length: int = 0) -> List[str]:
    """验证文本内容"""
    errors = []
    
    if not isinstance(text, str):
        errors.append("Text content must be a string")
        return errors
    
    if len(text) < min_length:
        errors.append(f"Text content too short (minimum {min_length} characters)")
    
    if max_length and len(text) > max_length:
        errors.append(f"Text content too long (maximum {max_length} characters)")
    
    return errors

def validate_numeric_range(value: Union[int, float], min_val: Union[int, float], 
                          max_val: Union[int, float], field_name: str = "Value") -> List[str]:
    """验证数值范围"""
    errors = []
    
    if not isinstance(value, (int, float)):
        errors.append(f"{field_name} must be a number")
        return errors
    
    if value < min_val or value > max_val:
        errors.append(f"{field_name} must be between {min_val} and {max_val}")
    
    return errors
