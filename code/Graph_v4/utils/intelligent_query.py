"""
AI小说管理工具 - 智能查询系统
提供自然语言查询解析和处理功能
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
from datetime import datetime

class QueryParser:
    """自然语言查询解析器"""
    
    def __init__(self):
        """初始化查询解析器"""
        self.logger = logging.getLogger(__name__)
        
        # 查询意图模式
        self.intent_patterns = {
            "search_character": [
                r"(?:找|查找|搜索|寻找).*?(?:人物|角色|人|character)",
                r"(?:人物|角色|character).*?(?:叫|名字|姓名)",
                r"(?:谁|who).*?(?:是|叫|名字)",
                r"(?:有没有|是否有).*?(?:人物|角色|人)"
            ],
            "search_relationship": [
                r"(?:关系|relationship).*?(?:是什么|如何|怎样)",
                r"(?:和|与|跟).*?(?:关系|认识|相识)",
                r"(?:谁|who).*?(?:认识|知道|了解)",
                r"(?:朋友|敌人|恋人|家人|师父|徒弟)"
            ],
            "search_event": [
                r"(?:事件|情节|剧情|plot|event)",
                r"(?:发生|happened).*?(?:什么|事情|事件)",
                r"(?:什么时候|when).*?(?:发生|happened)",
                r"(?:在哪里|where).*?(?:发生|happened)"
            ],
            "search_worldbuilding": [
                r"(?:世界观|设定|worldbuilding|world)",
                r"(?:规则|rule).*?(?:是什么|如何|怎样)",
                r"(?:地点|location|place).*?(?:在哪|where)",
                r"(?:背景|background|setting)"
            ],
            "search_note": [
                r"(?:笔记|note|记录|想法|idea)",
                r"(?:记录|记下|写下).*?(?:什么|内容)",
                r"(?:灵感|inspiration|创意)"
            ],
            "analyze_network": [
                r"(?:网络|network|关系图)",
                r"(?:分析|analyze).*?(?:关系|network)",
                r"(?:中心|central|重要).*?(?:人物|角色)",
                r"(?:影响力|influence|重要性)"
            ],
            "analyze_timeline": [
                r"(?:时间线|timeline|时间轴)",
                r"(?:时间|time).*?(?:顺序|order|sequence)",
                r"(?:先后|before|after).*?(?:发生|happened)",
                r"(?:什么时候|when).*?(?:开始|结束|发生)"
            ],
            "get_statistics": [
                r"(?:统计|statistics|数据|data)",
                r"(?:有多少|how many|count)",
                r"(?:总共|total|altogether)",
                r"(?:概览|overview|summary|总结)"
            ]
        }
        
        # 实体提取模式
        self.entity_patterns = {
            "character_name": [
                r"(?:人物|角色|character)(?:叫|名字|姓名|是)[\s\"\']*([^\s\"\'，。！？]+)",
                r"([^\s，。！？]+)(?:这个|这位)(?:人物|角色|人)",
                r"\"([^\"]+)\"(?:这个|这位)?(?:人物|角色|人)?",
                r"\'([^\']+)\'(?:这个|这位)?(?:人物|角色|人)?"
            ],
            "relationship_type": [
                r"(朋友|friend|友谊|friendship)",
                r"(敌人|enemy|仇人|对手|rival)",
                r"(恋人|lover|情侣|romantic)",
                r"(家人|family|亲人|父母|兄弟|姐妹)",
                r"(师父|master|老师|teacher|mentor)",
                r"(徒弟|student|学生|弟子)",
                r"(同事|colleague|伙伴|partner|ally)"
            ],
            "event_type": [
                r"(战斗|battle|fight|conflict)",
                r"(会面|meeting|encounter)",
                r"(离别|departure|farewell)",
                r"(成长|growth|development)",
                r"(转折|turning|climax)",
                r"(开始|beginning|start)",
                r"(结束|ending|conclusion)"
            ],
            "time_reference": [
                r"(\d{4}年|\d{1,2}月|\d{1,2}日)",
                r"(昨天|今天|明天|yesterday|today|tomorrow)",
                r"(上周|本周|下周|last week|this week|next week)",
                r"(之前|之后|before|after)",
                r"(开始|结束|start|end)"
            ],
            "location": [
                r"在([^\s，。！？]+)(?:发生|happened)",
                r"(?:地点|location|place)(?:是|在)([^\s，。！？]+)",
                r"([^\s，。！？]+)(?:这个|这里|那里)(?:地方|地点)"
            ]
        }
        
        # 查询修饰符
        self.modifiers = {
            "limit": [
                r"(?:前|top|最多)(\d+)(?:个|位|条)",
                r"(\d+)(?:个|位|条)(?:最|top)"
            ],
            "importance": [
                r"(?:重要|important|关键|key|主要|main)",
                r"(?:次要|secondary|不重要|minor)"
            ],
            "time_order": [
                r"(?:按时间|chronological|时间顺序)",
                r"(?:最新|latest|recent|最近)",
                r"(?:最早|earliest|oldest|最老)"
            ]
        }
    
    def parse_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """解析自然语言查询"""
        try:
            # 预处理查询
            normalized_query = self._normalize_query(query)
            
            # 识别查询意图
            intent = self._identify_intent(normalized_query)
            
            # 提取实体
            entities = self._extract_entities(normalized_query)
            
            # 提取修饰符
            modifiers = self._extract_modifiers(normalized_query)
            
            # 构建结构化查询
            structured_query = self._build_structured_query(intent, entities, modifiers, context)
            
            return {
                "success": True,
                "original_query": query,
                "normalized_query": normalized_query,
                "intent": intent,
                "entities": entities,
                "modifiers": modifiers,
                "structured_query": structured_query,
                "confidence": self._calculate_confidence(intent, entities)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to parse query: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "original_query": query
            }
    
    def _normalize_query(self, query: str) -> str:
        """标准化查询文本"""
        # 转换为小写
        normalized = query.lower()
        
        # 移除多余空格
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        # 标准化标点符号
        normalized = re.sub(r'[，。！？；：]', ' ', normalized)
        
        return normalized
    
    def _identify_intent(self, query: str) -> Dict[str, Any]:
        """识别查询意图"""
        intent_scores = defaultdict(float)
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, query, re.IGNORECASE)
                if matches:
                    intent_scores[intent] += len(matches) * 1.0
        
        if not intent_scores:
            return {"type": "unknown", "confidence": 0.0}
        
        # 找到最高分的意图
        best_intent = max(intent_scores.items(), key=lambda x: x[1])
        
        return {
            "type": best_intent[0],
            "confidence": min(best_intent[1] / 2.0, 1.0),  # 标准化到0-1
            "all_scores": dict(intent_scores)
        }
    
    def _extract_entities(self, query: str) -> Dict[str, List[str]]:
        """提取实体"""
        entities = defaultdict(list)
        
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, query, re.IGNORECASE)
                if matches:
                    entities[entity_type].extend(matches)
        
        # 去重
        for entity_type in entities:
            entities[entity_type] = list(set(entities[entity_type]))
        
        return dict(entities)
    
    def _extract_modifiers(self, query: str) -> Dict[str, Any]:
        """提取查询修饰符"""
        modifiers = {}
        
        for modifier_type, patterns in self.modifiers.items():
            for pattern in patterns:
                matches = re.findall(pattern, query, re.IGNORECASE)
                if matches:
                    if modifier_type == "limit":
                        modifiers["limit"] = int(matches[0])
                    elif modifier_type == "importance":
                        modifiers["importance_filter"] = True
                    elif modifier_type == "time_order":
                        modifiers["order_by_time"] = True
        
        return modifiers
    
    def _build_structured_query(self, intent: Dict[str, Any], entities: Dict[str, List[str]], 
                               modifiers: Dict[str, Any], context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """构建结构化查询"""
        structured = {
            "action": intent["type"],
            "parameters": {},
            "filters": {},
            "options": {}
        }
        
        # 根据意图类型构建参数
        if intent["type"] == "search_character":
            if "character_name" in entities:
                structured["parameters"]["name"] = entities["character_name"][0]
            structured["filters"].update(self._build_character_filters(entities))
            
        elif intent["type"] == "search_relationship":
            if "character_name" in entities:
                structured["parameters"]["character_id"] = entities["character_name"][0]
            if "relationship_type" in entities:
                structured["filters"]["relationship_type"] = entities["relationship_type"][0]
                
        elif intent["type"] == "search_event":
            structured["filters"].update(self._build_event_filters(entities))
            
        elif intent["type"] == "search_worldbuilding":
            structured["filters"].update(self._build_worldbuilding_filters(entities))
            
        elif intent["type"] == "search_note":
            if "character_name" in entities:
                structured["filters"]["related_characters"] = entities["character_name"]
                
        elif intent["type"] == "analyze_network":
            structured["action"] = "get_relationship_network"
            if "character_name" in entities:
                structured["parameters"]["character_id"] = entities["character_name"][0]
                
        elif intent["type"] == "analyze_timeline":
            structured["action"] = "get_timeline"
            if "character_name" in entities:
                structured["parameters"]["character_id"] = entities["character_name"][0]
        
        # 添加修饰符
        structured["options"].update(modifiers)
        
        # 添加上下文信息
        if context:
            structured["context"] = context
        
        return structured
    
    def _build_character_filters(self, entities: Dict[str, List[str]]) -> Dict[str, Any]:
        """构建人物过滤条件"""
        filters = {}
        
        if "relationship_type" in entities:
            # 如果提到关系类型，可能是在查找有特定关系的人物
            filters["has_relationship_type"] = entities["relationship_type"][0]
        
        return filters
    
    def _build_event_filters(self, entities: Dict[str, List[str]]) -> Dict[str, Any]:
        """构建事件过滤条件"""
        filters = {}
        
        if "event_type" in entities:
            filters["event_type"] = entities["event_type"][0]
        
        if "character_name" in entities:
            filters["participants"] = entities["character_name"]
        
        if "location" in entities:
            filters["location"] = entities["location"][0]
        
        if "time_reference" in entities:
            filters["time_reference"] = entities["time_reference"][0]
        
        return filters
    
    def _build_worldbuilding_filters(self, entities: Dict[str, List[str]]) -> Dict[str, Any]:
        """构建世界观过滤条件"""
        filters = {}
        
        if "location" in entities:
            filters["name"] = entities["location"][0]
        
        return filters
    
    def _calculate_confidence(self, intent: Dict[str, Any], entities: Dict[str, List[str]]) -> float:
        """计算查询解析的置信度"""
        base_confidence = intent.get("confidence", 0.0)
        
        # 如果提取到实体，增加置信度
        entity_bonus = min(len(entities) * 0.1, 0.3)
        
        # 如果意图明确且有相关实体，进一步增加置信度
        if intent["type"] != "unknown" and entities:
            entity_bonus += 0.2
        
        return min(base_confidence + entity_bonus, 1.0)


class IntelligentQueryProcessor:
    """智能查询处理器"""
    
    def __init__(self, api):
        """初始化查询处理器"""
        self.api = api
        self.parser = QueryParser()
        self.logger = logging.getLogger(__name__)
    
    def process_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """处理智能查询"""
        try:
            # 解析查询
            parse_result = self.parser.parse_query(query, context)
            
            if not parse_result["success"]:
                return parse_result
            
            # 执行结构化查询
            execution_result = self._execute_structured_query(parse_result["structured_query"])
            
            # 合并结果
            return {
                "success": True,
                "query_analysis": parse_result,
                "results": execution_result,
                "summary": self._generate_summary(parse_result, execution_result)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to process query: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "query": query
            }
    
    def _execute_structured_query(self, structured_query: Dict[str, Any]) -> Dict[str, Any]:
        """执行结构化查询"""
        action = structured_query["action"]
        parameters = structured_query.get("parameters", {})
        filters = structured_query.get("filters", {})
        options = structured_query.get("options", {})
        
        # 合并参数和过滤条件
        query_params = {**parameters, **filters, **options}
        
        # 根据动作类型调用相应的API方法
        if action == "search_character":
            return self.api.search_characters(query_params)
        elif action == "search_relationship":
            return self.api.search_relationships(query_params)
        elif action == "search_event":
            return self.api.search_events(query_params)
        elif action == "search_worldbuilding":
            return self.api.search_world_settings(query_params)
        elif action == "search_note":
            return self.api.search_notes(query_params)
        elif action == "get_relationship_network":
            return self.api.get_relationship_network(query_params)
        elif action == "get_timeline":
            return self.api.get_timeline(query_params)
        elif action == "get_statistics":
            return self.api.get_statistics()
        else:
            return {
                "success": False,
                "error": f"Unknown action: {action}"
            }
    
    def _generate_summary(self, parse_result: Dict[str, Any], execution_result: Dict[str, Any]) -> str:
        """生成查询结果摘要"""
        intent = parse_result["intent"]["type"]
        confidence = parse_result["confidence"]
        
        if not execution_result.get("success", False):
            return f"查询解析成功（置信度: {confidence:.2f}），但执行失败: {execution_result.get('error', '未知错误')}"
        
        # 根据查询类型生成不同的摘要
        if intent == "search_character":
            count = len(execution_result.get("characters", []))
            return f"找到 {count} 个符合条件的人物"
        elif intent == "search_relationship":
            count = len(execution_result.get("relationships", []))
            return f"找到 {count} 个符合条件的关系"
        elif intent == "search_event":
            count = len(execution_result.get("events", []))
            return f"找到 {count} 个符合条件的事件"
        elif intent == "search_worldbuilding":
            count = len(execution_result.get("world_settings", []))
            return f"找到 {count} 个符合条件的世界观设定"
        elif intent == "search_note":
            count = len(execution_result.get("notes", []))
            return f"找到 {count} 个符合条件的笔记"
        elif intent == "get_statistics":
            return "获取系统统计信息成功"
        else:
            return f"查询执行成功（置信度: {confidence:.2f}）"
