"""
AI小说管理工具 - 高级搜索引擎
提供全文搜索、模糊匹配和相关性排序功能
"""

import re
import json
import math
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from collections import defaultdict, Counter
from difflib import SequenceMatcher
import unicodedata

class SearchEngine:
    """高级搜索引擎"""
    
    def __init__(self):
        """初始化搜索引擎"""
        self.logger = logging.getLogger(__name__)
        
        # 停用词列表
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of',
            'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had'
        }
        
        # 搜索索引
        self.indexes = {
            'characters': {},
            'relationships': {},
            'events': {},
            'world_settings': {},
            'notes': {}
        }
        
        # 文档频率统计
        self.document_frequencies = defaultdict(int)
        self.total_documents = 0
    
    def build_index(self, data_type: str, documents: List[Dict[str, Any]]):
        """构建搜索索引"""
        try:
            self.logger.info(f"Building search index for {data_type}...")
            
            index = defaultdict(list)
            
            for doc in documents:
                doc_id = doc.get('id', '')
                if not doc_id:
                    continue
                
                # 提取文档文本
                text_content = self._extract_text_content(doc, data_type)
                
                # 分词和标准化
                tokens = self._tokenize_and_normalize(text_content)
                
                # 计算词频
                token_counts = Counter(tokens)
                
                # 添加到索引
                for token, count in token_counts.items():
                    if token not in self.stop_words:
                        index[token].append({
                            'doc_id': doc_id,
                            'count': count,
                            'document': doc
                        })
                        self.document_frequencies[token] += 1
            
            self.indexes[data_type] = dict(index)
            self.total_documents += len(documents)
            
            self.logger.info(f"Index built for {data_type}: {len(documents)} documents, {len(index)} unique terms")
            
        except Exception as e:
            self.logger.error(f"Failed to build index for {data_type}: {str(e)}")
    
    def search(self, query: str, data_types: Optional[List[str]] = None, 
              max_results: int = 50, fuzzy_threshold: float = 0.6) -> Dict[str, Any]:
        """执行搜索"""
        try:
            if not query.strip():
                return {
                    "success": False,
                    "error": "Empty query"
                }
            
            # 默认搜索所有类型
            if data_types is None:
                data_types = list(self.indexes.keys())
            
            # 分词和标准化查询
            query_tokens = self._tokenize_and_normalize(query)
            query_tokens = [token for token in query_tokens if token not in self.stop_words]
            
            if not query_tokens:
                return {
                    "success": False,
                    "error": "No valid search terms after filtering stop words"
                }
            
            # 执行搜索
            all_results = []
            
            for data_type in data_types:
                if data_type in self.indexes:
                    results = self._search_in_index(query_tokens, data_type, fuzzy_threshold)
                    for result in results:
                        result['data_type'] = data_type
                    all_results.extend(results)
            
            # 按相关性排序
            all_results.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            # 限制结果数量
            limited_results = all_results[:max_results]
            
            return {
                "success": True,
                "query": query,
                "query_tokens": query_tokens,
                "total_results": len(all_results),
                "returned_results": len(limited_results),
                "results": limited_results,
                "search_stats": self._generate_search_stats(query_tokens, all_results)
            }
            
        except Exception as e:
            self.logger.error(f"Search failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "query": query
            }
    
    def suggest_completions(self, partial_query: str, max_suggestions: int = 10) -> List[str]:
        """查询自动补全建议"""
        try:
            if len(partial_query) < 2:
                return []
            
            partial_query = partial_query.lower().strip()
            suggestions = set()
            
            # 在所有索引中查找匹配的词汇
            for data_type, index in self.indexes.items():
                for term in index.keys():
                    if term.startswith(partial_query):
                        suggestions.add(term)
                    elif self._fuzzy_match(partial_query, term) > 0.7:
                        suggestions.add(term)
            
            # 按长度和相似度排序
            sorted_suggestions = sorted(suggestions, key=lambda x: (len(x), -self._fuzzy_match(partial_query, x)))
            
            return sorted_suggestions[:max_suggestions]
            
        except Exception as e:
            self.logger.error(f"Failed to generate suggestions: {str(e)}")
            return []
    
    def _extract_text_content(self, document: Dict[str, Any], data_type: str) -> str:
        """提取文档的文本内容"""
        text_parts = []
        
        if data_type == 'characters':
            text_parts.extend([
                document.get('name', ''),
                document.get('background', ''),
                document.get('appearance', ''),
                document.get('role', ''),
                ' '.join(document.get('personality_traits', [])),
                ' '.join(document.get('aliases', []))
            ])
        
        elif data_type == 'relationships':
            text_parts.extend([
                document.get('relationship_type', ''),
                document.get('description', ''),
                document.get('character_a', ''),
                document.get('character_b', '')
            ])
        
        elif data_type == 'events':
            text_parts.extend([
                document.get('title', ''),
                document.get('description', ''),
                document.get('event_type', ''),
                document.get('location', ''),
                ' '.join(document.get('participants', []))
            ])
        
        elif data_type == 'world_settings':
            text_parts.extend([
                document.get('name', ''),
                document.get('description', ''),
                document.get('category', ''),
                ' '.join(document.get('rules', [])),
                ' '.join(document.get('related_settings', []))
            ])
        
        elif data_type == 'notes':
            text_parts.extend([
                document.get('title', ''),
                document.get('content', ''),
                document.get('note_type', ''),
                ' '.join(document.get('tags', []))
            ])
        
        return ' '.join(filter(None, text_parts))
    
    def _tokenize_and_normalize(self, text: str) -> List[str]:
        """分词和标准化"""
        if not text:
            return []
        
        # 标准化Unicode
        text = unicodedata.normalize('NFKC', text)
        
        # 转换为小写
        text = text.lower()
        
        # 移除标点符号，保留中文、英文和数字
        text = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', text)
        
        # 分词（简单的空格分词，可以集成更复杂的中文分词器）
        tokens = text.split()
        
        # 过滤短词
        tokens = [token for token in tokens if len(token) >= 2]
        
        return tokens
    
    def _search_in_index(self, query_tokens: List[str], data_type: str, 
                        fuzzy_threshold: float) -> List[Dict[str, Any]]:
        """在指定索引中搜索"""
        index = self.indexes[data_type]
        document_scores = defaultdict(float)
        document_matches = defaultdict(set)
        document_data = {}
        
        for query_token in query_tokens:
            # 精确匹配
            if query_token in index:
                for entry in index[query_token]:
                    doc_id = entry['doc_id']
                    tf_idf = self._calculate_tf_idf(entry['count'], self.document_frequencies[query_token])
                    document_scores[doc_id] += tf_idf
                    document_matches[doc_id].add(query_token)
                    document_data[doc_id] = entry['document']
            
            # 模糊匹配
            else:
                for term in index.keys():
                    similarity = self._fuzzy_match(query_token, term)
                    if similarity >= fuzzy_threshold:
                        for entry in index[term]:
                            doc_id = entry['doc_id']
                            tf_idf = self._calculate_tf_idf(entry['count'], self.document_frequencies[term])
                            # 模糊匹配的分数要打折扣
                            document_scores[doc_id] += tf_idf * similarity * 0.8
                            document_matches[doc_id].add(f"{query_token}~{term}")
                            document_data[doc_id] = entry['document']
        
        # 构建结果
        results = []
        for doc_id, score in document_scores.items():
            # 计算匹配度加成
            match_ratio = len(document_matches[doc_id]) / len(query_tokens)
            final_score = score * (1 + match_ratio)
            
            results.append({
                'document_id': doc_id,
                'document': document_data[doc_id],
                'relevance_score': final_score,
                'matched_terms': list(document_matches[doc_id]),
                'match_ratio': match_ratio
            })
        
        return results
    
    def _calculate_tf_idf(self, term_frequency: int, document_frequency: int) -> float:
        """计算TF-IDF分数"""
        if document_frequency == 0:
            return 0.0
        
        tf = 1 + math.log(term_frequency)  # 对数TF
        idf = math.log(self.total_documents / document_frequency)  # IDF
        
        return tf * idf
    
    def _fuzzy_match(self, s1: str, s2: str) -> float:
        """计算模糊匹配相似度"""
        return SequenceMatcher(None, s1, s2).ratio()
    
    def _generate_search_stats(self, query_tokens: List[str], results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成搜索统计信息"""
        stats = {
            "query_token_count": len(query_tokens),
            "total_matches": len(results),
            "data_type_distribution": defaultdict(int),
            "average_relevance": 0.0,
            "top_matched_terms": []
        }
        
        if results:
            # 数据类型分布
            for result in results:
                stats["data_type_distribution"][result.get('data_type', 'unknown')] += 1
            
            # 平均相关性
            total_relevance = sum(result['relevance_score'] for result in results)
            stats["average_relevance"] = total_relevance / len(results)
            
            # 最常匹配的词汇
            all_matched_terms = []
            for result in results:
                all_matched_terms.extend(result.get('matched_terms', []))
            
            term_counts = Counter(all_matched_terms)
            stats["top_matched_terms"] = term_counts.most_common(10)
        
        stats["data_type_distribution"] = dict(stats["data_type_distribution"])
        
        return stats
    
    def get_index_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        stats = {
            "total_documents": self.total_documents,
            "total_unique_terms": len(self.document_frequencies),
            "index_sizes": {},
            "top_terms": []
        }
        
        for data_type, index in self.indexes.items():
            stats["index_sizes"][data_type] = len(index)
        
        # 最常见的词汇
        stats["top_terms"] = self.document_frequencies.most_common(20)
        
        return stats
    
    def clear_indexes(self):
        """清空所有索引"""
        self.indexes = {
            'characters': {},
            'relationships': {},
            'events': {},
            'world_settings': {},
            'notes': {}
        }
        self.document_frequencies.clear()
        self.total_documents = 0
        
        self.logger.info("All search indexes cleared")


class AdvancedSearchManager:
    """高级搜索管理器"""
    
    def __init__(self, api):
        """初始化搜索管理器"""
        self.api = api
        self.search_engine = SearchEngine()
        self.logger = logging.getLogger(__name__)
        self._index_built = False
    
    def rebuild_indexes(self) -> Dict[str, Any]:
        """重建搜索索引"""
        try:
            self.logger.info("Rebuilding search indexes...")
            
            # 清空现有索引
            self.search_engine.clear_indexes()
            
            # 获取所有数据并构建索引
            data_types = ['characters', 'relationships', 'events', 'world_settings', 'notes']
            
            for data_type in data_types:
                # 获取数据
                if data_type == 'characters':
                    result = self.api.search_characters({})
                    documents = result.get('characters', []) if result.get('success') else []
                elif data_type == 'relationships':
                    result = self.api.search_relationships({})
                    documents = result.get('relationships', []) if result.get('success') else []
                elif data_type == 'events':
                    result = self.api.search_events({})
                    documents = result.get('events', []) if result.get('success') else []
                elif data_type == 'world_settings':
                    result = self.api.search_world_settings({})
                    documents = result.get('world_settings', []) if result.get('success') else []
                elif data_type == 'notes':
                    result = self.api.search_notes({})
                    documents = result.get('notes', []) if result.get('success') else []
                else:
                    documents = []
                
                # 构建索引
                if documents:
                    self.search_engine.build_index(data_type, documents)
            
            self._index_built = True
            
            # 获取索引统计
            stats = self.search_engine.get_index_stats()
            
            return {
                "success": True,
                "message": "Search indexes rebuilt successfully",
                "index_stats": stats
            }
            
        except Exception as e:
            self.logger.error(f"Failed to rebuild indexes: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def full_text_search(self, query: str, data_types: Optional[List[str]] = None,
                        max_results: int = 50, fuzzy_threshold: float = 0.6) -> Dict[str, Any]:
        """全文搜索"""
        try:
            # 确保索引已构建
            if not self._index_built:
                rebuild_result = self.rebuild_indexes()
                if not rebuild_result["success"]:
                    return rebuild_result
            
            # 执行搜索
            return self.search_engine.search(query, data_types, max_results, fuzzy_threshold)
            
        except Exception as e:
            self.logger.error(f"Full text search failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "query": query
            }
    
    def get_search_suggestions(self, partial_query: str, max_suggestions: int = 10) -> Dict[str, Any]:
        """获取搜索建议"""
        try:
            # 确保索引已构建
            if not self._index_built:
                rebuild_result = self.rebuild_indexes()
                if not rebuild_result["success"]:
                    return rebuild_result
            
            suggestions = self.search_engine.suggest_completions(partial_query, max_suggestions)
            
            return {
                "success": True,
                "partial_query": partial_query,
                "suggestions": suggestions,
                "suggestion_count": len(suggestions)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get search suggestions: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "partial_query": partial_query
            }
