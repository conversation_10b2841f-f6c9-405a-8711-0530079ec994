"""
AI小说管理工具 - 高级数据导出管理器
支持多种格式导出和自定义模板
"""

import json
import csv
import io
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

class ExportManager:
    """高级数据导出管理器"""
    
    def __init__(self):
        """初始化导出管理器"""
        self.logger = logging.getLogger(__name__)
        
        # 支持的导出格式
        self.supported_formats = {
            'json': self._export_json,
            'csv': self._export_csv,
            'markdown': self._export_markdown,
            'html': self._export_html,
            'txt': self._export_txt,
            'xml': self._export_xml
        }
        
        # 默认模板
        self.templates = {
            'character_profile': self._get_character_profile_template(),
            'relationship_summary': self._get_relationship_summary_template(),
            'event_timeline': self._get_event_timeline_template(),
            'world_guide': self._get_world_guide_template(),
            'story_bible': self._get_story_bible_template()
        }
    
    def export_data(self, data: List[Dict[str, Any]], data_type: str, 
                   export_format: str, template: Optional[str] = None,
                   custom_fields: Optional[List[str]] = None,
                   sort_by: Optional[str] = None,
                   filter_conditions: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """导出数据"""
        try:
            if export_format not in self.supported_formats:
                return {
                    "success": False,
                    "error": f"Unsupported format: {export_format}",
                    "supported_formats": list(self.supported_formats.keys())
                }
            
            # 过滤数据
            if filter_conditions:
                data = self._filter_data(data, filter_conditions)
            
            # 排序数据
            if sort_by:
                data = self._sort_data(data, sort_by)
            
            # 选择字段
            if custom_fields:
                data = self._select_fields(data, custom_fields)
            
            # 应用模板
            if template and template in self.templates:
                data = self._apply_template(data, data_type, template)
            
            # 执行导出
            export_func = self.supported_formats[export_format]
            export_result = export_func(data, data_type)
            
            return {
                "success": True,
                "format": export_format,
                "data_type": data_type,
                "template": template,
                "record_count": len(data),
                "export_data": export_result,
                "timestamp": datetime.now().isoformat(),
                "message": f"Successfully exported {len(data)} {data_type} records in {export_format} format"
            }
            
        except Exception as e:
            self.logger.error(f"Export failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "format": export_format,
                "data_type": data_type
            }
    
    def _export_json(self, data: List[Dict[str, Any]], data_type: str) -> str:
        """导出为JSON格式"""
        export_data = {
            "metadata": {
                "data_type": data_type,
                "export_time": datetime.now().isoformat(),
                "record_count": len(data),
                "format": "json"
            },
            "data": data
        }
        
        return json.dumps(export_data, ensure_ascii=False, indent=2, default=str)
    
    def _export_csv(self, data: List[Dict[str, Any]], data_type: str) -> str:
        """导出为CSV格式"""
        if not data:
            return "No data to export"
        
        output = io.StringIO()
        
        # 获取所有字段
        all_fields = set()
        for item in data:
            all_fields.update(item.keys())
        
        fieldnames = sorted(all_fields)
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        
        # 写入标题行
        writer.writeheader()
        
        # 写入数据行
        for item in data:
            # 处理复杂字段
            processed_item = {}
            for key, value in item.items():
                if isinstance(value, (list, dict)):
                    processed_item[key] = json.dumps(value, ensure_ascii=False)
                else:
                    processed_item[key] = str(value) if value is not None else ""
            writer.writerow(processed_item)
        
        return output.getvalue()
    
    def _export_markdown(self, data: List[Dict[str, Any]], data_type: str) -> str:
        """导出为Markdown格式"""
        lines = []
        
        # 标题
        lines.append(f"# {data_type.title()} Export")
        lines.append(f"")
        lines.append(f"**Export Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"**Record Count:** {len(data)}")
        lines.append(f"")
        
        if not data:
            lines.append("No data to export.")
            return "\n".join(lines)
        
        # 根据数据类型生成不同的Markdown格式
        if data_type == "characters":
            lines.extend(self._export_characters_markdown(data))
        elif data_type == "relationships":
            lines.extend(self._export_relationships_markdown(data))
        elif data_type == "events":
            lines.extend(self._export_events_markdown(data))
        elif data_type == "world_settings":
            lines.extend(self._export_world_settings_markdown(data))
        elif data_type == "notes":
            lines.extend(self._export_notes_markdown(data))
        else:
            # 通用格式
            lines.extend(self._export_generic_markdown(data))
        
        return "\n".join(lines)
    
    def _export_html(self, data: List[Dict[str, Any]], data_type: str) -> str:
        """导出为HTML格式"""
        html_parts = []
        
        # HTML头部
        html_parts.append("<!DOCTYPE html>")
        html_parts.append("<html lang='zh-CN'>")
        html_parts.append("<head>")
        html_parts.append("    <meta charset='UTF-8'>")
        html_parts.append("    <meta name='viewport' content='width=device-width, initial-scale=1.0'>")
        html_parts.append(f"    <title>{data_type.title()} Export</title>")
        html_parts.append("    <style>")
        html_parts.append(self._get_html_styles())
        html_parts.append("    </style>")
        html_parts.append("</head>")
        html_parts.append("<body>")
        
        # 内容
        html_parts.append(f"    <h1>{data_type.title()} Export</h1>")
        html_parts.append(f"    <p><strong>Export Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>")
        html_parts.append(f"    <p><strong>Record Count:</strong> {len(data)}</p>")
        
        if data:
            html_parts.append("    <div class='data-container'>")
            
            for i, item in enumerate(data):
                html_parts.append(f"        <div class='data-item' id='item-{i}'>")
                html_parts.append(f"            <h3>{item.get('name', item.get('title', f'Item {i+1}'))}</h3>")
                
                for key, value in item.items():
                    if key not in ['id', 'created_at', 'updated_at']:
                        html_parts.append(f"            <p><strong>{key.replace('_', ' ').title()}:</strong> {self._format_html_value(value)}</p>")
                
                html_parts.append("        </div>")
            
            html_parts.append("    </div>")
        else:
            html_parts.append("    <p>No data to export.</p>")
        
        # HTML尾部
        html_parts.append("</body>")
        html_parts.append("</html>")
        
        return "\n".join(html_parts)
    
    def _export_txt(self, data: List[Dict[str, Any]], data_type: str) -> str:
        """导出为纯文本格式"""
        lines = []
        
        # 标题
        lines.append(f"{data_type.upper()} EXPORT")
        lines.append("=" * 50)
        lines.append(f"Export Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"Record Count: {len(data)}")
        lines.append("")
        
        if not data:
            lines.append("No data to export.")
            return "\n".join(lines)
        
        # 数据内容
        for i, item in enumerate(data, 1):
            lines.append(f"[{i}] {item.get('name', item.get('title', f'Item {i}'))}")
            lines.append("-" * 30)
            
            for key, value in item.items():
                if key not in ['id', 'created_at', 'updated_at']:
                    formatted_value = self._format_text_value(value)
                    lines.append(f"{key.replace('_', ' ').title()}: {formatted_value}")
            
            lines.append("")
        
        return "\n".join(lines)
    
    def _export_xml(self, data: List[Dict[str, Any]], data_type: str) -> str:
        """导出为XML格式"""
        xml_parts = []
        
        # XML头部
        xml_parts.append('<?xml version="1.0" encoding="UTF-8"?>')
        xml_parts.append(f'<{data_type}_export>')
        xml_parts.append('  <metadata>')
        xml_parts.append(f'    <export_time>{datetime.now().isoformat()}</export_time>')
        xml_parts.append(f'    <record_count>{len(data)}</record_count>')
        xml_parts.append(f'    <data_type>{data_type}</data_type>')
        xml_parts.append('  </metadata>')
        xml_parts.append('  <data>')
        
        # 数据内容
        for item in data:
            xml_parts.append(f'    <{data_type[:-1] if data_type.endswith("s") else data_type}>')
            
            for key, value in item.items():
                safe_key = key.replace(' ', '_').replace('-', '_')
                formatted_value = self._escape_xml(str(value))
                xml_parts.append(f'      <{safe_key}>{formatted_value}</{safe_key}>')
            
            xml_parts.append(f'    </{data_type[:-1] if data_type.endswith("s") else data_type}>')
        
        # XML尾部
        xml_parts.append('  </data>')
        xml_parts.append(f'</{data_type}_export>')
        
        return "\n".join(xml_parts)
    
    def _filter_data(self, data: List[Dict[str, Any]], conditions: Dict[str, Any]) -> List[Dict[str, Any]]:
        """过滤数据"""
        filtered = []
        
        for item in data:
            match = True
            for key, value in conditions.items():
                if key not in item:
                    match = False
                    break
                
                item_value = item[key]
                
                # 支持不同的过滤条件
                if isinstance(value, dict):
                    # 范围过滤
                    if 'min' in value and item_value < value['min']:
                        match = False
                        break
                    if 'max' in value and item_value > value['max']:
                        match = False
                        break
                    # 包含过滤
                    if 'contains' in value and value['contains'].lower() not in str(item_value).lower():
                        match = False
                        break
                else:
                    # 精确匹配
                    if str(item_value).lower() != str(value).lower():
                        match = False
                        break
            
            if match:
                filtered.append(item)
        
        return filtered
    
    def _sort_data(self, data: List[Dict[str, Any]], sort_by: str) -> List[Dict[str, Any]]:
        """排序数据"""
        reverse = False
        if sort_by.startswith('-'):
            reverse = True
            sort_by = sort_by[1:]
        
        try:
            return sorted(data, key=lambda x: x.get(sort_by, ''), reverse=reverse)
        except Exception:
            # 如果排序失败，返回原数据
            return data
    
    def _select_fields(self, data: List[Dict[str, Any]], fields: List[str]) -> List[Dict[str, Any]]:
        """选择指定字段"""
        selected = []
        
        for item in data:
            selected_item = {}
            for field in fields:
                if field in item:
                    selected_item[field] = item[field]
            selected.append(selected_item)
        
        return selected
    
    def _apply_template(self, data: List[Dict[str, Any]], data_type: str, template: str) -> List[Dict[str, Any]]:
        """应用模板"""
        if template not in self.templates:
            return data
        
        template_func = self.templates[template]
        return template_func(data, data_type)
    
    def _export_characters_markdown(self, data: List[Dict[str, Any]]) -> List[str]:
        """导出人物的Markdown格式"""
        lines = []
        
        for char in data:
            lines.append(f"## {char.get('name', 'Unknown Character')}")
            lines.append("")
            
            if char.get('role'):
                lines.append(f"**Role:** {char['role']}")
            if char.get('age'):
                lines.append(f"**Age:** {char['age']}")
            if char.get('gender'):
                lines.append(f"**Gender:** {char['gender']}")
            
            if char.get('background'):
                lines.append(f"**Background:** {char['background']}")
            
            if char.get('personality_traits'):
                traits = char['personality_traits']
                if isinstance(traits, list):
                    lines.append(f"**Personality Traits:** {', '.join(traits)}")
                else:
                    lines.append(f"**Personality Traits:** {traits}")
            
            if char.get('appearance'):
                lines.append(f"**Appearance:** {char['appearance']}")
            
            lines.append("")
        
        return lines
    
    def _export_relationships_markdown(self, data: List[Dict[str, Any]]) -> List[str]:
        """导出关系的Markdown格式"""
        lines = []
        
        for rel in data:
            char_a = rel.get('character_a', 'Unknown')
            char_b = rel.get('character_b', 'Unknown')
            rel_type = rel.get('relationship_type', 'Unknown')
            
            lines.append(f"## {char_a} ↔ {char_b}")
            lines.append(f"**Relationship Type:** {rel_type}")
            
            if rel.get('strength'):
                lines.append(f"**Strength:** {rel['strength']}")
            
            if rel.get('description'):
                lines.append(f"**Description:** {rel['description']}")
            
            lines.append("")
        
        return lines
    
    def _export_events_markdown(self, data: List[Dict[str, Any]]) -> List[str]:
        """导出事件的Markdown格式"""
        lines = []
        
        for event in data:
            lines.append(f"## {event.get('title', 'Untitled Event')}")
            lines.append("")
            
            if event.get('event_type'):
                lines.append(f"**Type:** {event['event_type']}")
            if event.get('story_timestamp'):
                lines.append(f"**Time:** {event['story_timestamp']}")
            if event.get('location'):
                lines.append(f"**Location:** {event['location']}")
            if event.get('importance'):
                lines.append(f"**Importance:** {event['importance']}/10")
            
            if event.get('description'):
                lines.append(f"**Description:** {event['description']}")
            
            if event.get('participants'):
                participants = event['participants']
                if isinstance(participants, list):
                    lines.append(f"**Participants:** {', '.join(participants)}")
                else:
                    lines.append(f"**Participants:** {participants}")
            
            lines.append("")
        
        return lines
    
    def _export_world_settings_markdown(self, data: List[Dict[str, Any]]) -> List[str]:
        """导出世界观设定的Markdown格式"""
        lines = []
        
        # 按分类分组
        categories = {}
        for setting in data:
            category = setting.get('category', 'Other')
            if category not in categories:
                categories[category] = []
            categories[category].append(setting)
        
        for category, settings in categories.items():
            lines.append(f"## {category}")
            lines.append("")
            
            for setting in settings:
                lines.append(f"### {setting.get('name', 'Unnamed Setting')}")
                
                if setting.get('description'):
                    lines.append(f"{setting['description']}")
                
                if setting.get('rules'):
                    rules = setting['rules']
                    if isinstance(rules, list):
                        lines.append("**Rules:**")
                        for rule in rules:
                            lines.append(f"- {rule}")
                    else:
                        lines.append(f"**Rules:** {rules}")
                
                lines.append("")
        
        return lines
    
    def _export_notes_markdown(self, data: List[Dict[str, Any]]) -> List[str]:
        """导出笔记的Markdown格式"""
        lines = []
        
        for note in data:
            lines.append(f"## {note.get('title', 'Untitled Note')}")
            lines.append("")
            
            if note.get('note_type'):
                lines.append(f"**Type:** {note['note_type']}")
            if note.get('priority'):
                lines.append(f"**Priority:** {note['priority']}/5")
            
            if note.get('content'):
                lines.append(f"{note['content']}")
            
            if note.get('tags'):
                tags = note['tags']
                if isinstance(tags, list):
                    lines.append(f"**Tags:** {', '.join(tags)}")
                else:
                    lines.append(f"**Tags:** {tags}")
            
            lines.append("")
        
        return lines
    
    def _export_generic_markdown(self, data: List[Dict[str, Any]]) -> List[str]:
        """导出通用Markdown格式"""
        lines = []
        
        for i, item in enumerate(data, 1):
            title = item.get('name', item.get('title', f'Item {i}'))
            lines.append(f"## {title}")
            lines.append("")
            
            for key, value in item.items():
                if key not in ['id', 'name', 'title', 'created_at', 'updated_at']:
                    formatted_value = self._format_markdown_value(value)
                    lines.append(f"**{key.replace('_', ' ').title()}:** {formatted_value}")
            
            lines.append("")
        
        return lines

    def _get_html_styles(self) -> str:
        """获取HTML样式"""
        return """
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        h3 {
            color: #555;
            margin-top: 20px;
        }
        .data-container {
            display: grid;
            gap: 20px;
            margin-top: 20px;
        }
        .data-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .data-item h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        p {
            margin: 5px 0;
        }
        strong {
            color: #333;
        }
        """

    def _format_html_value(self, value: Any) -> str:
        """格式化HTML值"""
        if isinstance(value, list):
            return ", ".join(str(v) for v in value)
        elif isinstance(value, dict):
            return json.dumps(value, ensure_ascii=False)
        else:
            return str(value) if value is not None else ""

    def _format_text_value(self, value: Any) -> str:
        """格式化文本值"""
        if isinstance(value, list):
            return ", ".join(str(v) for v in value)
        elif isinstance(value, dict):
            return json.dumps(value, ensure_ascii=False)
        else:
            return str(value) if value is not None else ""

    def _format_markdown_value(self, value: Any) -> str:
        """格式化Markdown值"""
        if isinstance(value, list):
            return ", ".join(str(v) for v in value)
        elif isinstance(value, dict):
            return f"`{json.dumps(value, ensure_ascii=False)}`"
        else:
            return str(value) if value is not None else ""

    def _escape_xml(self, text: str) -> str:
        """转义XML特殊字符"""
        if not isinstance(text, str):
            text = str(text)

        text = text.replace("&", "&amp;")
        text = text.replace("<", "&lt;")
        text = text.replace(">", "&gt;")
        text = text.replace('"', "&quot;")
        text = text.replace("'", "&apos;")

        return text

    # 模板方法
    def _get_character_profile_template(self):
        """人物档案模板"""
        def template_func(data: List[Dict[str, Any]], data_type: str) -> List[Dict[str, Any]]:
            enhanced_data = []
            for char in data:
                enhanced_char = char.copy()

                # 添加计算字段
                enhanced_char['profile_completeness'] = self._calculate_profile_completeness(char)
                enhanced_char['key_traits'] = self._extract_key_traits(char)

                enhanced_data.append(enhanced_char)

            return enhanced_data

        return template_func

    def _get_relationship_summary_template(self):
        """关系摘要模板"""
        def template_func(data: List[Dict[str, Any]], data_type: str) -> List[Dict[str, Any]]:
            enhanced_data = []
            for rel in data:
                enhanced_rel = rel.copy()

                # 添加关系分析
                enhanced_rel['relationship_strength_level'] = self._categorize_strength(rel.get('strength', 0.5))
                enhanced_rel['relationship_summary'] = self._generate_relationship_summary(rel)

                enhanced_data.append(enhanced_rel)

            return enhanced_data

        return template_func

    def _get_event_timeline_template(self):
        """事件时间线模板"""
        def template_func(data: List[Dict[str, Any]], data_type: str) -> List[Dict[str, Any]]:
            # 按时间排序
            sorted_data = sorted(data, key=lambda x: x.get('story_timestamp', ''))

            enhanced_data = []
            for i, event in enumerate(sorted_data):
                enhanced_event = event.copy()

                # 添加时间线信息
                enhanced_event['timeline_position'] = i + 1
                enhanced_event['importance_level'] = self._categorize_importance(event.get('importance', 5))

                # 添加前后事件关联
                if i > 0:
                    enhanced_event['previous_event'] = sorted_data[i-1].get('title', 'Unknown')
                if i < len(sorted_data) - 1:
                    enhanced_event['next_event'] = sorted_data[i+1].get('title', 'Unknown')

                enhanced_data.append(enhanced_event)

            return enhanced_data

        return template_func

    def _get_world_guide_template(self):
        """世界观指南模板"""
        def template_func(data: List[Dict[str, Any]], data_type: str) -> List[Dict[str, Any]]:
            # 按分类分组
            categories = {}
            for setting in data:
                category = setting.get('category', 'Other')
                if category not in categories:
                    categories[category] = []
                categories[category].append(setting)

            enhanced_data = []
            for category, settings in categories.items():
                for setting in settings:
                    enhanced_setting = setting.copy()
                    enhanced_setting['category_count'] = len(settings)
                    enhanced_setting['world_guide_section'] = category
                    enhanced_data.append(enhanced_setting)

            return enhanced_data

        return template_func

    def _get_story_bible_template(self):
        """故事圣经模板"""
        def template_func(data: List[Dict[str, Any]], data_type: str) -> List[Dict[str, Any]]:
            enhanced_data = []
            for item in data:
                enhanced_item = item.copy()

                # 添加故事圣经相关字段
                enhanced_item['bible_section'] = self._determine_bible_section(item, data_type)
                enhanced_item['reference_priority'] = self._calculate_reference_priority(item, data_type)

                enhanced_data.append(enhanced_item)

            return enhanced_data

        return template_func

    def _calculate_profile_completeness(self, character: Dict[str, Any]) -> float:
        """计算人物档案完整度"""
        required_fields = ['name', 'background', 'personality_traits', 'appearance', 'role']
        completed_fields = sum(1 for field in required_fields if character.get(field))
        return completed_fields / len(required_fields)

    def _extract_key_traits(self, character: Dict[str, Any]) -> List[str]:
        """提取关键特征"""
        traits = character.get('personality_traits', [])
        if isinstance(traits, list):
            return traits[:3]  # 返回前3个特征
        elif isinstance(traits, str):
            return [traits]
        else:
            return []

    def _categorize_strength(self, strength: float) -> str:
        """分类关系强度"""
        if strength >= 0.8:
            return "Very Strong"
        elif strength >= 0.6:
            return "Strong"
        elif strength >= 0.4:
            return "Moderate"
        elif strength >= 0.2:
            return "Weak"
        else:
            return "Very Weak"

    def _generate_relationship_summary(self, relationship: Dict[str, Any]) -> str:
        """生成关系摘要"""
        char_a = relationship.get('character_a', 'Unknown')
        char_b = relationship.get('character_b', 'Unknown')
        rel_type = relationship.get('relationship_type', 'unknown')
        strength = relationship.get('strength', 0.5)

        strength_level = self._categorize_strength(strength)

        return f"{char_a} has a {strength_level.lower()} {rel_type} relationship with {char_b}"

    def _categorize_importance(self, importance: int) -> str:
        """分类重要性"""
        if importance >= 9:
            return "Critical"
        elif importance >= 7:
            return "High"
        elif importance >= 5:
            return "Medium"
        elif importance >= 3:
            return "Low"
        else:
            return "Minimal"

    def _determine_bible_section(self, item: Dict[str, Any], data_type: str) -> str:
        """确定故事圣经章节"""
        if data_type == "characters":
            importance = item.get('importance', 5)
            if importance >= 8:
                return "Main Characters"
            elif importance >= 6:
                return "Supporting Characters"
            else:
                return "Minor Characters"
        elif data_type == "events":
            importance = item.get('importance', 5)
            if importance >= 8:
                return "Major Plot Points"
            elif importance >= 6:
                return "Significant Events"
            else:
                return "Background Events"
        elif data_type == "world_settings":
            return f"World Building - {item.get('category', 'General')}"
        else:
            return "Reference Material"

    def _calculate_reference_priority(self, item: Dict[str, Any], data_type: str) -> int:
        """计算参考优先级"""
        importance = item.get('importance', 5)

        # 基础优先级基于重要性
        priority = importance

        # 根据数据类型调整
        if data_type == "characters":
            # 主角优先级更高
            if item.get('role', '').lower() in ['protagonist', 'main character', '主角']:
                priority += 2
        elif data_type == "events":
            # 情节事件优先级更高
            if item.get('event_type', '').lower() in ['plot', 'climax', '情节', '高潮']:
                priority += 1

        return min(priority, 10)  # 限制在1-10范围内

    def get_available_templates(self) -> Dict[str, str]:
        """获取可用模板列表"""
        return {
            'character_profile': '人物档案模板 - 增强人物信息展示',
            'relationship_summary': '关系摘要模板 - 分析和总结人物关系',
            'event_timeline': '事件时间线模板 - 按时间顺序组织事件',
            'world_guide': '世界观指南模板 - 按分类组织世界观设定',
            'story_bible': '故事圣经模板 - 创建完整的故事参考资料'
        }

    def get_supported_formats(self) -> Dict[str, str]:
        """获取支持的导出格式"""
        return {
            'json': 'JSON格式 - 结构化数据，适合程序处理',
            'csv': 'CSV格式 - 表格数据，适合Excel等工具',
            'markdown': 'Markdown格式 - 文档格式，适合阅读和编辑',
            'html': 'HTML格式 - 网页格式，适合浏览器查看',
            'txt': '纯文本格式 - 简单文本，通用性强',
            'xml': 'XML格式 - 结构化标记语言'
        }
