"""
AI小说管理工具 - 辅助工具函数
提供各种通用辅助功能
"""

import re
import uuid
import hashlib
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Union
import json

def generate_id() -> str:
    """生成唯一ID"""
    return str(uuid.uuid4())

def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化日期时间"""
    if not isinstance(dt, datetime):
        return ""
    return dt.strftime(format_str)

def parse_datetime(dt_str: str) -> Optional[datetime]:
    """解析日期时间字符串"""
    if not dt_str:
        return None
    
    try:
        # 尝试ISO格式
        return datetime.fromisoformat(dt_str)
    except ValueError:
        pass
    
    # 尝试其他常见格式
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d",
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d",
        "%d/%m/%Y %H:%M:%S",
        "%d/%m/%Y",
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(dt_str, fmt)
        except ValueError:
            continue
    
    return None

def sanitize_text(text: str, max_length: int = None) -> str:
    """清理和规范化文本"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 移除控制字符
    text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
    
    # 截断长度
    if max_length and len(text) > max_length:
        text = text[:max_length].rstrip()
    
    return text

def extract_keywords(text: str, min_length: int = 3, max_keywords: int = 20) -> List[str]:
    """从文本中提取关键词"""
    if not text:
        return []
    
    # 转换为小写并移除标点
    text = re.sub(r'[^\w\s]', ' ', text.lower())
    
    # 分割单词
    words = text.split()
    
    # 过滤短词和常见停用词
    stop_words = {
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
        'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does',
        'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that',
        'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her',
        'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their'
    }
    
    keywords = []
    for word in words:
        if len(word) >= min_length and word not in stop_words:
            keywords.append(word)
    
    # 去重并限制数量
    unique_keywords = list(dict.fromkeys(keywords))  # 保持顺序的去重
    return unique_keywords[:max_keywords]

def calculate_text_similarity(text1: str, text2: str) -> float:
    """计算两个文本的相似度（简单的词汇重叠）"""
    if not text1 or not text2:
        return 0.0
    
    words1 = set(extract_keywords(text1))
    words2 = set(extract_keywords(text2))
    
    if not words1 or not words2:
        return 0.0
    
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    return len(intersection) / len(union) if union else 0.0

def generate_hash(data: Union[str, Dict, List]) -> str:
    """生成数据的哈希值"""
    if isinstance(data, (dict, list)):
        data_str = json.dumps(data, sort_keys=True, ensure_ascii=False)
    else:
        data_str = str(data)
    
    return hashlib.md5(data_str.encode('utf-8')).hexdigest()

def merge_dictionaries(dict1: Dict[str, Any], dict2: Dict[str, Any], 
                      deep_merge: bool = True) -> Dict[str, Any]:
    """合并两个字典"""
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and deep_merge:
            if isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = merge_dictionaries(result[key], value, deep_merge)
            elif isinstance(result[key], list) and isinstance(value, list):
                result[key] = result[key] + value
            else:
                result[key] = value
        else:
            result[key] = value
    
    return result

def flatten_list(nested_list: List[Any]) -> List[Any]:
    """展平嵌套列表"""
    result = []
    for item in nested_list:
        if isinstance(item, list):
            result.extend(flatten_list(item))
        else:
            result.append(item)
    return result

def group_by_attribute(items: List[Dict[str, Any]], attribute: str) -> Dict[str, List[Dict[str, Any]]]:
    """按属性分组"""
    groups = {}
    for item in items:
        key = item.get(attribute, 'unknown')
        if key not in groups:
            groups[key] = []
        groups[key].append(item)
    return groups

def calculate_age_from_birth_year(birth_year: int, current_year: int = None) -> int:
    """根据出生年份计算年龄"""
    if current_year is None:
        current_year = datetime.now().year
    return max(0, current_year - birth_year)

def format_duration(seconds: int) -> str:
    """格式化时间间隔"""
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        return f"{minutes}分钟"
    elif seconds < 86400:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}小时{minutes}分钟" if minutes > 0 else f"{hours}小时"
    else:
        days = seconds // 86400
        hours = (seconds % 86400) // 3600
        return f"{days}天{hours}小时" if hours > 0 else f"{days}天"

def validate_and_convert_type(value: Any, target_type: type, default: Any = None) -> Any:
    """验证并转换数据类型"""
    if value is None:
        return default
    
    try:
        if target_type == bool:
            if isinstance(value, str):
                return value.lower() in ('true', '1', 'yes', 'on')
            return bool(value)
        elif target_type == int:
            return int(float(value))  # 先转float再转int，处理"3.0"这种情况
        elif target_type == float:
            return float(value)
        elif target_type == str:
            return str(value)
        elif target_type == list:
            if isinstance(value, str):
                # 尝试解析JSON
                try:
                    return json.loads(value)
                except json.JSONDecodeError:
                    return [value]
            elif isinstance(value, (list, tuple)):
                return list(value)
            else:
                return [value]
        else:
            return target_type(value)
    except (ValueError, TypeError):
        return default

def create_backup_filename(original_name: str, timestamp: datetime = None) -> str:
    """创建备份文件名"""
    if timestamp is None:
        timestamp = datetime.now()
    
    name_parts = original_name.rsplit('.', 1)
    if len(name_parts) == 2:
        name, ext = name_parts
        return f"{name}_backup_{timestamp.strftime('%Y%m%d_%H%M%S')}.{ext}"
    else:
        return f"{original_name}_backup_{timestamp.strftime('%Y%m%d_%H%M%S')}"

def safe_get_nested_value(data: Dict[str, Any], keys: List[str], default: Any = None) -> Any:
    """安全地获取嵌套字典的值"""
    current = data
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return default
    return current
