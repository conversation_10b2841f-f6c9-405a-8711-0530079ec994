"""
AI小说管理工具 - 可视化引擎
提供关系网络和时间线的交互式可视化功能
"""

import json
import math
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import networkx as nx
import logging

class VisualizationEngine:
    """交互式可视化引擎"""
    
    def __init__(self):
        """初始化可视化引擎"""
        self.logger = logging.getLogger(__name__)
        
        # 预定义颜色方案
        self.color_schemes = {
            "character_types": {
                "protagonist": "#FF6B6B",
                "antagonist": "#FF4757", 
                "supporting": "#FFA502",
                "mentor": "#4ECDC4",
                "comic_relief": "#70A1FF",
                "love_interest": "#FF9FF3",
                "default": "#97C2FC"
            },
            "event_types": {
                "plot": "#FF6B6B",
                "character": "#4ECDC4", 
                "worldbuilding": "#FFA502",
                "conflict": "#FF4757",
                "resolution": "#70A1FF",
                "default": "#97C2FC"
            },
            "relationship_types": {
                "family": "#FF6B6B",
                "romantic": "#FF9FF3",
                "friendship": "#4ECDC4",
                "rivalry": "#FF4757",
                "mentor": "#FFA502",
                "alliance": "#70A1FF",
                "default": "#97C2FC"
            }
        }
    
    def create_character_network(self, characters: List[Dict[str, Any]], 
                               relationships: List[Dict[str, Any]],
                               layout: str = "spring",
                               filter_strength: float = 0.0,
                               show_labels: bool = True) -> go.Figure:
        """创建人物关系网络图"""
        try:
            if not characters:
                return self._create_empty_figure("暂无人物数据")
            
            # 创建NetworkX图
            G = nx.Graph()
            
            # 添加节点
            for char in characters:
                char_id = char["id"]
                G.add_node(char_id, **char)
            
            # 添加边（过滤低强度关系）
            for rel in relationships:
                strength = rel.get("strength", 0.5)
                if strength >= filter_strength:
                    G.add_edge(
                        rel["character_a"], 
                        rel["character_b"],
                        strength=strength,
                        relationship_type=rel.get("relationship_type", "unknown"),
                        **rel
                    )
            
            if G.number_of_nodes() == 0:
                return self._create_empty_figure("暂无符合条件的人物关系数据")
            
            # 计算布局
            pos = self._calculate_layout(G, layout)
            
            # 创建边的轨迹
            edge_traces = self._create_edge_traces(G, pos)
            
            # 创建节点轨迹
            node_trace = self._create_node_trace(G, pos, show_labels)
            
            # 创建图形
            fig = go.Figure(data=edge_traces + [node_trace])
            
            # 设置布局
            fig.update_layout(
                title=f"人物关系网络图 ({len(characters)}个人物, {len([r for r in relationships if r.get('strength', 0.5) >= filter_strength])}个关系)",
                showlegend=False,
                hovermode='closest',
                margin=dict(b=20,l=5,r=5,t=40),
                annotations=[ dict(
                    text="拖拽节点可调整位置，悬停查看详细信息",
                    showarrow=False,
                    xref="paper", yref="paper",
                    x=0.005, y=-0.002,
                    xanchor='left', yanchor='bottom',
                    font=dict(color="#888888", size=12)
                )],
                xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                plot_bgcolor='white'
            )
            
            return fig
            
        except Exception as e:
            self.logger.error(f"Failed to create character network: {str(e)}")
            return self._create_empty_figure(f"创建网络图时出错: {str(e)}")
    
    def create_timeline_visualization(self, events: List[Dict[str, Any]],
                                    character_filter: Optional[str] = None,
                                    event_type_filter: Optional[List[str]] = None) -> go.Figure:
        """创建时间线可视化"""
        try:
            if not events:
                return self._create_empty_figure("暂无事件数据")
            
            # 过滤事件
            filtered_events = self._filter_events(events, character_filter, event_type_filter)
            
            if not filtered_events:
                return self._create_empty_figure("暂无符合条件的事件数据")
            
            # 按时间排序
            sorted_events = sorted(filtered_events, key=lambda x: x.get("story_timestamp", ""))
            
            # 创建时间线数据
            timeline_data = self._prepare_timeline_data(sorted_events)
            
            # 创建图形
            fig = go.Figure()
            
            # 添加事件点
            fig.add_trace(go.Scatter(
                x=timeline_data["x_values"],
                y=timeline_data["y_values"],
                mode='markers+text',
                marker=dict(
                    size=timeline_data["sizes"],
                    color=timeline_data["colors"],
                    opacity=0.8,
                    line=dict(width=2, color='white')
                ),
                text=timeline_data["labels"],
                textposition="top center",
                hovertemplate=timeline_data["hover_text"],
                name="事件"
            ))
            
            # 添加连接线
            if len(sorted_events) > 1:
                fig.add_trace(go.Scatter(
                    x=timeline_data["x_values"],
                    y=timeline_data["y_values"],
                    mode='lines',
                    line=dict(color='lightgray', width=1, dash='dot'),
                    showlegend=False,
                    hoverinfo='skip'
                ))
            
            # 设置布局
            title = "全局时间线"
            if character_filter:
                title = f"{character_filter} 的时间线"
            if event_type_filter:
                title += f" ({', '.join(event_type_filter)})"
            
            fig.update_layout(
                title=title,
                xaxis_title="时间",
                yaxis_title="重要性",
                hovermode='closest',
                plot_bgcolor='white',
                margin=dict(t=50, b=50, l=50, r=50)
            )
            
            return fig
            
        except Exception as e:
            self.logger.error(f"Failed to create timeline: {str(e)}")
            return self._create_empty_figure(f"创建时间线时出错: {str(e)}")
    
    def create_character_growth_chart(self, character_id: str, 
                                    events: List[Dict[str, Any]],
                                    character_data: Dict[str, Any]) -> go.Figure:
        """创建人物成长轨迹图"""
        try:
            # 过滤与该人物相关的事件
            char_events = [
                event for event in events 
                if character_id in event.get("participants", [])
            ]
            
            if not char_events:
                return self._create_empty_figure(f"暂无 {character_data.get('name', character_id)} 的相关事件")
            
            # 按时间排序
            sorted_events = sorted(char_events, key=lambda x: x.get("story_timestamp", ""))
            
            # 创建成长数据
            growth_data = self._calculate_character_growth(sorted_events, character_data)
            
            # 创建子图
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('重要性变化', '参与频率', '关系复杂度', '成长里程碑'),
                specs=[[{"secondary_y": False}, {"secondary_y": False}],
                       [{"secondary_y": False}, {"secondary_y": False}]]
            )
            
            # 添加各种轨迹
            self._add_growth_traces(fig, growth_data)
            
            # 设置布局
            fig.update_layout(
                title=f"{character_data.get('name', character_id)} 的成长轨迹分析",
                height=600,
                showlegend=True
            )
            
            return fig
            
        except Exception as e:
            self.logger.error(f"Failed to create growth chart: {str(e)}")
            return self._create_empty_figure(f"创建成长轨迹图时出错: {str(e)}")
    
    def _create_empty_figure(self, message: str) -> go.Figure:
        """创建空图形"""
        fig = go.Figure()
        fig.add_annotation(
            text=message,
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        fig.update_layout(
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            plot_bgcolor='white'
        )
        return fig
    
    def _calculate_layout(self, G: nx.Graph, layout: str) -> Dict[str, Tuple[float, float]]:
        """计算图布局"""
        if layout == "spring":
            return nx.spring_layout(G, k=1, iterations=50)
        elif layout == "circular":
            return nx.circular_layout(G)
        elif layout == "kamada_kawai":
            return nx.kamada_kawai_layout(G)
        elif layout == "random":
            return nx.random_layout(G)
        else:
            return nx.spring_layout(G)
    
    def _create_edge_traces(self, G: nx.Graph, pos: Dict) -> List[go.Scatter]:
        """创建边的轨迹"""
        edge_traces = []
        
        # 按关系类型分组
        edges_by_type = defaultdict(list)
        for edge in G.edges(data=True):
            rel_type = edge[2].get("relationship_type", "default")
            edges_by_type[rel_type].append(edge)
        
        # 为每种关系类型创建轨迹
        for rel_type, edges in edges_by_type.items():
            edge_x = []
            edge_y = []
            edge_info = []
            
            for edge in edges:
                x0, y0 = pos[edge[0]]
                x1, y1 = pos[edge[1]]
                edge_x.extend([x0, x1, None])
                edge_y.extend([y0, y1, None])
                
                strength = edge[2].get("strength", 0.5)
                edge_info.append(f"{edge[0]} - {edge[1]}<br>类型: {rel_type}<br>强度: {strength}")
            
            color = self.color_schemes["relationship_types"].get(rel_type, "#97C2FC")
            
            edge_trace = go.Scatter(
                x=edge_x, y=edge_y,
                line=dict(width=2, color=color),
                hoverinfo='none',
                mode='lines',
                name=rel_type,
                showlegend=False
            )
            
            edge_traces.append(edge_trace)
        
        return edge_traces
    
    def _create_node_trace(self, G: nx.Graph, pos: Dict, show_labels: bool) -> go.Scatter:
        """创建节点轨迹"""
        node_x = []
        node_y = []
        node_text = []
        node_color = []
        node_size = []
        hover_text = []
        
        # 计算中心性
        centrality = nx.degree_centrality(G)
        
        for node in G.nodes():
            x, y = pos[node]
            node_x.append(x)
            node_y.append(y)
            
            node_data = G.nodes[node]
            name = node_data.get("name", node)
            
            # 设置标签
            if show_labels:
                node_text.append(name)
            else:
                node_text.append("")
            
            # 设置颜色
            char_type = node_data.get("character_type", "default")
            color = self.color_schemes["character_types"].get(char_type, "#97C2FC")
            node_color.append(color)
            
            # 设置大小（基于中心性）
            size = 20 + centrality.get(node, 0) * 30
            node_size.append(size)
            
            # 设置悬停信息
            hover_info = f"<b>{name}</b><br>"
            hover_info += f"类型: {node_data.get('character_type', '未知')}<br>"
            hover_info += f"重要性: {node_data.get('importance', 5)}<br>"
            hover_info += f"中心性: {centrality.get(node, 0):.3f}<br>"
            hover_info += f"连接数: {G.degree(node)}"
            hover_text.append(hover_info)
        
        return go.Scatter(
            x=node_x, y=node_y,
            mode='markers+text',
            hovertemplate='%{hovertext}<extra></extra>',
            hovertext=hover_text,
            text=node_text,
            textposition="middle center",
            marker=dict(
                size=node_size,
                color=node_color,
                opacity=0.8,
                line=dict(width=2, color='white')
            )
        )

    def _filter_events(self, events: List[Dict[str, Any]],
                      character_filter: Optional[str] = None,
                      event_type_filter: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """过滤事件"""
        filtered = events

        if character_filter:
            filtered = [
                event for event in filtered
                if character_filter in event.get("participants", [])
            ]

        if event_type_filter:
            filtered = [
                event for event in filtered
                if event.get("event_type") in event_type_filter
            ]

        return filtered

    def _prepare_timeline_data(self, events: List[Dict[str, Any]]) -> Dict[str, List]:
        """准备时间线数据"""
        x_values = []
        y_values = []
        sizes = []
        colors = []
        labels = []
        hover_text = []

        for i, event in enumerate(events):
            # X轴：时间（使用索引作为简化）
            x_values.append(i)

            # Y轴：重要性
            importance = event.get("importance", 5)
            y_values.append(importance)

            # 大小：基于重要性
            size = 10 + importance * 3
            sizes.append(size)

            # 颜色：基于事件类型
            event_type = event.get("event_type", "default")
            color = self.color_schemes["event_types"].get(event_type, "#97C2FC")
            colors.append(color)

            # 标签
            title = event.get("title", f"事件{i+1}")
            labels.append(title[:15] + "..." if len(title) > 15 else title)

            # 悬停信息
            hover_info = f"<b>{event.get('title', '未命名事件')}</b><br>"
            hover_info += f"时间: {event.get('story_timestamp', '未知')}<br>"
            hover_info += f"类型: {event_type}<br>"
            hover_info += f"重要性: {importance}<br>"
            hover_info += f"参与者: {', '.join(event.get('participants', []))}<br>"
            hover_info += f"描述: {event.get('description', '无描述')[:50]}..."
            hover_text.append(hover_info)

        return {
            "x_values": x_values,
            "y_values": y_values,
            "sizes": sizes,
            "colors": colors,
            "labels": labels,
            "hover_text": hover_text
        }

    def _calculate_character_growth(self, events: List[Dict[str, Any]],
                                  character_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算人物成长数据"""
        growth_data = {
            "timestamps": [],
            "importance_scores": [],
            "participation_frequency": [],
            "relationship_complexity": [],
            "milestones": []
        }

        for i, event in enumerate(events):
            growth_data["timestamps"].append(i)

            # 重要性变化
            importance = event.get("importance", 5)
            growth_data["importance_scores"].append(importance)

            # 参与频率（累积）
            growth_data["participation_frequency"].append(i + 1)

            # 关系复杂度（参与者数量）
            participants_count = len(event.get("participants", []))
            growth_data["relationship_complexity"].append(participants_count)

            # 里程碑事件
            if importance >= 8:
                growth_data["milestones"].append({
                    "x": i,
                    "y": importance,
                    "title": event.get("title", "重要事件")
                })

        return growth_data

    def _add_growth_traces(self, fig: go.Figure, growth_data: Dict[str, Any]):
        """添加成长轨迹"""
        timestamps = growth_data["timestamps"]

        # 重要性变化
        fig.add_trace(
            go.Scatter(
                x=timestamps,
                y=growth_data["importance_scores"],
                mode='lines+markers',
                name='重要性',
                line=dict(color='#FF6B6B', width=2)
            ),
            row=1, col=1
        )

        # 参与频率
        fig.add_trace(
            go.Scatter(
                x=timestamps,
                y=growth_data["participation_frequency"],
                mode='lines+markers',
                name='参与频率',
                line=dict(color='#4ECDC4', width=2)
            ),
            row=1, col=2
        )

        # 关系复杂度
        fig.add_trace(
            go.Scatter(
                x=timestamps,
                y=growth_data["relationship_complexity"],
                mode='lines+markers',
                name='关系复杂度',
                line=dict(color='#FFA502', width=2)
            ),
            row=2, col=1
        )

        # 里程碑事件
        if growth_data["milestones"]:
            milestone_x = [m["x"] for m in growth_data["milestones"]]
            milestone_y = [m["y"] for m in growth_data["milestones"]]
            milestone_text = [m["title"] for m in growth_data["milestones"]]

            fig.add_trace(
                go.Scatter(
                    x=milestone_x,
                    y=milestone_y,
                    mode='markers+text',
                    name='里程碑',
                    text=milestone_text,
                    textposition="top center",
                    marker=dict(
                        size=15,
                        color='#FF4757',
                        symbol='star'
                    )
                ),
                row=2, col=2
            )

    def create_relationship_heatmap(self, relationships: List[Dict[str, Any]],
                                  characters: List[Dict[str, Any]]) -> go.Figure:
        """创建关系强度热力图"""
        try:
            if not relationships or not characters:
                return self._create_empty_figure("暂无关系数据")

            # 创建人物名称映射
            char_names = {char["id"]: char.get("name", char["id"]) for char in characters}
            char_list = list(char_names.keys())

            # 创建关系矩阵
            matrix = [[0 for _ in char_list] for _ in char_list]

            for rel in relationships:
                char_a = rel["character_a"]
                char_b = rel["character_b"]

                if char_a in char_list and char_b in char_list:
                    i = char_list.index(char_a)
                    j = char_list.index(char_b)
                    strength = rel.get("strength", 0.5)
                    matrix[i][j] = strength
                    matrix[j][i] = strength  # 对称矩阵

            # 创建热力图
            fig = go.Figure(data=go.Heatmap(
                z=matrix,
                x=[char_names[char_id] for char_id in char_list],
                y=[char_names[char_id] for char_id in char_list],
                colorscale='RdYlBu_r',
                hoverongaps=False,
                hovertemplate='%{y} - %{x}<br>关系强度: %{z}<extra></extra>'
            ))

            fig.update_layout(
                title="人物关系强度热力图",
                xaxis_title="人物",
                yaxis_title="人物"
            )

            return fig

        except Exception as e:
            self.logger.error(f"Failed to create heatmap: {str(e)}")
            return self._create_empty_figure(f"创建热力图时出错: {str(e)}")

    def export_visualization_data(self, data: Dict[str, Any], format_type: str = "json") -> str:
        """导出可视化数据"""
        if format_type == "json":
            return json.dumps(data, ensure_ascii=False, indent=2)
        elif format_type == "csv":
            # 简化的CSV导出
            return self._export_to_csv(data)
        else:
            raise ValueError(f"Unsupported format: {format_type}")

    def _export_to_csv(self, data: Dict[str, Any]) -> str:
        """导出为CSV格式"""
        # 这里可以根据数据类型实现不同的CSV导出逻辑
        lines = ["type,id,name,value"]

        if "nodes" in data:
            for node in data["nodes"]:
                lines.append(f"node,{node.get('id', '')},{node.get('name', '')},{node.get('importance', 5)}")

        if "edges" in data:
            for edge in data["edges"]:
                lines.append(f"edge,{edge.get('source', '')}-{edge.get('target', '')},{edge.get('type', '')},{edge.get('strength', 0.5)}")

        return "\n".join(lines)
