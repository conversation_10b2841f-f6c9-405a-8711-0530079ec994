"""
AI小说管理工具 - 数据模型定义
定义系统中使用的所有数据结构
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
import uuid
import json

class EntityStatus(Enum):
    """实体状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"
    DELETED = "deleted"

class RelationshipType(Enum):
    """关系类型枚举"""
    FAMILY = "family"
    FRIEND = "friend"
    ENEMY = "enemy"
    ROMANTIC = "romantic"
    MENTOR = "mentor"
    COLLEAGUE = "colleague"
    RIVAL = "rival"
    ALLY = "ally"
    NEUTRAL = "neutral"
    COMPLEX = "complex"

class EventType(Enum):
    """事件类型枚举"""
    PLOT = "plot"
    BACKSTORY = "backstory"
    WORLD_EVENT = "world_event"
    CHARACTER_DEVELOPMENT = "character_development"
    CONFLICT = "conflict"
    RESOLUTION = "resolution"

class NoteType(Enum):
    """笔记类型枚举"""
    IDEA = "idea"
    INSPIRATION = "inspiration"
    TODO = "todo"
    RESEARCH = "research"
    REVISION = "revision"
    QUESTION = "question"

@dataclass
class BaseEntity:
    """基础实体类"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    status: EntityStatus = EntityStatus.ACTIVE
    tags: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, datetime):
                result[key] = value.isoformat()
            elif isinstance(value, Enum):
                result[key] = value.value
            elif isinstance(value, list):
                result[key] = value.copy()
            else:
                result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """从字典创建实例"""
        # 处理datetime字段
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        # 处理枚举字段
        if 'status' in data and isinstance(data['status'], str):
            data['status'] = EntityStatus(data['status'])
            
        return cls(**data)

@dataclass
class PersonalityTrait:
    """性格特征"""
    trait: str
    description: str
    intensity: float = 0.5  # 0.0 - 1.0

@dataclass
class DevelopmentStage:
    """发展阶段"""
    stage: str
    description: str
    key_events: List[str] = field(default_factory=list)
    timestamp: Optional[datetime] = None

@dataclass
class Character(BaseEntity):
    """人物角色模型"""
    name: str = ""
    aliases: List[str] = field(default_factory=list)

    # 基本信息
    age: Optional[int] = None
    gender: str = ""
    occupation: str = ""
    background: str = ""

    # 性格特征
    personality_traits: List[PersonalityTrait] = field(default_factory=list)
    motivations: List[str] = field(default_factory=list)
    fears: List[str] = field(default_factory=list)
    goals: List[str] = field(default_factory=list)

    # 外貌描述
    physical_description: str = ""
    distinctive_features: List[str] = field(default_factory=list)

    # 发展轨迹
    development_arc: List[DevelopmentStage] = field(default_factory=list)

    # 重要性评级 (1-10)
    importance: int = 5

@dataclass
class RelationshipHistory:
    """关系历史记录"""
    event: str
    impact: str
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class Relationship(BaseEntity):
    """人物关系模型"""
    character_a: str = ""  # character_id
    character_b: str = ""  # character_id
    relationship_type: RelationshipType = RelationshipType.NEUTRAL
    strength: float = 0.5  # 0.0 - 1.0
    description: str = ""
    history: List[RelationshipHistory] = field(default_factory=list)
    is_mutual: bool = True  # 是否双向关系

@dataclass
class Event(BaseEntity):
    """事件模型"""
    title: str = ""
    description: str = ""
    event_type: EventType = EventType.PLOT

    # 时间信息
    story_timestamp: Optional[datetime] = None  # 故事内时间
    duration: Optional[timedelta] = None

    # 位置和参与者
    location: str = ""
    participants: List[str] = field(default_factory=list)  # character_ids

    # 关系链
    consequences: List[str] = field(default_factory=list)  # 后续事件IDs
    causes: List[str] = field(default_factory=list)  # 前置事件IDs

    # 重要性评级 (1-10)
    importance: int = 5

    # 写作状态
    writing_status: str = "planned"  # planned, drafted, written, revised

@dataclass
class WorldSetting(BaseEntity):
    """世界观设定模型"""
    category: str = ""  # geography, history, culture, magic_system, technology
    name: str = ""
    description: str = ""
    rules: List[str] = field(default_factory=list)
    related_settings: List[str] = field(default_factory=list)  # 相关设定IDs
    consistency_notes: List[str] = field(default_factory=list)
    version: int = 1

@dataclass
class CreativeNote(BaseEntity):
    """创作笔记模型"""
    title: str = ""
    content: str = ""
    note_type: NoteType = NoteType.IDEA

    # 关联实体
    related_characters: List[str] = field(default_factory=list)
    related_events: List[str] = field(default_factory=list)
    related_settings: List[str] = field(default_factory=list)

    # 优先级 (1-5)
    priority: int = 3

    # 使用状态
    usage_status: str = "active"  # active, used, archived
