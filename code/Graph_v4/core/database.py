"""
AI小说管理工具 - 数据库管理模块
提供SQLite数据库连接和基础操作功能
"""

import sqlite3
import json
import logging
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from contextlib import contextmanager
from functools import lru_cache

from ..config import get_config
from .exceptions import DatabaseError, ValidationError
from .models import BaseEntity

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = None):
        """初始化数据库管理器"""
        self.db_path = db_path or get_config("database")["main_db"]
        self.logger = logging.getLogger(__name__)

        # 初始化缓存
        self.cache_config = get_config("query")
        self._query_cache = {}
        self._cache_max_size = self.cache_config.get("cache_size", 1000)
        self._cache_hits = 0
        self._cache_misses = 0

        # JSON序列化缓存
        self._json_cache = {}
        self._json_cache_max_size = 500

        # 预定义JSON字段集合，避免重复创建
        self._json_fields = frozenset([
            'aliases', 'personality_traits', 'motivations', 'fears', 'goals',
            'distinctive_features', 'development_arc', 'history', 'participants',
            'consequences', 'causes', 'rules', 'related_settings',
            'consistency_notes', 'related_characters', 'related_events',
            'related_settings', 'tags'
        ])

        self._ensure_database_exists()
        self._create_tables()

    def _generate_cache_key(self, operation: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = f"{operation}:{str(args)}:{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode('utf-8')).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """从缓存获取数据"""
        if cache_key in self._query_cache:
            self._cache_hits += 1
            # 更新访问时间（简单的LRU实现）
            value = self._query_cache.pop(cache_key)
            self._query_cache[cache_key] = value
            return value
        self._cache_misses += 1
        return None

    def _put_to_cache(self, cache_key: str, value: Any) -> None:
        """将数据放入缓存"""
        # 如果缓存已满，删除最旧的条目
        if len(self._query_cache) >= self._cache_max_size:
            oldest_key = next(iter(self._query_cache))
            del self._query_cache[oldest_key]

        self._query_cache[cache_key] = value

    def clear_cache(self) -> None:
        """清空缓存"""
        self._query_cache.clear()
        self._cache_hits = 0
        self._cache_misses = 0

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self._cache_hits + self._cache_misses
        hit_rate = self._cache_hits / total_requests if total_requests > 0 else 0

        return {
            "cache_size": len(self._query_cache),
            "max_size": self._cache_max_size,
            "hits": self._cache_hits,
            "misses": self._cache_misses,
            "hit_rate": round(hit_rate, 3),
            "total_requests": total_requests
        }

    def _ensure_database_exists(self):
        """确保数据库文件存在"""
        db_file = Path(self.db_path)
        db_file.parent.mkdir(parents=True, exist_ok=True)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            yield conn
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise DatabaseError(f"Database operation failed: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def _create_tables(self):
        """创建数据库表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 人物表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS characters (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    aliases TEXT,  -- JSON array
                    age INTEGER,
                    gender TEXT,
                    occupation TEXT,
                    background TEXT,
                    personality_traits TEXT,  -- JSON array
                    motivations TEXT,  -- JSON array
                    fears TEXT,  -- JSON array
                    goals TEXT,  -- JSON array
                    physical_description TEXT,
                    distinctive_features TEXT,  -- JSON array
                    development_arc TEXT,  -- JSON array
                    importance INTEGER DEFAULT 5,
                    status TEXT DEFAULT 'active',
                    tags TEXT,  -- JSON array
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 关系表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS relationships (
                    id TEXT PRIMARY KEY,
                    character_a TEXT NOT NULL,
                    character_b TEXT NOT NULL,
                    relationship_type TEXT NOT NULL,
                    strength REAL DEFAULT 0.5,
                    description TEXT,
                    history TEXT,  -- JSON array
                    is_mutual BOOLEAN DEFAULT 1,
                    status TEXT DEFAULT 'active',
                    tags TEXT,  -- JSON array
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (character_a) REFERENCES characters (id),
                    FOREIGN KEY (character_b) REFERENCES characters (id)
                )
            """)
            
            # 事件表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS events (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT,
                    event_type TEXT NOT NULL,
                    story_timestamp TEXT,
                    duration TEXT,
                    location TEXT,
                    participants TEXT,  -- JSON array
                    consequences TEXT,  -- JSON array
                    causes TEXT,  -- JSON array
                    importance INTEGER DEFAULT 5,
                    writing_status TEXT DEFAULT 'planned',
                    status TEXT DEFAULT 'active',
                    tags TEXT,  -- JSON array
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 世界观设定表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS world_settings (
                    id TEXT PRIMARY KEY,
                    category TEXT NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    rules TEXT,  -- JSON array
                    related_settings TEXT,  -- JSON array
                    consistency_notes TEXT,  -- JSON array
                    version INTEGER DEFAULT 1,
                    status TEXT DEFAULT 'active',
                    tags TEXT,  -- JSON array
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 创作笔记表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS creative_notes (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    content TEXT,
                    note_type TEXT NOT NULL,
                    related_characters TEXT,  -- JSON array
                    related_events TEXT,  -- JSON array
                    related_settings TEXT,  -- JSON array
                    priority INTEGER DEFAULT 3,
                    usage_status TEXT DEFAULT 'active',
                    status TEXT DEFAULT 'active',
                    tags TEXT,  -- JSON array
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 创建索引
            self._create_indexes(cursor)
            
            conn.commit()
    
    def _create_indexes(self, cursor):
        """创建数据库索引"""
        indexes = [
            # 人物表索引
            "CREATE INDEX IF NOT EXISTS idx_characters_name ON characters(name)",
            "CREATE INDEX IF NOT EXISTS idx_characters_status ON characters(status)",
            "CREATE INDEX IF NOT EXISTS idx_characters_importance ON characters(importance)",
            "CREATE INDEX IF NOT EXISTS idx_characters_created_at ON characters(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_characters_name_status ON characters(name, status)",

            # 关系表索引
            "CREATE INDEX IF NOT EXISTS idx_relationships_character_a ON relationships(character_a)",
            "CREATE INDEX IF NOT EXISTS idx_relationships_character_b ON relationships(character_b)",
            "CREATE INDEX IF NOT EXISTS idx_relationships_type ON relationships(relationship_type)",
            "CREATE INDEX IF NOT EXISTS idx_relationships_strength ON relationships(strength)",
            "CREATE INDEX IF NOT EXISTS idx_relationships_status ON relationships(status)",
            "CREATE INDEX IF NOT EXISTS idx_relationships_characters ON relationships(character_a, character_b)",

            # 事件表索引
            "CREATE INDEX IF NOT EXISTS idx_events_title ON events(title)",
            "CREATE INDEX IF NOT EXISTS idx_events_type ON events(event_type)",
            "CREATE INDEX IF NOT EXISTS idx_events_timestamp ON events(story_timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_events_importance ON events(importance)",
            "CREATE INDEX IF NOT EXISTS idx_events_status ON events(status)",
            "CREATE INDEX IF NOT EXISTS idx_events_writing_status ON events(writing_status)",

            # 世界观设定表索引
            "CREATE INDEX IF NOT EXISTS idx_world_settings_category ON world_settings(category)",
            "CREATE INDEX IF NOT EXISTS idx_world_settings_name ON world_settings(name)",
            "CREATE INDEX IF NOT EXISTS idx_world_settings_status ON world_settings(status)",
            "CREATE INDEX IF NOT EXISTS idx_world_settings_category_name ON world_settings(category, name)",

            # 创作笔记表索引
            "CREATE INDEX IF NOT EXISTS idx_notes_title ON creative_notes(title)",
            "CREATE INDEX IF NOT EXISTS idx_notes_type ON creative_notes(note_type)",
            "CREATE INDEX IF NOT EXISTS idx_notes_priority ON creative_notes(priority)",
            "CREATE INDEX IF NOT EXISTS idx_notes_status ON creative_notes(status)",
            "CREATE INDEX IF NOT EXISTS idx_notes_usage_status ON creative_notes(usage_status)",
            "CREATE INDEX IF NOT EXISTS idx_notes_created_at ON creative_notes(created_at)",
        ]

        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except sqlite3.Error as e:
                self.logger.warning(f"Failed to create index: {index_sql}, Error: {str(e)}")
    
    def _serialize_json_field(self, value: Any) -> str:
        """序列化JSON字段（优化版本）"""
        if value is None:
            return "[]"

        # 对于简单类型，直接转换
        if isinstance(value, str):
            return value if value.startswith('[') or value.startswith('{') else f'["{value}"]'

        # 对于复杂类型，使用缓存
        if isinstance(value, (list, dict)):
            # 生成缓存键
            cache_key = str(hash(str(value)))
            if cache_key in self._json_cache:
                return self._json_cache[cache_key]

            # 序列化
            serialized = json.dumps(value, ensure_ascii=False, separators=(',', ':'))

            # 缓存结果（限制缓存大小）
            if len(self._json_cache) < self._json_cache_max_size:
                self._json_cache[cache_key] = serialized

            return serialized

        return str(value)

    def _deserialize_json_field(self, value: str) -> Any:
        """反序列化JSON字段（优化版本）"""
        if not value or value == "[]":
            return []

        # 使用缓存
        if value in self._json_cache:
            return self._json_cache[value]

        try:
            result = json.loads(value)

            # 缓存结果
            if len(self._json_cache) < self._json_cache_max_size:
                self._json_cache[value] = result

            return result
        except (json.JSONDecodeError, TypeError):
            return []

    def _process_entity_values(self, data: Dict[str, Any]) -> List[Any]:
        """处理实体数据值，优化JSON字段处理"""
        values = []
        for key, value in data.items():
            if key in self._json_fields:
                values.append(self._serialize_json_field(value))
            else:
                values.append(value)
        return values
    
    def insert_entity(self, table: str, entity: BaseEntity) -> str:
        """插入实体到数据库"""
        try:
            entity.updated_at = datetime.now()
            data = entity.to_dict()
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建插入SQL
                columns = list(data.keys())
                placeholders = ["?" for _ in columns]
                
                sql = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                
                # 处理JSON字段（优化版本）
                values = self._process_entity_values(data)
                
                cursor.execute(sql, values)
                conn.commit()
                
                return entity.id
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to insert entity: {str(e)}", "INSERT")

    def batch_insert_entities(self, table: str, entities: List[BaseEntity]) -> List[str]:
        """批量插入实体"""
        if not entities:
            return []

        try:
            entity_ids = []
            with self.get_connection() as conn:
                cursor = conn.cursor()

                for entity in entities:
                    entity.updated_at = datetime.now()
                    data = entity.to_dict()

                    # 构建插入SQL
                    columns = list(data.keys())
                    placeholders = ["?" for _ in columns]

                    sql = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"

                    # 处理JSON字段（优化版本）
                    values = self._process_entity_values(data)

                    cursor.execute(sql, values)
                    entity_ids.append(entity.id)

                conn.commit()
                return entity_ids

        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to batch insert entities: {str(e)}", "BATCH_INSERT")

    def get_entity(self, table: str, entity_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取实体"""
        try:
            # 检查缓存
            cache_key = self._generate_cache_key("get_entity", table, entity_id)
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                return cached_result

            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT * FROM {table} WHERE id = ?", (entity_id,))
                row = cursor.fetchone()

                result = dict(row) if row else None

                # 缓存结果
                self._put_to_cache(cache_key, result)
                return result

        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to get entity: {str(e)}", "SELECT")
    
    def update_entity(self, table: str, entity_id: str, updates: Dict[str, Any]) -> bool:
        """更新实体"""
        try:
            updates['updated_at'] = datetime.now().isoformat()
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建更新SQL
                set_clauses = []
                values = []
                
                for key, value in updates.items():
                    set_clauses.append(f"{key} = ?")
                    if key in self._json_fields:
                        values.append(self._serialize_json_field(value))
                    else:
                        values.append(value)
                
                values.append(entity_id)
                
                sql = f"UPDATE {table} SET {', '.join(set_clauses)} WHERE id = ?"
                cursor.execute(sql, values)
                conn.commit()

                # 清除相关缓存
                cache_key = self._generate_cache_key("get_entity", table, entity_id)
                if cache_key in self._query_cache:
                    del self._query_cache[cache_key]

                return cursor.rowcount > 0

        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to update entity: {str(e)}", "UPDATE")
    
    def delete_entity(self, table: str, entity_id: str) -> bool:
        """删除实体（软删除）"""
        return self.update_entity(table, entity_id, {"status": "deleted"})

    def batch_update_entities(self, table: str, updates: List[Tuple[str, Dict[str, Any]]]) -> int:
        """批量更新实体"""
        if not updates:
            return 0

        try:
            updated_count = 0
            with self.get_connection() as conn:
                cursor = conn.cursor()

                for entity_id, update_data in updates:
                    update_data['updated_at'] = datetime.now().isoformat()

                    # 构建更新SQL
                    set_clauses = []
                    values = []

                    for key, value in update_data.items():
                        set_clauses.append(f"{key} = ?")
                        if key in self._json_fields:
                            values.append(self._serialize_json_field(value))
                        else:
                            values.append(value)

                    values.append(entity_id)

                    sql = f"UPDATE {table} SET {', '.join(set_clauses)} WHERE id = ?"
                    cursor.execute(sql, values)

                    if cursor.rowcount > 0:
                        updated_count += 1
                        # 清除相关缓存
                        cache_key = self._generate_cache_key("get_entity", table, entity_id)
                        if cache_key in self._query_cache:
                            del self._query_cache[cache_key]

                conn.commit()
                return updated_count

        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to batch update entities: {str(e)}", "BATCH_UPDATE")

    def batch_delete_entities(self, table: str, entity_ids: List[str]) -> int:
        """批量删除实体（软删除）"""
        if not entity_ids:
            return 0

        updates = [(entity_id, {"status": "deleted"}) for entity_id in entity_ids]
        return self.batch_update_entities(table, updates)
    
    def search_entities(self, table: str, conditions: Dict[str, Any] = None,
                       limit: int = 100, offset: int = 0, order_by: str = None) -> List[Dict[str, Any]]:
        """搜索实体"""
        try:
            # 检查缓存
            cache_key = self._generate_cache_key("search_entities", table, conditions, limit, offset, order_by)
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                return cached_result

            with self.get_connection() as conn:
                cursor = conn.cursor()

                sql = f"SELECT * FROM {table}"
                values = []

                if conditions:
                    where_clauses = []
                    for key, value in conditions.items():
                        if isinstance(value, list):
                            placeholders = ",".join(["?" for _ in value])
                            where_clauses.append(f"{key} IN ({placeholders})")
                            values.extend(value)
                        elif isinstance(value, str) and value.startswith('%') and value.endswith('%'):
                            # 支持LIKE查询
                            where_clauses.append(f"{key} LIKE ?")
                            values.append(value)
                        else:
                            where_clauses.append(f"{key} = ?")
                            values.append(value)

                    sql += " WHERE " + " AND ".join(where_clauses)

                # 添加排序
                if order_by:
                    sql += f" ORDER BY {order_by}"
                else:
                    # 默认按创建时间排序
                    sql += " ORDER BY created_at DESC"

                sql += f" LIMIT {limit} OFFSET {offset}"

                cursor.execute(sql, values)
                rows = cursor.fetchall()

                result = [dict(row) for row in rows]

                # 缓存结果（只缓存小结果集）
                if len(result) <= 50:
                    self._put_to_cache(cache_key, result)

                return result

        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to search entities: {str(e)}", "SEARCH")
    
    def execute_custom_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行自定义查询"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(sql, params or ())
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to execute custom query: {str(e)}", "CUSTOM_QUERY")
