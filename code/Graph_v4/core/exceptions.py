"""
AI小说管理工具 - 自定义异常类
定义系统中使用的各种异常类型
"""

class NovelManagementError(Exception):
    """小说管理系统基础异常类"""
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class DatabaseError(NovelManagementError):
    """数据库操作异常"""
    def __init__(self, message: str, operation: str = None):
        self.operation = operation
        super().__init__(message, "DB_ERROR")

class ValidationError(NovelManagementError):
    """数据验证异常"""
    def __init__(self, message: str, field: str = None, value=None):
        self.field = field
        self.value = value
        super().__init__(message, "VALIDATION_ERROR")

class QueryError(NovelManagementError):
    """查询操作异常"""
    def __init__(self, message: str, query: str = None):
        self.query = query
        super().__init__(message, "QUERY_ERROR")

class EntityNotFoundError(NovelManagementError):
    """实体未找到异常"""
    def __init__(self, entity_type: str, entity_id: str):
        message = f"{entity_type} with ID '{entity_id}' not found"
        self.entity_type = entity_type
        self.entity_id = entity_id
        super().__init__(message, "ENTITY_NOT_FOUND")

class DuplicateEntityError(NovelManagementError):
    """重复实体异常"""
    def __init__(self, entity_type: str, identifier: str):
        message = f"{entity_type} with identifier '{identifier}' already exists"
        self.entity_type = entity_type
        self.identifier = identifier
        super().__init__(message, "DUPLICATE_ENTITY")

class RelationshipError(NovelManagementError):
    """关系操作异常"""
    def __init__(self, message: str, character_a: str = None, character_b: str = None):
        self.character_a = character_a
        self.character_b = character_b
        super().__init__(message, "RELATIONSHIP_ERROR")

class ConsistencyError(NovelManagementError):
    """一致性检查异常"""
    def __init__(self, message: str, conflicts: list = None):
        self.conflicts = conflicts or []
        super().__init__(message, "CONSISTENCY_ERROR")

class ImportExportError(NovelManagementError):
    """导入导出异常"""
    def __init__(self, message: str, file_path: str = None, format_type: str = None):
        self.file_path = file_path
        self.format_type = format_type
        super().__init__(message, "IMPORT_EXPORT_ERROR")

class SearchError(NovelManagementError):
    """搜索操作异常"""
    def __init__(self, message: str, search_term: str = None):
        self.search_term = search_term
        super().__init__(message, "SEARCH_ERROR")
