# AI小说管理工具 (AI Novel Management System)

一个为作家创作过程提供信息管理和决策支持的本地工具，通过AI Agent协助管理小说创作中的复杂信息。

## 🎯 核心功能

### 📚 人物档案管理
- **完整人物档案**: 姓名、年龄、背景、性格特征、外貌描述
- **性格特征系统**: 多维度性格特征，支持强度评分
- **发展轨迹追踪**: 记录人物在故事中的成长变化
- **角色关系分析**: 自动分析人物重要性和影响力

### 🕸️ 关系网络分析
- **复杂关系建模**: 支持多种关系类型和强度评分
- **网络可视化**: 构建人物关系网络图
- **路径查找**: 查找任意两个人物间的关系路径
- **中心性分析**: 识别关系网络中的关键人物

### ⏰ 情节时间线管理
- **事件时间线**: 按时间顺序组织故事事件
- **因果关系**: 建立事件间的因果关系链
- **情节结构分析**: 自动检测情节漏洞和不一致
- **时间线可视化**: 生成时间线图表和分析报告

### 🌍 世界观设定管理
- **分类管理**: 地理、历史、文化、魔法体系等12个分类
- **版本控制**: 跟踪设定变更历史
- **一致性检查**: 自动检测设定冲突和矛盾
- **关联网络**: 建立设定间的依赖关系

### 📝 创作笔记系统
- **多类型笔记**: 想法、灵感、待办、研究等类型
- **智能搜索**: 全文搜索和相似度匹配
- **实体关联**: 与人物、事件、设定建立关联
- **自动标签**: 基于内容自动提取关键词

## 🏗️ 系统架构

```
Graph_v4/
├── core/                   # 核心模块
│   ├── models.py          # 数据模型定义
│   ├── database.py        # 数据库管理
│   └── exceptions.py      # 异常定义
├── managers/              # 业务管理器
│   ├── character_manager.py      # 人物管理
│   ├── relationship_manager.py   # 关系管理
│   ├── plot_manager.py           # 情节管理
│   ├── worldbuilding_manager.py  # 世界观管理
│   └── notes_manager.py          # 笔记管理
├── utils/                 # 工具模块
│   ├── validators.py      # 数据验证
│   ├── helpers.py         # 辅助函数
│   ├── network_analysis.py       # 网络分析
│   └── timeline_analysis.py      # 时间线分析
├── api/                   # API接口
│   └── novel_api.py       # 统一API接口
├── config.py              # 配置管理
├── main.py                # 主入口
└── mcp_interface.py       # MCP兼容接口
```

## 🚀 快速开始

### 基本使用

```python
from Graph_v4 import create_system

# 创建系统实例
system = create_system()

# 创建人物
result = system.create_character(
    name="张三",
    age=25,
    gender="男",
    background="来自小镇的年轻人",
    role="protagonist"
)

# 创建关系
system.create_relationship(
    character_a=result["character_id"],
    character_b="other_character_id",
    relationship_type="friend",
    description="童年好友"
)

# 创建事件
system.create_event(
    title="初次相遇",
    description="主角与重要配角的初次相遇",
    event_type="meeting",
    involved_characters=[result["character_id"]]
)

# 智能查询
query_result = system.query("张三有哪些朋友？")
```

### MCP接口使用

```python
from Graph_v4.mcp_interface import create_mcp_interface

# 创建MCP接口
mcp = create_mcp_interface()

# 获取可用工具
tools = mcp.get_available_tools()

# 调用工具
result = mcp.call_tool("create_character", {
    "name": "李四",
    "age": 30,
    "background": "经验丰富的导师"
})
```

## 🔧 配置说明

系统配置在 `config.py` 中定义：

```python
# 数据库配置
DATABASE_CONFIG = {
    "path": "data/novel_management.db",
    "timeout": 30,
    "check_same_thread": False
}

# 验证配置
VALIDATION_CONFIG = {
    "max_name_length": 100,
    "max_description_length": 2000,
    "min_relationship_strength": 0.0,
    "max_relationship_strength": 1.0
}
```

## 📊 数据模型

### 人物模型 (Character)
- `id`: 唯一标识符
- `name`: 姓名
- `age`: 年龄
- `gender`: 性别
- `background`: 背景故事
- `personality_traits`: 性格特征列表
- `appearance`: 外貌描述
- `role`: 角色定位
- `importance_score`: 重要性评分

### 关系模型 (Relationship)
- `id`: 唯一标识符
- `character_a`: 人物A ID
- `character_b`: 人物B ID
- `relationship_type`: 关系类型
- `description`: 关系描述
- `strength`: 关系强度 (0-1)
- `is_mutual`: 是否双向关系

### 事件模型 (Event)
- `id`: 唯一标识符
- `title`: 事件标题
- `description`: 事件描述
- `event_type`: 事件类型
- `timeline_position`: 时间线位置
- `involved_characters`: 参与人物列表
- `causal_relationships`: 因果关系列表
- `importance`: 重要性等级

## 🔍 高级功能

### 网络分析
```python
# 获取关系网络
network = system.get_relationship_network("character_id", max_depth=3)

# 分析网络结构
analysis = network["analysis"]
print(f"网络密度: {analysis['density']}")
print(f"关键人物: {analysis['central_characters']}")
```

### 一致性检查
```python
# 检查世界观一致性
consistency = system.check_world_consistency()

for issue in consistency["consistency_issues"]:
    print(f"问题类型: {issue['type']}")
    print(f"严重程度: {issue['severity']}")
    print(f"描述: {issue['message']}")
```

### 数据导出
```python
# 导出人物数据
export_result = system.export_data("characters", "json")

# 导出为CSV格式
csv_result = system.export_data("relationships", "csv")
```

## 🛠️ 开发指南

### 扩展管理器
```python
from Graph_v4.managers.character_manager import CharacterManager

class CustomCharacterManager(CharacterManager):
    def custom_analysis(self):
        # 自定义分析逻辑
        pass
```

### 添加新的数据模型
1. 在 `core/models.py` 中定义数据类
2. 在相应管理器中实现CRUD操作
3. 更新数据库schema
4. 添加验证规则

### 自定义查询
```python
# 使用原生SQL查询
custom_query = """
    SELECT c.name, COUNT(r.id) as relationship_count
    FROM characters c
    LEFT JOIN relationships r ON c.id IN (r.character_a, r.character_b)
    GROUP BY c.id
    ORDER BY relationship_count DESC
"""

results = system.api.db.execute_custom_query(custom_query)
```

## 📈 性能优化

- **数据库索引**: 自动为常用查询字段创建索引
- **缓存机制**: 内置查询结果缓存
- **批量操作**: 支持批量创建和更新
- **懒加载**: 大型数据集的按需加载

## 🔒 数据安全

- **本地存储**: 所有数据存储在本地SQLite数据库
- **备份功能**: 支持数据备份和恢复
- **软删除**: 删除操作使用软删除，可恢复
- **数据验证**: 严格的输入验证和类型检查

## 🤝 AI Agent集成

系统专为AI Agent调用优化：

- **MCP兼容**: 完全兼容MCP协议
- **JSON接口**: 所有接口返回结构化JSON数据
- **错误处理**: 详细的错误信息和状态码
- **批量操作**: 支持批量数据处理
- **智能查询**: 自然语言查询解析

## 📝 许可证

MIT License

## 🙋‍♂️ 支持

如有问题或建议，请提交Issue或联系开发团队。
