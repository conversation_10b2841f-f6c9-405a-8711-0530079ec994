"""
AI小说管理工具 - 统一API接口
为AI Agent提供MCP兼容的统一接口
"""

import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

from ..core.database import DatabaseManager
from ..managers import (
    CharacterManager, RelationshipManager, PlotManager,
    WorldBuildingManager, NotesManager
)
from ..utils.network_analysis import NetworkAnalyzer
from ..utils.timeline_analysis import TimelineAnalyzer
from ..utils.visualization import VisualizationEngine
from ..utils.intelligent_query import IntelligentQueryProcessor
from ..utils.search_engine import AdvancedSearchManager
from ..utils.export_manager import ExportManager
from ..utils.consistency_checker import ConsistencyManager
from ..core.exceptions import NovelManagementError, ValidationError
from ..config import get_config

class NovelManagementAPI:
    """小说管理系统统一API接口"""
    
    def __init__(self, db_path: str = None):
        """初始化API接口"""
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库管理器
        self.db = DatabaseManager(db_path)
        
        # 初始化各个管理器
        self.character_manager = CharacterManager(self.db)
        self.relationship_manager = RelationshipManager(self.db)
        self.plot_manager = PlotManager(self.db)
        self.worldbuilding_manager = WorldBuildingManager(self.db)
        self.notes_manager = NotesManager(self.db)
        
        # 初始化分析工具
        self.network_analyzer = NetworkAnalyzer()
        self.timeline_analyzer = TimelineAnalyzer()
        self.visualization_engine = VisualizationEngine()
        self.intelligent_query = IntelligentQueryProcessor(self)
        self.search_manager = AdvancedSearchManager(self)
        self.export_manager = ExportManager()
        self.consistency_manager = ConsistencyManager(self)
        
        self.logger.info("Novel Management API initialized")

    # ==================== 参数验证辅助方法 ====================

    def _validate_required_fields(self, data: Dict[str, Any], required_fields: List[str]) -> None:
        """验证必需字段"""
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            raise ValidationError(f"Missing required fields: {', '.join(missing_fields)}")

    def _validate_string_field(self, value: Any, field_name: str, max_length: int = None) -> str:
        """验证字符串字段"""
        if not isinstance(value, str):
            raise ValidationError(f"{field_name} must be a string")

        value = value.strip()
        if not value:
            raise ValidationError(f"{field_name} cannot be empty")

        if max_length and len(value) > max_length:
            raise ValidationError(f"{field_name} too long (max {max_length} chars)")

        return value

    # ==================== 人物管理接口 ====================
    
    def create_character(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建人物"""
        try:
            # 验证必需字段
            self._validate_required_fields(data, ['name'])

            # 验证并提取name参数
            name = self._validate_string_field(data.get('name'), 'name', 200)
            kwargs = {k: v for k, v in data.items() if k != 'name'}

            character_id = self.character_manager.create_character(name, **kwargs)
            return {
                "success": True,
                "character_id": character_id,
                "message": f"Character '{name}' created successfully"
            }
        except Exception as e:
            return self._handle_error("create_character", e)

    def batch_create_characters(self, data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量创建人物"""
        try:
            # 验证每个人物数据
            validated_data = []
            for data in data_list:
                self._validate_required_fields(data, ['name'])
                name = self._validate_string_field(data.get('name'), 'name', 200)
                validated_data.append({**data, 'name': name})

            character_ids = self.character_manager.batch_create_characters(validated_data)
            return {
                "success": True,
                "character_ids": character_ids,
                "count": len(character_ids),
                "message": f"Successfully created {len(character_ids)} characters"
            }
        except Exception as e:
            return self._handle_error("batch_create_characters", e)

    def batch_update_characters(self, updates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量更新人物"""
        try:
            # 转换为(id, data)格式
            update_tuples = []
            for update in updates:
                if 'id' not in update:
                    raise ValidationError("Missing 'id' field in update data")
                character_id = update.pop('id')
                update_tuples.append((character_id, update))

            updated_count = self.character_manager.batch_update_characters(update_tuples)
            return {
                "success": True,
                "updated_count": updated_count,
                "message": f"Successfully updated {updated_count} characters"
            }
        except Exception as e:
            return self._handle_error("batch_update_characters", e)

    def batch_delete_characters(self, character_ids: List[str]) -> Dict[str, Any]:
        """批量删除人物"""
        try:
            deleted_count = self.character_manager.batch_delete_characters(character_ids)
            return {
                "success": True,
                "deleted_count": deleted_count,
                "message": f"Successfully deleted {deleted_count} characters"
            }
        except Exception as e:
            return self._handle_error("batch_delete_characters", e)

    def get_character(self, character_id: str) -> Dict[str, Any]:
        """获取人物信息"""
        try:
            character = self.character_manager.get_character(character_id)
            if character:
                return {
                    "success": True,
                    "character": character.to_dict()
                }
            else:
                return {
                    "success": False,
                    "error": "Character not found"
                }
        except Exception as e:
            return self._handle_error("get_character", e)
    
    def update_character(self, character_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """更新人物信息"""
        try:
            success = self.character_manager.update_character(character_id, updates)
            return {
                "success": success,
                "message": "Character updated successfully" if success else "Update failed"
            }
        except Exception as e:
            return self._handle_error("update_character", e)
    
    def search_characters(self, criteria: Dict[str, Any] = None, limit: int = 100) -> Dict[str, Any]:
        """搜索人物"""
        try:
            results = self.character_manager.search_characters(criteria, limit)
            return {
                "success": True,
                "characters": results,
                "count": len(results)
            }
        except Exception as e:
            return self._handle_error("search_characters", e)
    
    def get_character_statistics(self) -> Dict[str, Any]:
        """获取人物统计信息"""
        try:
            stats = self.character_manager.get_character_statistics()
            return {
                "success": True,
                "statistics": stats
            }
        except Exception as e:
            return self._handle_error("get_character_statistics", e)
    
    # ==================== 关系管理接口 ====================
    
    def create_relationship(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建人物关系"""
        try:
            # 提取必需的位置参数
            character_a = data.get('character_a', '')
            character_b = data.get('character_b', '')
            relationship_type = data.get('relationship_type', 'neutral')

            # 其余参数作为kwargs
            kwargs = {k: v for k, v in data.items()
                     if k not in ['character_a', 'character_b', 'relationship_type']}

            relationship_id = self.relationship_manager.create_relationship(
                character_a, character_b, relationship_type, **kwargs
            )
            return {
                "success": True,
                "relationship_id": relationship_id,
                "message": "Relationship created successfully"
            }
        except Exception as e:
            return self._handle_error("create_relationship", e)
    
    def get_relationship_network(self, character_id: str, max_depth: int = 2) -> Dict[str, Any]:
        """获取关系网络"""
        try:
            network = self.relationship_manager.get_relationship_network(character_id, max_depth)
            analysis = self.network_analyzer.analyze_network_structure(network)
            
            return {
                "success": True,
                "network": network,
                "analysis": analysis,
                "summary": self.network_analyzer.generate_network_summary(network)
            }
        except Exception as e:
            return self._handle_error("get_relationship_network", e)
    
    def find_relationship_path(self, start_character: str, end_character: str) -> Dict[str, Any]:
        """查找关系路径"""
        try:
            path = self.relationship_manager.find_relationship_path(start_character, end_character)
            return {
                "success": True,
                "path": path,
                "path_length": len(path),
                "exists": len(path) > 0
            }
        except Exception as e:
            return self._handle_error("find_relationship_path", e)
    
    # ==================== 情节管理接口 ====================
    
    def create_event(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建事件"""
        try:
            # 处理字段名称映射
            event_data = data.copy()
            if 'involved_characters' in event_data:
                event_data['participants'] = event_data.pop('involved_characters')

            event_id = self.plot_manager.create_event(**event_data)
            return {
                "success": True,
                "event_id": event_id,
                "message": f"Event '{data.get('title')}' created successfully"
            }
        except Exception as e:
            return self._handle_error("create_event", e)
    
    def get_timeline(self, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """获取时间线"""
        try:
            timeline_events = self.plot_manager.get_timeline(**filters if filters else {})
            timeline_data = self.timeline_analyzer.create_timeline_visualization(timeline_events)
            
            return {
                "success": True,
                "timeline": timeline_data,
                "summary": self.timeline_analyzer.generate_timeline_summary(timeline_data)
            }
        except Exception as e:
            return self._handle_error("get_timeline", e)
    
    def analyze_plot_structure(self) -> Dict[str, Any]:
        """分析情节结构"""
        try:
            analysis = self.plot_manager.analyze_plot_structure()
            return {
                "success": True,
                "analysis": analysis
            }
        except Exception as e:
            return self._handle_error("analyze_plot_structure", e)
    
    def add_causal_relationship(self, cause_event_id: str, effect_event_id: str) -> Dict[str, Any]:
        """添加因果关系"""
        try:
            success = self.plot_manager.add_causal_relationship(cause_event_id, effect_event_id)
            return {
                "success": success,
                "message": "Causal relationship added successfully" if success else "Failed to add causal relationship"
            }
        except Exception as e:
            return self._handle_error("add_causal_relationship", e)
    
    # ==================== 世界观管理接口 ====================
    
    def create_world_setting(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建世界观设定"""
        try:
            # 提取必需的位置参数
            category = data.get('category', '')
            name = data.get('name', '')
            description = data.get('description', '')

            # 其余参数作为kwargs
            kwargs = {k: v for k, v in data.items()
                     if k not in ['category', 'name', 'description']}

            setting_id = self.worldbuilding_manager.create_setting(
                category, name, description, **kwargs
            )
            return {
                "success": True,
                "setting_id": setting_id,
                "message": f"World setting '{name}' created successfully"
            }
        except Exception as e:
            return self._handle_error("create_world_setting", e)
    
    def check_world_consistency(self) -> Dict[str, Any]:
        """检查世界观一致性"""
        try:
            issues = self.worldbuilding_manager.check_consistency()
            return {
                "success": True,
                "consistency_issues": issues,
                "issue_count": len(issues),
                "has_issues": len(issues) > 0
            }
        except Exception as e:
            return self._handle_error("check_world_consistency", e)
    
    def get_setting_network(self, setting_id: str, max_depth: int = 3) -> Dict[str, Any]:
        """获取设定关联网络"""
        try:
            network = self.worldbuilding_manager.get_setting_network(setting_id, max_depth)
            return {
                "success": True,
                "network": network
            }
        except Exception as e:
            return self._handle_error("get_setting_network", e)
    
    # ==================== 创作笔记接口 ====================
    
    def create_note(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建创作笔记"""
        try:
            # 提取必需的位置参数
            title = data.get('title', '')
            content = data.get('content', '')
            note_type = data.get('note_type', 'idea')

            # 其余参数作为kwargs
            kwargs = {k: v for k, v in data.items()
                     if k not in ['title', 'content', 'note_type']}

            note_id = self.notes_manager.create_note(
                title, content, note_type, **kwargs
            )
            return {
                "success": True,
                "note_id": note_id,
                "message": f"Note '{title}' created successfully"
            }
        except Exception as e:
            return self._handle_error("create_note", e)
    
    def search_notes_by_content(self, query: str, limit: int = 50) -> Dict[str, Any]:
        """根据内容搜索笔记"""
        try:
            results = self.notes_manager.search_notes_by_content(query, limit)
            return {
                "success": True,
                "notes": results,
                "count": len(results),
                "query": query
            }
        except Exception as e:
            return self._handle_error("search_notes_by_content", e)
    
    def get_related_notes(self, entity_type: str, entity_id: str) -> Dict[str, Any]:
        """获取相关笔记"""
        try:
            notes = self.notes_manager.get_related_notes(entity_type, entity_id)
            return {
                "success": True,
                "notes": notes,
                "count": len(notes)
            }
        except Exception as e:
            return self._handle_error("get_related_notes", e)
    
    # ==================== 智能查询接口 ====================
    
    def intelligent_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """智能查询接口"""
        try:
            # 解析查询意图
            intent = self._parse_query_intent(query)
            
            # 根据意图执行相应操作
            if intent["type"] == "character_search":
                return self._handle_character_query(intent, context)
            elif intent["type"] == "relationship_analysis":
                return self._handle_relationship_query(intent, context)
            elif intent["type"] == "plot_analysis":
                return self._handle_plot_query(intent, context)
            elif intent["type"] == "world_query":
                return self._handle_world_query(intent, context)
            elif intent["type"] == "note_search":
                return self._handle_note_query(intent, context)
            else:
                return self._handle_general_query(query, context)
                
        except Exception as e:
            return self._handle_error("intelligent_query", e)
    
    def get_system_overview(self) -> Dict[str, Any]:
        """获取系统概览"""
        try:
            overview = {
                "characters": self.character_manager.get_character_statistics(),
                "relationships": self.relationship_manager.get_relationship_statistics(),
                "events": self.plot_manager._calculate_plot_metrics(),
                "world_settings": self.worldbuilding_manager.get_setting_statistics(),
                "notes": self.notes_manager.get_note_statistics()
            }
            
            return {
                "success": True,
                "overview": overview,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return self._handle_error("get_system_overview", e)
    
    def export_data(self, data_type: str, format_type: str = "json", filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """导出数据"""
        try:
            if data_type == "characters":
                data = self.character_manager.search_characters(filters)
            elif data_type == "relationships":
                # 获取所有关系数据
                data = self.relationship_manager.db.search_entities("relationships", filters or {"status": "active"})
            elif data_type == "events":
                data = self.plot_manager.search_events(filters)
            elif data_type == "world_settings":
                data = self.worldbuilding_manager.search_settings(filters)
            elif data_type == "notes":
                data = self.notes_manager.search_notes(filters)
            else:
                return {
                    "success": False,
                    "error": f"Unsupported data type: {data_type}"
                }
            
            # 格式化导出数据
            if format_type == "json":
                exported_data = json.dumps(data, ensure_ascii=False, indent=2, default=str)
            elif format_type == "csv":
                exported_data = self._convert_to_csv(data)
            else:
                return {
                    "success": False,
                    "error": f"Unsupported format: {format_type}"
                }
            
            return {
                "success": True,
                "data": exported_data,
                "format": format_type,
                "count": len(data)
            }
            
        except Exception as e:
            return self._handle_error("export_data", e)
    
    # ==================== 私有辅助方法 ====================
    
    def _handle_error(self, operation: str, error: Exception) -> Dict[str, Any]:
        """统一错误处理"""
        error_msg = str(error)
        self.logger.error(f"Error in {operation}: {error_msg}")
        
        return {
            "success": False,
            "error": error_msg,
            "operation": operation,
            "error_type": type(error).__name__
        }
    
    def _parse_query_intent(self, query: str) -> Dict[str, Any]:
        """解析查询意图"""
        query_lower = query.lower()
        
        # 人物相关查询
        if any(keyword in query_lower for keyword in ["character", "人物", "角色", "who is", "谁是"]):
            return {"type": "character_search", "query": query}
        
        # 关系相关查询
        elif any(keyword in query_lower for keyword in ["relationship", "关系", "connection", "连接"]):
            return {"type": "relationship_analysis", "query": query}
        
        # 情节相关查询
        elif any(keyword in query_lower for keyword in ["plot", "story", "event", "情节", "故事", "事件"]):
            return {"type": "plot_analysis", "query": query}
        
        # 世界观相关查询
        elif any(keyword in query_lower for keyword in ["world", "setting", "世界", "设定", "背景"]):
            return {"type": "world_query", "query": query}
        
        # 笔记相关查询
        elif any(keyword in query_lower for keyword in ["note", "idea", "笔记", "想法", "灵感"]):
            return {"type": "note_search", "query": query}
        
        else:
            return {"type": "general", "query": query}
    
    def _handle_character_query(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理人物查询"""
        query = intent["query"]
        
        # 简单的关键词提取
        keywords = query.split()
        
        # 搜索人物
        results = self.character_manager.search_characters(limit=10)
        
        # 过滤相关结果
        relevant_results = []
        for char in results:
            char_text = f"{char.get('name', '')} {char.get('background', '')}".lower()
            if any(keyword.lower() in char_text for keyword in keywords):
                relevant_results.append(char)
        
        return {
            "success": True,
            "type": "character_search",
            "results": relevant_results[:5],
            "count": len(relevant_results)
        }
    
    def _handle_relationship_query(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理关系查询"""
        # 获取关系统计
        stats = self.relationship_manager.get_relationship_statistics()
        
        return {
            "success": True,
            "type": "relationship_analysis",
            "statistics": stats
        }
    
    def _handle_plot_query(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理情节查询"""
        # 获取情节分析
        analysis = self.plot_manager.analyze_plot_structure()
        
        return {
            "success": True,
            "type": "plot_analysis",
            "analysis": analysis
        }
    
    def _handle_world_query(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理世界观查询"""
        # 获取世界观统计
        stats = self.worldbuilding_manager.get_setting_statistics()
        
        return {
            "success": True,
            "type": "world_query",
            "statistics": stats
        }
    
    def _handle_note_query(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理笔记查询"""
        query = intent["query"]
        
        # 搜索笔记
        results = self.notes_manager.search_notes_by_content(query)
        
        return {
            "success": True,
            "type": "note_search",
            "results": results[:10],
            "count": len(results)
        }
    
    def _handle_general_query(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理通用查询"""
        # 获取系统概览
        overview = self.get_system_overview()
        
        return {
            "success": True,
            "type": "general",
            "message": "Here's an overview of your novel management system",
            "overview": overview.get("overview", {})
        }
    
    def _convert_to_csv(self, data: List[Dict[str, Any]]) -> str:
        """转换为CSV格式"""
        if not data:
            return ""
        
        # 获取所有字段
        all_fields = set()
        for item in data:
            all_fields.update(item.keys())
        
        fields = sorted(all_fields)
        
        # 生成CSV
        csv_lines = [",".join(fields)]
        
        for item in data:
            row = []
            for field in fields:
                value = item.get(field, "")
                if isinstance(value, (list, dict)):
                    value = json.dumps(value, ensure_ascii=False)
                row.append(f'"{str(value)}"')
            csv_lines.append(",".join(row))
        
        return "\n".join(csv_lines)

    # ==================== 可视化方法 ====================

    def create_character_network_visualization(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建人物关系网络可视化"""
        try:
            # 获取参数
            layout = data.get("layout", "spring")
            filter_strength = data.get("filter_strength", 0.0)
            show_labels = data.get("show_labels", True)

            # 获取数据
            characters = self.search_characters({})
            relationships = self.search_relationships({})

            if not characters["success"]:
                return characters

            if not relationships["success"]:
                return relationships

            # 创建可视化
            fig = self.visualization_engine.create_character_network(
                characters["characters"],
                relationships["relationships"],
                layout=layout,
                filter_strength=filter_strength,
                show_labels=show_labels
            )

            # 保存为HTML（如果指定路径）
            save_path = data.get("save_path")
            if save_path:
                fig.write_html(save_path)

            return {
                "success": True,
                "visualization_type": "character_network",
                "layout": layout,
                "filter_strength": filter_strength,
                "node_count": len(characters["characters"]),
                "edge_count": len([r for r in relationships["relationships"] if r.get("strength", 0.5) >= filter_strength]),
                "html_data": fig.to_html() if not save_path else None,
                "save_path": save_path,
                "message": "Character network visualization created successfully"
            }

        except Exception as e:
            return self._handle_error("create_character_network_visualization", e)

    def create_timeline_visualization(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建时间线可视化"""
        try:
            # 获取参数
            character_filter = data.get("character_filter")
            event_type_filter = data.get("event_type_filter")

            # 获取事件数据
            events_result = self.search_events({})
            if not events_result["success"]:
                return events_result

            # 创建可视化
            fig = self.visualization_engine.create_timeline_visualization(
                events_result["events"],
                character_filter=character_filter,
                event_type_filter=event_type_filter
            )

            # 保存为HTML（如果指定路径）
            save_path = data.get("save_path")
            if save_path:
                fig.write_html(save_path)

            return {
                "success": True,
                "visualization_type": "timeline",
                "character_filter": character_filter,
                "event_type_filter": event_type_filter,
                "event_count": len(events_result["events"]),
                "html_data": fig.to_html() if not save_path else None,
                "save_path": save_path,
                "message": "Timeline visualization created successfully"
            }

        except Exception as e:
            return self._handle_error("create_timeline_visualization", e)

    def create_character_growth_chart(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建人物成长轨迹图"""
        try:
            # 验证必要参数
            self._validate_required_fields(data, ['character_id'])
            character_id = data["character_id"]

            # 获取人物数据
            character_result = self.get_character(character_id)
            if not character_result["success"]:
                return character_result

            # 获取事件数据
            events_result = self.search_events({})
            if not events_result["success"]:
                return events_result

            # 创建可视化
            fig = self.visualization_engine.create_character_growth_chart(
                character_id,
                events_result["events"],
                character_result["character"]
            )

            # 保存为HTML（如果指定路径）
            save_path = data.get("save_path")
            if save_path:
                fig.write_html(save_path)

            return {
                "success": True,
                "visualization_type": "character_growth",
                "character_id": character_id,
                "character_name": character_result["character"].get("name", character_id),
                "html_data": fig.to_html() if not save_path else None,
                "save_path": save_path,
                "message": f"Character growth chart created for {character_result['character'].get('name', character_id)}"
            }

        except Exception as e:
            return self._handle_error("create_character_growth_chart", e)

    def create_relationship_heatmap(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建关系强度热力图"""
        try:
            # 获取数据
            characters = self.search_characters({})
            relationships = self.search_relationships({})

            if not characters["success"]:
                return characters

            if not relationships["success"]:
                return relationships

            # 创建可视化
            fig = self.visualization_engine.create_relationship_heatmap(
                relationships["relationships"],
                characters["characters"]
            )

            # 保存为HTML（如果指定路径）
            save_path = data.get("save_path")
            if save_path:
                fig.write_html(save_path)

            return {
                "success": True,
                "visualization_type": "relationship_heatmap",
                "character_count": len(characters["characters"]),
                "relationship_count": len(relationships["relationships"]),
                "html_data": fig.to_html() if not save_path else None,
                "save_path": save_path,
                "message": "Relationship heatmap created successfully"
            }

        except Exception as e:
            return self._handle_error("create_relationship_heatmap", e)

    # ==================== 智能查询方法 ====================

    def intelligent_query(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """智能查询处理"""
        try:
            # 验证必要参数
            self._validate_required_fields(data, ['query'])
            query = data["query"]
            context = data.get("context")

            # 处理查询
            result = self.intelligent_query.process_query(query, context)

            return result

        except Exception as e:
            return self._handle_error("intelligent_query", e)

    def suggest_queries(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """查询建议"""
        try:
            # 获取系统统计信息
            stats = self.get_statistics()

            suggestions = []

            # 基于现有数据生成建议
            if stats.get("success") and stats.get("statistics"):
                system_stats = stats["statistics"]

                # 人物相关建议
                if system_stats.get("character_count", 0) > 0:
                    suggestions.extend([
                        "找到所有主要人物",
                        "谁是最重要的人物？",
                        "显示人物关系网络",
                        "分析人物影响力"
                    ])

                # 关系相关建议
                if system_stats.get("relationship_count", 0) > 0:
                    suggestions.extend([
                        "显示所有朋友关系",
                        "谁和谁是敌人？",
                        "最复杂的人物关系",
                        "创建关系热力图"
                    ])

                # 事件相关建议
                if system_stats.get("event_count", 0) > 0:
                    suggestions.extend([
                        "显示时间线",
                        "最重要的事件是什么？",
                        "按时间顺序显示事件",
                        "分析情节结构"
                    ])

                # 世界观相关建议
                if system_stats.get("world_setting_count", 0) > 0:
                    suggestions.extend([
                        "显示所有世界观设定",
                        "检查世界观一致性",
                        "地点设定有哪些？"
                    ])

                # 笔记相关建议
                if system_stats.get("note_count", 0) > 0:
                    suggestions.extend([
                        "显示所有创作笔记",
                        "高优先级的笔记",
                        "最近的创作想法"
                    ])

            # 如果没有数据，提供基础建议
            if not suggestions:
                suggestions = [
                    "创建第一个人物",
                    "添加人物关系",
                    "记录重要事件",
                    "设定世界观",
                    "写下创作想法"
                ]

            return {
                "success": True,
                "suggestions": suggestions[:10],  # 限制建议数量
                "total_suggestions": len(suggestions),
                "message": f"生成了 {len(suggestions)} 个查询建议"
            }

        except Exception as e:
            return self._handle_error("suggest_queries", e)

    def explain_query_result(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """解释查询结果"""
        try:
            # 验证必要参数
            self._validate_required_fields(data, ['query_result'])
            query_result = data["query_result"]

            explanation = {
                "result_type": "unknown",
                "summary": "",
                "details": [],
                "suggestions": []
            }

            # 分析查询结果类型
            if "characters" in query_result:
                characters = query_result["characters"]
                explanation["result_type"] = "character_search"
                explanation["summary"] = f"找到 {len(characters)} 个人物"

                for char in characters[:3]:  # 只解释前3个
                    explanation["details"].append(
                        f"人物 {char.get('name', '未知')} - {char.get('role', '角色未定义')}"
                    )

                explanation["suggestions"] = [
                    "查看人物详细信息",
                    "显示人物关系网络",
                    "分析人物重要性"
                ]

            elif "relationships" in query_result:
                relationships = query_result["relationships"]
                explanation["result_type"] = "relationship_search"
                explanation["summary"] = f"找到 {len(relationships)} 个关系"

                for rel in relationships[:3]:
                    explanation["details"].append(
                        f"{rel.get('character_a', '未知')} 和 {rel.get('character_b', '未知')} 的 {rel.get('relationship_type', '未知')} 关系"
                    )

                explanation["suggestions"] = [
                    "创建关系网络图",
                    "分析关系强度",
                    "查看相关事件"
                ]

            elif "events" in query_result:
                events = query_result["events"]
                explanation["result_type"] = "event_search"
                explanation["summary"] = f"找到 {len(events)} 个事件"

                for event in events[:3]:
                    explanation["details"].append(
                        f"事件: {event.get('title', '未命名')} - {event.get('event_type', '类型未知')}"
                    )

                explanation["suggestions"] = [
                    "创建时间线视图",
                    "分析事件重要性",
                    "查看参与人物"
                ]

            elif "world_settings" in query_result:
                settings = query_result["world_settings"]
                explanation["result_type"] = "worldbuilding_search"
                explanation["summary"] = f"找到 {len(settings)} 个世界观设定"

                for setting in settings[:3]:
                    explanation["details"].append(
                        f"设定: {setting.get('name', '未命名')} - {setting.get('category', '分类未知')}"
                    )

                explanation["suggestions"] = [
                    "检查一致性",
                    "查看相关设定",
                    "完善设定细节"
                ]

            elif "notes" in query_result:
                notes = query_result["notes"]
                explanation["result_type"] = "note_search"
                explanation["summary"] = f"找到 {len(notes)} 个笔记"

                for note in notes[:3]:
                    explanation["details"].append(
                        f"笔记: {note.get('title', '未命名')} - {note.get('note_type', '类型未知')}"
                    )

                explanation["suggestions"] = [
                    "查看笔记详情",
                    "按优先级排序",
                    "关联相关内容"
                ]

            return {
                "success": True,
                "explanation": explanation,
                "message": "查询结果解释生成成功"
            }

        except Exception as e:
            return self._handle_error("explain_query_result", e)

    # ==================== 全文搜索方法 ====================

    def full_text_search(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """全文搜索"""
        try:
            # 验证必要参数
            self._validate_required_fields(data, ['query'])
            query = data["query"]

            # 获取可选参数
            data_types = data.get("data_types")
            max_results = data.get("max_results", 50)
            fuzzy_threshold = data.get("fuzzy_threshold", 0.6)

            # 执行搜索
            result = self.search_manager.full_text_search(
                query, data_types, max_results, fuzzy_threshold
            )

            return result

        except Exception as e:
            return self._handle_error("full_text_search", e)

    def rebuild_search_indexes(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """重建搜索索引"""
        try:
            result = self.search_manager.rebuild_indexes()
            return result

        except Exception as e:
            return self._handle_error("rebuild_search_indexes", e)

    def get_search_suggestions(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """获取搜索建议"""
        try:
            # 验证必要参数
            self._validate_required_fields(data, ['partial_query'])
            partial_query = data["partial_query"]
            max_suggestions = data.get("max_suggestions", 10)

            # 获取建议
            result = self.search_manager.get_search_suggestions(partial_query, max_suggestions)

            return result

        except Exception as e:
            return self._handle_error("get_search_suggestions", e)

    def advanced_search(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """高级搜索（组合多种搜索方式）"""
        try:
            # 验证必要参数
            self._validate_required_fields(data, ['query'])
            query = data["query"]

            results = {
                "success": True,
                "query": query,
                "search_methods": {},
                "combined_results": [],
                "summary": {}
            }

            # 1. 全文搜索
            if data.get("enable_full_text", True):
                full_text_result = self.full_text_search({
                    "query": query,
                    "data_types": data.get("data_types"),
                    "max_results": data.get("max_results", 20),
                    "fuzzy_threshold": data.get("fuzzy_threshold", 0.6)
                })
                results["search_methods"]["full_text"] = full_text_result

                if full_text_result.get("success"):
                    for result in full_text_result.get("results", []):
                        result["search_method"] = "full_text"
                        results["combined_results"].append(result)

            # 2. 智能查询
            if data.get("enable_intelligent", True):
                intelligent_result = self.intelligent_query({
                    "query": query,
                    "context": data.get("context")
                })
                results["search_methods"]["intelligent"] = intelligent_result

                if intelligent_result.get("success") and intelligent_result.get("results"):
                    # 将智能查询结果转换为统一格式
                    query_results = intelligent_result["results"]
                    for data_type in ["characters", "relationships", "events", "world_settings", "notes"]:
                        if data_type in query_results:
                            for item in query_results[data_type]:
                                results["combined_results"].append({
                                    "document_id": item.get("id", ""),
                                    "document": item,
                                    "data_type": data_type,
                                    "relevance_score": 0.8,  # 智能查询的默认相关性
                                    "search_method": "intelligent",
                                    "matched_terms": []
                                })

            # 3. 精确匹配搜索
            if data.get("enable_exact", True):
                exact_results = self._exact_search(query, data.get("data_types"))
                results["search_methods"]["exact"] = exact_results

                if exact_results.get("success"):
                    for result in exact_results.get("results", []):
                        result["search_method"] = "exact"
                        results["combined_results"].append(result)

            # 去重和排序
            results["combined_results"] = self._deduplicate_and_rank_results(results["combined_results"])

            # 生成摘要
            results["summary"] = self._generate_search_summary(results)

            return results

        except Exception as e:
            return self._handle_error("advanced_search", e)

    def _exact_search(self, query: str, data_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """精确匹配搜索"""
        try:
            if data_types is None:
                data_types = ["characters", "relationships", "events", "world_settings", "notes"]

            all_results = []

            for data_type in data_types:
                if data_type == "characters":
                    result = self.search_characters({"name": query})
                    if result.get("success"):
                        for char in result.get("characters", []):
                            all_results.append({
                                "document_id": char.get("id", ""),
                                "document": char,
                                "data_type": "characters",
                                "relevance_score": 1.0,
                                "matched_terms": [query]
                            })

                elif data_type == "events":
                    result = self.search_events({"title": query})
                    if result.get("success"):
                        for event in result.get("events", []):
                            all_results.append({
                                "document_id": event.get("id", ""),
                                "document": event,
                                "data_type": "events",
                                "relevance_score": 1.0,
                                "matched_terms": [query]
                            })

                elif data_type == "world_settings":
                    result = self.search_world_settings({"name": query})
                    if result.get("success"):
                        for setting in result.get("world_settings", []):
                            all_results.append({
                                "document_id": setting.get("id", ""),
                                "document": setting,
                                "data_type": "world_settings",
                                "relevance_score": 1.0,
                                "matched_terms": [query]
                            })

                elif data_type == "notes":
                    result = self.search_notes({"title": query})
                    if result.get("success"):
                        for note in result.get("notes", []):
                            all_results.append({
                                "document_id": note.get("id", ""),
                                "document": note,
                                "data_type": "notes",
                                "relevance_score": 1.0,
                                "matched_terms": [query]
                            })

            return {
                "success": True,
                "results": all_results,
                "total_results": len(all_results)
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def _deduplicate_and_rank_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重和排序结果"""
        # 按文档ID和数据类型去重
        seen = set()
        deduplicated = []

        for result in results:
            key = (result.get("document_id", ""), result.get("data_type", ""))
            if key not in seen:
                seen.add(key)
                deduplicated.append(result)

        # 按相关性分数排序
        deduplicated.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

        return deduplicated

    def _generate_search_summary(self, search_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成搜索结果摘要"""
        combined_results = search_results.get("combined_results", [])

        summary = {
            "total_results": len(combined_results),
            "data_type_distribution": {},
            "search_method_distribution": {},
            "average_relevance": 0.0,
            "top_results": []
        }

        if combined_results:
            # 数据类型分布
            data_type_counts = {}
            method_counts = {}
            total_relevance = 0

            for result in combined_results:
                data_type = result.get("data_type", "unknown")
                method = result.get("search_method", "unknown")
                relevance = result.get("relevance_score", 0)

                data_type_counts[data_type] = data_type_counts.get(data_type, 0) + 1
                method_counts[method] = method_counts.get(method, 0) + 1
                total_relevance += relevance

            summary["data_type_distribution"] = data_type_counts
            summary["search_method_distribution"] = method_counts
            summary["average_relevance"] = total_relevance / len(combined_results)

            # 前5个最相关的结果
            summary["top_results"] = combined_results[:5]

        return summary

    # ==================== 数据导出方法 ====================

    def export_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """导出数据"""
        try:
            # 验证必要参数
            self._validate_required_fields(data, ['data_type', 'export_format'])
            data_type = data["data_type"]
            export_format = data["export_format"]

            # 获取数据
            export_data = []
            if data_type == "characters":
                result = self.search_characters(data.get("filters", {}))
                if result.get("success"):
                    export_data = result.get("characters", [])
            elif data_type == "relationships":
                result = self.search_relationships(data.get("filters", {}))
                if result.get("success"):
                    export_data = result.get("relationships", [])
            elif data_type == "events":
                result = self.search_events(data.get("filters", {}))
                if result.get("success"):
                    export_data = result.get("events", [])
            elif data_type == "world_settings":
                result = self.search_world_settings(data.get("filters", {}))
                if result.get("success"):
                    export_data = result.get("world_settings", [])
            elif data_type == "notes":
                result = self.search_notes(data.get("filters", {}))
                if result.get("success"):
                    export_data = result.get("notes", [])
            else:
                return {
                    "success": False,
                    "error": f"Unsupported data type: {data_type}"
                }

            # 执行导出
            export_result = self.export_manager.export_data(
                data=export_data,
                data_type=data_type,
                export_format=export_format,
                template=data.get("template"),
                custom_fields=data.get("custom_fields"),
                sort_by=data.get("sort_by"),
                filter_conditions=data.get("filter_conditions")
            )

            return export_result

        except Exception as e:
            return self._handle_error("export_data", e)

    def export_all_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """导出所有数据"""
        try:
            # 验证必要参数
            self._validate_required_fields(data, ['export_format'])
            export_format = data["export_format"]

            all_exports = {}
            data_types = ["characters", "relationships", "events", "world_settings", "notes"]

            for data_type in data_types:
                export_result = self.export_data({
                    "data_type": data_type,
                    "export_format": export_format,
                    "template": data.get("template"),
                    "custom_fields": data.get("custom_fields"),
                    "sort_by": data.get("sort_by")
                })

                if export_result.get("success"):
                    all_exports[data_type] = export_result
                else:
                    all_exports[data_type] = {
                        "success": False,
                        "error": export_result.get("error", "Unknown error")
                    }

            # 统计成功和失败的导出
            successful_exports = sum(1 for result in all_exports.values() if result.get("success"))
            total_exports = len(all_exports)

            return {
                "success": successful_exports > 0,
                "export_format": export_format,
                "total_data_types": total_exports,
                "successful_exports": successful_exports,
                "failed_exports": total_exports - successful_exports,
                "exports": all_exports,
                "message": f"Exported {successful_exports}/{total_exports} data types successfully"
            }

        except Exception as e:
            return self._handle_error("export_all_data", e)

    def get_export_templates(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """获取可用的导出模板"""
        try:
            templates = self.export_manager.get_available_templates()

            return {
                "success": True,
                "templates": templates,
                "template_count": len(templates),
                "message": f"Found {len(templates)} available export templates"
            }

        except Exception as e:
            return self._handle_error("get_export_templates", e)

    def get_export_formats(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """获取支持的导出格式"""
        try:
            formats = self.export_manager.get_supported_formats()

            return {
                "success": True,
                "formats": formats,
                "format_count": len(formats),
                "message": f"Found {len(formats)} supported export formats"
            }

        except Exception as e:
            return self._handle_error("get_export_formats", e)

    def create_story_bible(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建故事圣经（完整导出）"""
        try:
            export_format = data.get("export_format", "markdown")

            # 获取所有数据
            story_bible = {
                "metadata": {
                    "title": data.get("title", "Story Bible"),
                    "author": data.get("author", "Unknown"),
                    "created_at": datetime.now().isoformat(),
                    "export_format": export_format
                },
                "sections": {}
            }

            # 导出各个部分
            data_types = ["characters", "relationships", "events", "world_settings", "notes"]

            for data_type in data_types:
                export_result = self.export_data({
                    "data_type": data_type,
                    "export_format": export_format,
                    "template": "story_bible",
                    "sort_by": data.get("sort_by")
                })

                if export_result.get("success"):
                    story_bible["sections"][data_type] = {
                        "title": data_type.replace("_", " ").title(),
                        "record_count": export_result.get("record_count", 0),
                        "content": export_result.get("export_data", "")
                    }

            # 生成完整的故事圣经文档
            if export_format == "markdown":
                bible_content = self._generate_story_bible_markdown(story_bible)
            elif export_format == "html":
                bible_content = self._generate_story_bible_html(story_bible)
            else:
                bible_content = json.dumps(story_bible, ensure_ascii=False, indent=2)

            return {
                "success": True,
                "story_bible": bible_content,
                "metadata": story_bible["metadata"],
                "sections": list(story_bible["sections"].keys()),
                "total_records": sum(section.get("record_count", 0) for section in story_bible["sections"].values()),
                "message": "Story bible created successfully"
            }

        except Exception as e:
            return self._handle_error("create_story_bible", e)

    def _generate_story_bible_markdown(self, story_bible: Dict[str, Any]) -> str:
        """生成Markdown格式的故事圣经"""
        lines = []
        metadata = story_bible["metadata"]

        # 标题页
        lines.append(f"# {metadata['title']}")
        lines.append("")
        lines.append(f"**Author:** {metadata['author']}")
        lines.append(f"**Created:** {metadata['created_at']}")
        lines.append("")
        lines.append("---")
        lines.append("")

        # 目录
        lines.append("## Table of Contents")
        lines.append("")
        for section_name, section_data in story_bible["sections"].items():
            section_title = section_data["title"]
            record_count = section_data["record_count"]
            lines.append(f"- [{section_title}](#{section_name.replace('_', '-')}) ({record_count} items)")
        lines.append("")
        lines.append("---")
        lines.append("")

        # 各个章节
        for section_name, section_data in story_bible["sections"].items():
            lines.append(f"# {section_data['title']}")
            lines.append("")
            lines.append(section_data["content"])
            lines.append("")
            lines.append("---")
            lines.append("")

        return "\n".join(lines)

    def _generate_story_bible_html(self, story_bible: Dict[str, Any]) -> str:
        """生成HTML格式的故事圣经"""
        metadata = story_bible["metadata"]

        html_parts = []
        html_parts.append("<!DOCTYPE html>")
        html_parts.append("<html lang='zh-CN'>")
        html_parts.append("<head>")
        html_parts.append("    <meta charset='UTF-8'>")
        html_parts.append("    <meta name='viewport' content='width=device-width, initial-scale=1.0'>")
        html_parts.append(f"    <title>{metadata['title']}</title>")
        html_parts.append("    <style>")
        html_parts.append("""
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; line-height: 1.6; }
        h1 { color: #333; border-bottom: 3px solid #4CAF50; padding-bottom: 10px; }
        h2 { color: #555; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
        .toc { background-color: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .toc ul { list-style-type: none; padding-left: 0; }
        .toc li { margin: 5px 0; }
        .section { margin: 40px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        """)
        html_parts.append("    </style>")
        html_parts.append("</head>")
        html_parts.append("<body>")

        # 标题页
        html_parts.append(f"    <h1>{metadata['title']}</h1>")
        html_parts.append(f"    <p><strong>Author:</strong> {metadata['author']}</p>")
        html_parts.append(f"    <p><strong>Created:</strong> {metadata['created_at']}</p>")

        # 目录
        html_parts.append("    <div class='toc'>")
        html_parts.append("        <h2>Table of Contents</h2>")
        html_parts.append("        <ul>")
        for section_name, section_data in story_bible["sections"].items():
            section_title = section_data["title"]
            record_count = section_data["record_count"]
            html_parts.append(f"            <li><a href='#{section_name}'>{section_title}</a> ({record_count} items)</li>")
        html_parts.append("        </ul>")
        html_parts.append("    </div>")

        # 各个章节
        for section_name, section_data in story_bible["sections"].items():
            html_parts.append(f"    <div class='section' id='{section_name}'>")
            html_parts.append(f"        <h2>{section_data['title']}</h2>")
            html_parts.append(f"        <div>{section_data['content']}</div>")
            html_parts.append("    </div>")

        html_parts.append("</body>")
        html_parts.append("</html>")

        return "\n".join(html_parts)

    # ==================== 一致性检查方法 ====================

    def check_consistency(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """执行一致性检查"""
        try:
            check_types = data.get("check_types")

            # 运行一致性检查
            result = self.consistency_manager.run_consistency_check(check_types)

            return result

        except Exception as e:
            return self._handle_error("check_consistency", e)

    def get_consistency_report(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """获取详细的一致性报告"""
        try:
            check_types = data.get("check_types")

            # 生成一致性报告
            result = self.consistency_manager.get_consistency_report(check_types)

            return result

        except Exception as e:
            return self._handle_error("get_consistency_report", e)

    def fix_consistency_issue(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """修复特定的一致性问题"""
        try:
            # 验证必要参数
            self._validate_required_fields(data, ['issue_type', 'issue_details'])
            issue_type = data["issue_type"]
            issue_details = data["issue_details"]

            # 根据问题类型执行相应的修复操作
            if issue_type == "character_name_duplicate":
                return self._fix_duplicate_character_names(issue_details)
            elif issue_type == "relationship_conflict":
                return self._fix_relationship_conflicts(issue_details)
            elif issue_type == "timeline_logic_error":
                return self._fix_timeline_errors(issue_details)
            elif issue_type == "world_rule_violation":
                return self._fix_world_rule_violations(issue_details)
            else:
                return {
                    "success": False,
                    "error": f"Unsupported issue type for automatic fixing: {issue_type}",
                    "suggestion": "This issue type requires manual review and fixing"
                }

        except Exception as e:
            return self._handle_error("fix_consistency_issue", e)

    def get_consistency_suggestions(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """获取一致性改进建议"""
        try:
            # 运行快速一致性检查
            check_result = self.consistency_manager.run_consistency_check()

            if not check_result.get('success'):
                return check_result

            suggestions = {
                "success": True,
                "consistency_score": check_result.get('overall_stats', {}).get('consistency_score', 100),
                "improvement_areas": [],
                "quick_fixes": [],
                "preventive_measures": []
            }

            # 基于检查结果生成建议
            issues = check_result.get('issues', [])
            severity_dist = check_result.get('overall_stats', {}).get('severity_distribution', {})

            # 改进领域
            if severity_dist.get('critical', 0) > 0:
                suggestions["improvement_areas"].append({
                    "area": "Critical Issues",
                    "priority": "High",
                    "description": f"{severity_dist['critical']} critical issues need immediate attention",
                    "impact": "High - Affects story coherence"
                })

            if severity_dist.get('major', 0) > 0:
                suggestions["improvement_areas"].append({
                    "area": "Major Inconsistencies",
                    "priority": "Medium",
                    "description": f"{severity_dist['major']} major issues should be addressed",
                    "impact": "Medium - Affects story quality"
                })

            # 快速修复建议
            issue_types = Counter(issue.get('type', 'unknown') for issue in issues)

            if issue_types.get('character_name_duplicate', 0) > 0:
                suggestions["quick_fixes"].append({
                    "fix": "Rename Duplicate Characters",
                    "description": "Use unique names or merge duplicate character entries",
                    "estimated_time": "15-30 minutes",
                    "difficulty": "Easy"
                })

            if issue_types.get('relationship_asymmetric', 0) > 0:
                suggestions["quick_fixes"].append({
                    "fix": "Add Reciprocal Relationships",
                    "description": "Add missing reciprocal relationships for symmetric relationship types",
                    "estimated_time": "10-20 minutes",
                    "difficulty": "Easy"
                })

            # 预防措施
            suggestions["preventive_measures"] = [
                {
                    "measure": "Regular Consistency Checks",
                    "description": "Run consistency checks weekly during active writing",
                    "benefit": "Early detection of issues"
                },
                {
                    "measure": "Character Name Registry",
                    "description": "Maintain a list of all character names to avoid duplicates",
                    "benefit": "Prevents naming conflicts"
                },
                {
                    "measure": "Timeline Documentation",
                    "description": "Keep detailed timeline notes for complex plots",
                    "benefit": "Maintains chronological consistency"
                },
                {
                    "measure": "World Building Bible",
                    "description": "Document all world rules and settings in detail",
                    "benefit": "Ensures consistent world logic"
                }
            ]

            return suggestions

        except Exception as e:
            return self._handle_error("get_consistency_suggestions", e)

    def _fix_duplicate_character_names(self, issue_details: Dict[str, Any]) -> Dict[str, Any]:
        """修复重复的人物姓名"""
        try:
            affected_items = issue_details.get('affected_items', [])

            if not affected_items:
                return {
                    "success": False,
                    "error": "No affected items specified for duplicate name fix"
                }

            # 这里应该实现具体的修复逻辑
            # 由于涉及数据修改，这里只返回修复建议
            return {
                "success": True,
                "fix_type": "character_name_duplicate",
                "affected_characters": affected_items,
                "suggested_actions": [
                    f"Rename character to make it unique",
                    f"Add distinguishing suffix (e.g., 'Jr.', 'II')",
                    f"Use nickname or alias for one character",
                    f"Merge characters if they represent the same person"
                ],
                "manual_action_required": True,
                "message": "Duplicate character names detected. Manual review and renaming required."
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def _fix_relationship_conflicts(self, issue_details: Dict[str, Any]) -> Dict[str, Any]:
        """修复关系冲突"""
        try:
            affected_items = issue_details.get('affected_items', [])

            return {
                "success": True,
                "fix_type": "relationship_conflict",
                "affected_relationships": affected_items,
                "suggested_actions": [
                    "Review conflicting relationship types",
                    "Consider relationship evolution over time",
                    "Add temporal context to relationships",
                    "Clarify complex relationships with detailed descriptions"
                ],
                "manual_action_required": True,
                "message": "Relationship conflicts detected. Manual review and clarification required."
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def _fix_timeline_errors(self, issue_details: Dict[str, Any]) -> Dict[str, Any]:
        """修复时间线错误"""
        try:
            affected_items = issue_details.get('affected_items', [])

            return {
                "success": True,
                "fix_type": "timeline_logic_error",
                "affected_events": affected_items,
                "suggested_actions": [
                    "Review event chronology",
                    "Check character participation in events",
                    "Verify causal relationships",
                    "Update event timestamps if necessary"
                ],
                "manual_action_required": True,
                "message": "Timeline logic errors detected. Manual review and correction required."
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def _fix_world_rule_violations(self, issue_details: Dict[str, Any]) -> Dict[str, Any]:
        """修复世界观规则违反"""
        try:
            affected_items = issue_details.get('affected_items', [])

            return {
                "success": True,
                "fix_type": "world_rule_violation",
                "affected_items": affected_items,
                "suggested_actions": [
                    "Review event against world setting rules",
                    "Modify event to comply with world rules",
                    "Update world rules if they need to be changed",
                    "Add exceptions or special circumstances"
                ],
                "manual_action_required": True,
                "message": "World rule violations detected. Manual review and resolution required."
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
