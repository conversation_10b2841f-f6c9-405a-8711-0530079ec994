"""
AI小说管理工具 - 配置文件
为作家创作过程提供信息管理和决策支持的本地工具
"""

import os
from pathlib import Path
from typing import Dict, Any

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
DB_DIR = DATA_DIR / "databases"
BACKUP_DIR = DATA_DIR / "backups"
LOGS_DIR = DATA_DIR / "logs"

# 确保目录存在
for directory in [DATA_DIR, DB_DIR, BACKUP_DIR, LOGS_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# 数据库配置
DATABASE_CONFIG = {
    "main_db": str(DB_DIR / "novel_management.db"),
    "search_index": str(DATA_DIR / "search_index"),
    "backup_interval": 3600,  # 备份间隔（秒）
    "max_backups": 10,  # 最大备份数量
}

# 查询配置
QUERY_CONFIG = {
    "max_results": 100,  # 默认最大查询结果数
    "search_timeout": 30,  # 搜索超时时间（秒）
    "cache_size": 1000,  # 查询缓存大小
    "enable_fuzzy_search": True,  # 启用模糊搜索
}

# AI Agent接口配置
API_CONFIG = {
    "enable_rest_api": False,  # 是否启用REST API
    "api_host": "localhost",
    "api_port": 8000,
    "enable_cors": True,
    "max_request_size": 10 * 1024 * 1024,  # 10MB
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": str(LOGS_DIR / "novel_management.log"),
    "max_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5,
}

# 数据验证配置
VALIDATION_CONFIG = {
    "max_name_length": 200,
    "max_description_length": 5000,
    "max_content_length": 50000,
    "min_relationship_strength": 0.0,
    "max_relationship_strength": 1.0,
    "max_importance_level": 10,
}

# 搜索配置
SEARCH_CONFIG = {
    "min_query_length": 2,
    "max_query_length": 500,
    "default_context_lines": 3,
    "highlight_results": True,
}

# 导出配置
EXPORT_CONFIG = {
    "formats": ["json", "csv", "markdown"],
    "include_metadata": True,
    "compress_exports": True,
}

def get_config(section: str = None) -> Dict[str, Any]:
    """获取配置信息"""
    configs = {
        "database": DATABASE_CONFIG,
        "query": QUERY_CONFIG,
        "api": API_CONFIG,
        "logging": LOGGING_CONFIG,
        "validation": VALIDATION_CONFIG,
        "search": SEARCH_CONFIG,
        "export": EXPORT_CONFIG,
    }
    
    if section:
        return configs.get(section, {})
    return configs

def update_config(section: str, key: str, value: Any) -> bool:
    """更新配置项"""
    try:
        config_map = {
            "database": DATABASE_CONFIG,
            "query": QUERY_CONFIG,
            "api": API_CONFIG,
            "logging": LOGGING_CONFIG,
            "validation": VALIDATION_CONFIG,
            "search": SEARCH_CONFIG,
            "export": EXPORT_CONFIG,
        }
        
        if section in config_map and key in config_map[section]:
            config_map[section][key] = value
            return True
        return False
    except Exception:
        return False
