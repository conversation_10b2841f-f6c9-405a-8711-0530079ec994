from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import create_engine, Column, String, Integer, Float, Text, DateTime, JSON, ForeignKey, Table
from sqlalchemy.orm import declarative_base, relationship, sessionmaker, Session

# 数据库设置
engine = create_engine('sqlite:///novel_characters.db', echo=True)
Base = declarative_base()
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 关联表：人物-事件多对多关系
character_event = Table(
    'character_event',
    Base.metadata,
    Column('character_id', String, ForeignKey('characters.id')),
    Column('event_id', String, ForeignKey('events.id'))
)


class Character(Base):
    __tablename__ = 'characters'

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    prototype = Column(String)  # 角色原型
    narrative_role = Column(String)  # 故事角色

    # 使用JSON字段存储复杂结构
    core_identity = Column(JSON)  # 核心身份
    internal_dimension = Column(JSON)  # 内在维度
    external_dimension = Column(JSON)  # 外在表现
    tags = Column(JSON)  # 标签

    # 关系
    milestones = relationship("Milestone", back_populates="character")
    relationships_as_source = relationship(
        "CharacterRelationship",
        foreign_keys="CharacterRelationship.source_character_id",
        back_populates="source_character"
    )
    relationships_as_target = relationship(
        "CharacterRelationship",
        foreign_keys="CharacterRelationship.target_character_id",
        back_populates="target_character"
    )
    events = relationship("Event", secondary=character_event, back_populates="characters")
    event_roles = relationship("CharacterEventRole", back_populates="character")

    def __repr__(self):
        return f"<Character(id={self.id}, name={self.name})>"


class Event(Base):
    __tablename__ = 'events'

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    timestamp = Column(DateTime)
    location = Column(String)
    tags = Column(JSON)

    # 关系
    milestones = relationship("Milestone", back_populates="event")
    characters = relationship("Character", secondary=character_event, back_populates="events")
    character_roles = relationship("CharacterEventRole", back_populates="event")

    def __repr__(self):
        return f"<Event(id={self.id}, name={self.name})>"


class Milestone(Base):
    __tablename__ = 'milestones'

    id = Column(String, primary_key=True)
    character_id = Column(String, ForeignKey('characters.id'))
    event_id = Column(String, ForeignKey('events.id'))
    description = Column(Text)
    psychological_change = Column(JSON)  # 心理状态变化
    values_change = Column(JSON)  # 价值观变化
    goals_change = Column(JSON)  # 目标变化
    proof = Column(Text)  # 证明变化的剧情线索

    # 关系
    character = relationship("Character", back_populates="milestones")
    event = relationship("Event", back_populates="milestones")

    def __repr__(self):
        return f"<Milestone(id={self.id}, character_id={self.character_id}, event_id={self.event_id})>"


class CharacterRelationship(Base):
    __tablename__ = 'character_relationships'

    id = Column(String, primary_key=True)
    source_character_id = Column(String, ForeignKey('characters.id'))
    target_character_id = Column(String, ForeignKey('characters.id'))
    type = Column(String)  # 关系类型
    strength = Column(Integer)  # 关系强度 (1-10)
    status = Column(String)  # 关系状态
    description = Column(Text)  # 关系描述

    # 关系
    source_character = relationship("Character", foreign_keys=[source_character_id],
                                    back_populates="relationships_as_source")
    target_character = relationship("Character", foreign_keys=[target_character_id],
                                    back_populates="relationships_as_target")

    def __repr__(self):
        return f"<CharacterRelationship(id={self.id}, source={self.source_character_id}, target={self.target_character_id}, type={self.type})>"


class CharacterEventRole(Base):
    __tablename__ = 'character_event_roles'

    id = Column(String, primary_key=True)
    character_id = Column(String, ForeignKey('characters.id'))
    event_id = Column(String, ForeignKey('events.id'))
    role = Column(String)  # 在事件中的角色
    impact_description = Column(Text)  # 影响描述

    # 关系
    character = relationship("Character", back_populates="event_roles")
    event = relationship("Event", back_populates="character_roles")

    def __repr__(self):
        return f"<CharacterEventRole(id={self.id}, character_id={self.character_id}, event_id={self.event_id}, role={self.role})>"


# 数据库管理类
class NovelManagementSystem:
    def __init__(self):
        self.engine = engine
        Base.metadata.create_all(self.engine)
        self.SessionLocal = SessionLocal

    def get_db(self):
        db = self.SessionLocal()
        try:
            yield db
        finally:
            db.close()

    # 人物CRUD操作
    def create_character(self, db: Session, character_data: Dict[str, Any]):
        character = Character(**character_data)
        db.add(character)
        db.commit()
        db.refresh(character)
        return character

    def get_character(self, db: Session, character_id: str):
        return db.query(Character).filter(Character.id == character_id).first()

    def get_all_characters(self, db: Session):
        return db.query(Character).all()

    def update_character(self, db: Session, character_id: str, update_data: Dict[str, Any]):
        db.query(Character).filter(Character.id == character_id).update(update_data)
        db.commit()
        return self.get_character(db, character_id)

    def delete_character(self, db: Session, character_id: str):
        character = self.get_character(db, character_id)
        if character:
            db.delete(character)
            db.commit()
            return True
        return False

    # 事件CRUD操作
    def create_event(self, db: Session, event_data: Dict[str, Any]):
        event = Event(**event_data)
        db.add(event)
        db.commit()
        db.refresh(event)
        return event

    def get_event(self, db: Session, event_id: str):
        return db.query(Event).filter(Event.id == event_id).first()

    # 类似地实现其他实体的CRUD操作...

    # 添加人物到事件
    def add_character_to_event(self, db: Session, character_id: str, event_id: str, role_data: Dict[str, Any]):
        character = self.get_character(db, character_id)
        event = self.get_event(db, event_id)

        if character and event:
            # 添加关联
            event.characters.append(character)

            # 创建角色记录
            role_data.update({
                "character_id": character_id,
                "event_id": event_id
            })
            role = CharacterEventRole(**role_data)
            db.add(role)

            db.commit()
            return role
        return None

    # 创建里程碑
    def create_milestone(self, db: Session, milestone_data: Dict[str, Any]):
        milestone = Milestone(**milestone_data)
        db.add(milestone)
        db.commit()
        db.refresh(milestone)
        return milestone

    # 创建人物关系
    def create_relationship(self, db: Session, relationship_data: Dict[str, Any]):
        relationship = CharacterRelationship(**relationship_data)
        db.add(relationship)
        db.commit()
        db.refresh(relationship)
        return relationship


# 示例数据初始化
def init_sample_data(system: NovelManagementSystem):
    db = next(system.get_db())

    try:
        # 创建示例人物
        protagonist = system.create_character(db, {
            "id": "char_001",
            "name": "李风",
            "prototype": "英雄",
            "narrative_role": "主角",
            "core_identity": {
                "aliases": ["风之子"],
                "basic_info": {
                    "gender": "男",
                    "age": 22,
                    "nationality": "大唐",
                    "race": "人族"
                },
                "background": "出身贫寒，自幼父母双亡，被剑术大师收养"
            },
            "internal_dimension": {
                "personality": ["勇敢", "正义", "冲动"],
                "personality_description": "天性善良，但有时过于冲动",
                "core_values": ["正义", "自由"],
                "motivations": ["为养父报仇", "保护弱小"],
                "fears_secrets": ["害怕失去重要的人", "隐藏着自己的真实身世"]
            },
            "external_dimension": {
                "appearance": "黑色短发，身材修长，常穿青色长衫",
                "behavior": "行动敏捷，剑不离身",
                "skills_abilities": ["高级剑术", "轻功", "内力修炼"]
            },
            "tags": ["核心角色", "成长型主角"]
        })

        mentor = system.create_character(db, {
            "id": "char_002",
            "name": "白大师",
            "prototype": "导师",
            "narrative_role": "配角",
            "core_identity": {
                "basic_info": {
                    "gender": "男",
                    "age": 65,
                    "nationality": "大唐",
                    "race": "人族"
                },
                "background": "前朝剑术宗师，隐居于山林"
            },
            "internal_dimension": {
                "personality": ["智慧", "耐心", "严肃"],
                "core_values": ["知识", "平衡"],
                "motivations": ["传授剑术", "保护传统"]
            },
            "external_dimension": {
                "appearance": "白发长须，目光如炬，常穿白色道袍",
                "skills_abilities": ["宗师级剑术", "内功大师", "兵法策略"]
            },
            "tags": ["导师角色", "重要配角"]
        })

        # 创建示例事件
        attack_event = system.create_event(db, {
            "id": "event_001",
            "name": "山林遇袭",
            "description": "李风在山林中遭遇神秘刺客袭击",
            "timestamp": datetime(2023, 5, 15, 14, 30),
            "location": "青云山",
            "tags": ["战斗", "转折点", "第一幕"]
        })

        # 添加人物到事件
        system.add_character_to_event(db, "char_001", "event_001", {
            "id": "role_001",
            "role": "受害者",
            "impact_description": "身受重伤，但侥幸逃生"
        })

        # 创建人物关系
        system.create_relationship(db, {
            "id": "rel_001",
            "source_character_id": "char_001",
            "target_character_id": "char_002",
            "type": "师徒",
            "strength": 9,
            "status": "稳固",
            "description": "白大师是李风的剑术导师，对他寄予厚望"
        })

        # 创建里程碑
        system.create_milestone(db, {
            "id": "mile_001",
            "character_id": "char_001",
            "event_id": "event_001",
            "description": "首次遭遇生死危机，意识到自身不足",
            "psychological_change": {
                "before": "自信过度，有些轻敌",
                "after": "更加谨慎，意识到需要更刻苦训练"
            },
            "values_change": {
                "before": ["正义", "自由"],
                "after": ["正义", "自由", "责任"]
            },
            "goals_change": {
                "before": ["为养父报仇"],
                "after": ["为养父报仇", "提升剑术实力", "查明刺客身份"]
            },
            "proof": "在后续剧情中，李风每天增加练剑时间，并向白大师请教更多实战技巧"
        })

        db.commit()
        print("示例数据初始化完成")

    except Exception as e:
        db.rollback()
        print(f"初始化数据时出错: {e}")
    finally:
        db.close()


# 查询示例
def query_examples(system: NovelManagementSystem):
    db = next(system.get_db())

    try:
        # 查询所有人物
        characters = system.get_all_characters(db)
        print("所有人物:")
        for char in characters:
            print(f"  - {char.name} ({char.narrative_role})")

        # 查询特定人物
        li_feng = system.get_character(db, "char_001")
        print(f"\n李风的详细信息:")
        print(f"  核心价值观: {li_feng.internal_dimension['core_values']}")
        print(f"  技能: {li_feng.external_dimension['skills_abilities']}")

        # 查询人物关系
        relationships = db.query(CharacterRelationship).filter(
            CharacterRelationship.source_character_id == "char_001"
        ).all()

        print(f"\n李风的关系:")
        for rel in relationships:
            target = system.get_character(db, rel.target_character_id)
            print(f"  - 与{target.name}的关系: {rel.type} (强度: {rel.strength}/10)")

        # 查询事件及其参与者
        event = system.get_event(db, "event_001")
        print(f"\n事件'{event.name}'的参与者:")
        for char in event.characters:
            role = db.query(CharacterEventRole).filter(
                CharacterEventRole.character_id == char.id,
                CharacterEventRole.event_id == event.id
            ).first()
            print(f"  - {char.name}: {role.role}")

        # 查询人物的里程碑
        milestones = db.query(Milestone).filter(
            Milestone.character_id == "char_001"
        ).all()

        print(f"\n李风的里程碑:")
        for milestone in milestones:
            event = system.get_event(db, milestone.event_id)
            print(f"  - 事件: {event.name}")
            print(f"    变化: {milestone.description}")

    finally:
        db.close()


# 主程序
if __name__ == "__main__":
    # 初始化系统
    novel_system = NovelManagementSystem()

    # 初始化示例数据
    init_sample_data(novel_system)

    # 运行查询示例
    query_examples(novel_system)