#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Graph_v4 API 使用示例
展示如何使用 AI小说管理工具 的各种功能
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    print("=" * 50)
    print("🎭 Graph_v4 API 使用示例")
    print("=" * 50)
    
    try:
        # 导入并创建系统
        from code.Graph_v4 import create_system
        system = create_system()
        print("✅ 系统初始化成功")
        
        # 1. 创建人物
        print("\n📝 1. 创建人物示例")
        result = system.create_character(
            name="测试角色",
            age=25,
            gender="男",
            background="测试背景故事",
            personality_traits=["勇敢", "智慧"]
        )
        print(f"   创建结果: {result.get('success', False)}")
        if result.get('success'):
            char_id = result.get('character_id')
            print(f"   人物ID: {char_id}")
        
        # 2. 查询人物
        print("\n🔍 2. 查询人物示例")
        chars = system.search_characters()
        if chars.get('success'):
            characters = chars.get('characters', [])
            print(f"   找到 {len(characters)} 个人物")
            for char in characters[:3]:  # 只显示前3个
                print(f"   - {char.get('name', 'N/A')} (ID: {char.get('id', 'N/A')[:8]}...)")
        
        # 3. MCP工具列表
        print("\n🔧 3. MCP工具列表")
        from code.Graph_v4.mcp_interface import create_mcp_interface
        mcp = create_mcp_interface()
        tools = mcp.get_available_tools()
        print(f"   可用工具数量: {len(tools)}")
        print("   主要工具:")
        for i, tool in enumerate(tools[:5]):  # 显示前5个工具
            print(f"   {i+1}. {tool['name']} - {tool['description']}")
        
        # 4. 系统概览
        print("\n📊 4. 系统概览")
        overview = system.get_overview()
        if overview.get('success'):
            print("   系统状态: 正常")
            stats = overview.get('statistics', {})
            print(f"   人物数量: {stats.get('characters_count', 0)}")
            print(f"   关系数量: {stats.get('relationships_count', 0)}")
        
        # 5. 智能查询示例
        print("\n🤖 5. 智能查询示例")
        query_result = system.query("有哪些人物？")
        if query_result.get('success'):
            print("   查询成功")
            print(f"   查询结果: {query_result.get('response', 'N/A')[:100]}...")
        
        print("\n" + "=" * 50)
        print("🎉 API 示例演示完成！")
        print("💡 您可以使用这些API来构建自己的小说管理应用")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
