#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Graph_v4 可行性测试脚本
参照豆包模型代码结构，测试 AI小说管理工具 的基本功能
"""

import sys
import os
import json
from datetime import datetime
from pathlib import Path

# 添加项目路径到 Python 路径
# project_root = Path(__file__).parent
# sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试核心导入
        from code.Graph_v4 import create_system, NovelManagementSystem
        print("✅ 核心模块导入成功")
        
        # 测试MCP接口导入
        from code.Graph_v4.mcp_interface import create_mcp_interface
        print("✅ MCP接口导入成功")
        
        # 测试数据模型导入
        from code.Graph_v4.core.models import Character, Relationship, Event
        print("✅ 数据模型导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_system_initialization():
    """测试系统初始化"""
    print("\n🚀 测试系统初始化...")
    
    try:
        from code.Graph_v4 import create_system
        
        # 创建系统实例（类似豆包模型的客户端初始化）
        system = create_system()
        print("✅ 系统初始化成功")
        
        # 测试健康检查
        health = system.health_check()
        print(f"✅ 系统健康检查: {health.get('status', 'unknown')}")
        
        return system
        
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return None

def test_basic_functionality(system):
    """测试基本功能（类似豆包模型的消息处理）"""
    print("\n📝 测试基本功能...")

    try:
        # 测试创建人物（类似发送消息）
        result = system.create_character(
            name="张三",
            age=25,
            gender="男",
            background="来自小镇的年轻人",
            personality_traits=["勇敢", "善良"],
            importance=8
        )
        print(f"✅ 创建人物成功: {result.get('character_id', 'unknown')}")

        # 测试查询功能
        characters = system.search_characters()
        print(f"✅ 查询人物成功: 共 {len(characters.get('characters', []))} 个人物")

        # 测试系统统计
        stats = system.get_statistics()
        print(f"✅ 获取统计信息成功")

        return True

    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_mcp_interface():
    """测试MCP接口（类似豆包模型的API调用）"""
    print("\n🔌 测试MCP接口...")
    
    try:
        from code.Graph_v4.mcp_interface import create_mcp_interface
        
        # 创建MCP接口（类似豆包模型的API客户端）
        mcp = create_mcp_interface()
        print("✅ MCP接口创建成功")
        
        # 获取可用工具（类似豆包模型的模型列表）
        tools = mcp.get_available_tools()
        print(f"✅ 获取可用工具: {len(tools)} 个工具")
        
        # 测试工具调用（类似豆包模型的聊天完成）
        result = mcp.call_tool("get_system_overview", {})
        print(f"✅ 工具调用成功: {result.get('success', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP接口测试失败: {e}")
        return False

def test_advanced_features(system):
    """测试高级功能"""
    print("\n🔬 测试高级功能...")

    try:
        # 创建第二个人物
        result2 = system.create_character(
            name="李四",
            age=30,
            gender="男",
            background="经验丰富的导师",
            personality_traits=["智慧", "严格"],
            importance=7
        )
        char2_id = result2.get('character_id')

        # 获取第一个人物ID（假设之前创建成功）
        characters = system.search_characters()
        if characters.get('success') and characters.get('characters'):
            char1_id = characters['characters'][0]['id']

            # 创建关系
            rel_result = system.create_relationship(
                character_a=char1_id,
                character_b=char2_id,
                relationship_type="mentor",
                description="师父与弟子的关系",
                strength=0.8
            )
            print(f"✅ 创建关系成功: {rel_result.get('relationship_id', 'unknown')}")

            # 创建事件
            event_result = system.create_event(
                title="初次相遇",
                description="张三遇到了他的导师李四",
                event_type="character_development",
                involved_characters=[char1_id, char2_id],
                importance=6
            )
            print(f"✅ 创建事件成功: {event_result.get('event_id', 'unknown')}")

        return True

    except Exception as e:
        print(f"❌ 高级功能测试失败: {e}")
        return False

def main():
    """主测试函数（类似豆包模型的主程序结构）"""
    print("=" * 60)
    print("🧪 Graph_v4 可行性测试")
    print("参照豆包模型代码结构进行功能验证")
    print("=" * 60)
    
    # 测试步骤
    test_results = []
    
    # 1. 测试导入
    import_success = test_imports()
    test_results.append(("模块导入", import_success))
    
    if not import_success:
        print("\n❌ 导入失败，无法继续测试")
        return False
    
    # 2. 测试系统初始化
    system = test_system_initialization()
    test_results.append(("系统初始化", system is not None))
    
    if system is None:
        print("\n❌ 系统初始化失败，无法继续测试")
        return False
    
    # 3. 测试基本功能
    basic_success = test_basic_functionality(system)
    test_results.append(("基本功能", basic_success))
    
    # 4. 测试MCP接口
    mcp_success = test_mcp_interface()
    test_results.append(("MCP接口", mcp_success))
    
    # 5. 测试高级功能
    advanced_success = test_advanced_features(system)
    test_results.append(("高级功能", advanced_success))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if not success:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！Graph_v4 系统可行性验证成功")
        print("💡 系统已准备就绪，可以开始使用")
    else:
        print("⚠️  部分测试失败，请检查相关问题")
        print("💡 建议检查依赖安装和配置设置")
    
    print("=" * 60)
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 测试过程中发生未预期的错误: {e}")
        sys.exit(1)
